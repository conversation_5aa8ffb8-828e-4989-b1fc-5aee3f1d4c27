'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'owner' | 'user' | 'cantieri_user'
  requiresUser?: boolean
  requiresCantiere?: boolean
  redirectTo?: string
}

export default function ProtectedRoute({
  children,
  requiredRole,
  requiresUser = false,
  requiresCantiere = false,
  redirectTo = '/login'
}: ProtectedRouteProps) {
  const { user, cantiere, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkAccess = () => {
      console.log('🛡️ ProtectedRoute: Controllo accesso', {
        isAuthenticated,
        user,
        cantiere,
        requiredRole,
        requiresUser,
        requiresCantiere
      })

      // Se ancora in caricamento, aspetta
      if (isLoading) {
        console.log('⏳ ProtectedRoute: Ancora in caricamento...')
        return
      }

      // Se non autenticato, reindirizza al login
      if (!isAuthenticated) {
        console.log('❌ ProtectedRoute: Non autenticato, reindirizzamento a', redirectTo)
        router.replace(redirectTo)
        return
      }

      // Se richiede specificamente un utente ma abbiamo solo cantiere
      if (requiresUser && !user) {
        console.log('❌ ProtectedRoute: Richiede utente ma abbiamo solo cantiere')
        router.replace('/login')
        return
      }

      // Se richiede specificamente un cantiere ma abbiamo solo utente
      if (requiresCantiere && !cantiere) {
        console.log('❌ ProtectedRoute: Richiede cantiere ma abbiamo solo utente')
        router.replace('/login')
        return
      }

      // Se richiede un ruolo specifico
      if (requiredRole && user) {
        if (user.ruolo !== requiredRole) {
          console.log('❌ ProtectedRoute: Ruolo non autorizzato', {
            required: requiredRole,
            actual: user.ruolo
          })
          
          // Reindirizza alla pagina appropriata per il ruolo dell'utente
          switch (user.ruolo) {
            case 'owner':
              router.replace('/admin')
              break
            case 'user':
              router.replace('/cantieri')
              break
            case 'cantieri_user':
              router.replace('/cavi')
              break
            default:
              router.replace('/login')
          }
          return
        }
      }

      console.log('✅ ProtectedRoute: Accesso autorizzato')
      setIsChecking(false)
    }

    checkAccess()
  }, [isAuthenticated, user, cantiere, isLoading, requiredRole, requiresUser, requiresCantiere, router, redirectTo])

  // Mostra loader durante il controllo
  if (isLoading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-sm text-gray-600">Verifica autenticazione...</p>
        </div>
      </div>
    )
  }

  // Se tutto ok, mostra il contenuto
  return <>{children}</>
}
