{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/utils/securityValidation.ts"], "sourcesContent": ["/**\n * 🔒 SECURITY VALIDATION UTILITIES\n * Validazione robusta per prevenire attacchi di sicurezza\n */\n\n// Caratteri pericolosi da rimuovere/escape\nconst DANGEROUS_CHARS = /[<>\\\"'&\\x00-\\x1f\\x7f-\\x9f]/g\nconst SQL_INJECTION_PATTERNS = /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\\b)/gi\nconst XSS_PATTERNS = /(<script|javascript:|vbscript:|onload|onerror|onclick)/gi\n\n/**\n * Sanitizza input rimuovendo caratteri pericolosi\n */\nexport const sanitizeInput = (input: string): string => {\n  if (typeof input !== 'string') return ''\n  \n  return input\n    .trim()\n    .replace(DANGEROUS_CHARS, '') // Rimuove caratteri pericolosi\n    .replace(/\\s+/g, ' ') // Normalizza spazi\n    .substring(0, 1000) // <PERSON><PERSON> lunghezza massima\n}\n\n/**\n * Valida username con regole sicure\n */\nexport const validateUsername = (username: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(username)\n  \n  if (sanitized.length < 3) {\n    return { isValid: false, error: 'Username deve essere almeno 3 caratteri' }\n  }\n  \n  if (sanitized.length > 20) {\n    return { isValid: false, error: 'Username non può superare 20 caratteri' }\n  }\n  \n  if (!/^[a-zA-Z0-9._-]+$/.test(sanitized)) {\n    return { isValid: false, error: 'Username può contenere solo lettere, numeri, punti, underscore e trattini' }\n  }\n  \n  if (/^[._-]|[._-]$/.test(sanitized)) {\n    return { isValid: false, error: 'Username non può iniziare o finire con caratteri speciali' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida password con regole di sicurezza\n */\nexport const validatePassword = (password: string): { isValid: boolean; error?: string; strength: number } => {\n  if (!password || password.length < 8) {\n    return { isValid: false, error: 'Password deve essere almeno 8 caratteri', strength: 0 }\n  }\n  \n  if (password.length > 128) {\n    return { isValid: false, error: 'Password troppo lunga (max 128 caratteri)', strength: 0 }\n  }\n  \n  let strength = 0\n  \n  // Controlla criteri di sicurezza\n  if (/[a-z]/.test(password)) strength++\n  if (/[A-Z]/.test(password)) strength++\n  if (/[0-9]/.test(password)) strength++\n  if (/[^a-zA-Z0-9]/.test(password)) strength++\n  if (password.length >= 12) strength++\n  \n  if (strength < 3) {\n    return { \n      isValid: false, \n      error: 'Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale',\n      strength \n    }\n  }\n  \n  // Controlla password comuni\n  const commonPasswords = ['password', '123456', 'admin', 'qwerty', 'letmein']\n  if (commonPasswords.some(common => password.toLowerCase().includes(common))) {\n    return { isValid: false, error: 'Password troppo comune', strength }\n  }\n  \n  return { isValid: true, strength }\n}\n\n/**\n * Valida email con controlli di sicurezza\n */\nexport const validateEmail = (email: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(email)\n  \n  if (!sanitized) {\n    return { isValid: false, error: 'Email è obbligatoria' }\n  }\n  \n  if (sanitized.length > 254) {\n    return { isValid: false, error: 'Email troppo lunga' }\n  }\n  \n  // Regex RFC 5322 semplificata ma sicura\n  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/\n  \n  if (!emailRegex.test(sanitized)) {\n    return { isValid: false, error: 'Formato email non valido' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida testo generico con controlli XSS\n */\nexport const validateText = (text: string, maxLength: number = 255): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(text)\n  \n  if (sanitized.length > maxLength) {\n    return { isValid: false, error: `Testo troppo lungo (max ${maxLength} caratteri)` }\n  }\n  \n  // Controlla pattern XSS\n  if (XSS_PATTERNS.test(text)) {\n    return { isValid: false, error: 'Contenuto non consentito rilevato' }\n  }\n  \n  // Controlla pattern SQL injection\n  if (SQL_INJECTION_PATTERNS.test(text)) {\n    return { isValid: false, error: 'Contenuto non consentito rilevato' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida ragione sociale con controlli business\n */\nexport const validateRagioneSociale = (ragioneSociale: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(ragioneSociale)\n  \n  if (!sanitized) {\n    return { isValid: false, error: 'Ragione sociale è obbligatoria' }\n  }\n  \n  if (sanitized.length < 2) {\n    return { isValid: false, error: 'Ragione sociale troppo corta' }\n  }\n  \n  if (sanitized.length > 100) {\n    return { isValid: false, error: 'Ragione sociale troppo lunga (max 100 caratteri)' }\n  }\n  \n  // Solo lettere, numeri, spazi e caratteri business comuni\n  if (!/^[a-zA-Z0-9\\s\\.\\-&']+$/.test(sanitized)) {\n    return { isValid: false, error: 'Ragione sociale contiene caratteri non consentiti' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida codice VAT/Partita IVA\n */\nexport const validateVAT = (vat: string): { isValid: boolean; error?: string } => {\n  if (!vat) return { isValid: true } // VAT è opzionale\n  \n  const sanitized = sanitizeInput(vat).replace(/\\s/g, '') // Rimuove spazi\n  \n  if (sanitized.length < 8 || sanitized.length > 15) {\n    return { isValid: false, error: 'VAT deve essere tra 8 e 15 caratteri' }\n  }\n  \n  // Solo numeri e lettere per VAT internazionali\n  if (!/^[A-Z0-9]+$/i.test(sanitized)) {\n    return { isValid: false, error: 'VAT può contenere solo lettere e numeri' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Rate limiting semplice (in-memory)\n */\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>()\n\nexport const checkRateLimit = (key: string, maxAttempts: number, windowMs: number): boolean => {\n  const now = Date.now()\n  const record = rateLimitStore.get(key)\n  \n  if (!record || now > record.resetTime) {\n    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })\n    return true\n  }\n  \n  if (record.count >= maxAttempts) {\n    return false\n  }\n  \n  record.count++\n  return true\n}\n\n/**\n * Genera token CSRF sicuro\n */\nexport const generateCSRFToken = (): string => {\n  const array = new Uint8Array(32)\n  crypto.getRandomValues(array)\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')\n}\n\n/**\n * Valida form completo utente\n */\nexport const validateUserForm = (formData: {\n  username: string\n  password?: string\n  ragione_sociale: string\n  email?: string\n  vat?: string\n  indirizzo?: string\n  nazione?: string\n  referente_aziendale?: string\n}): { isValid: boolean; errors: Record<string, string> } => {\n  const errors: Record<string, string> = {}\n  \n  // Valida username\n  const usernameValidation = validateUsername(formData.username)\n  if (!usernameValidation.isValid) {\n    errors.username = usernameValidation.error!\n  }\n  \n  // Valida password (se presente)\n  if (formData.password) {\n    const passwordValidation = validatePassword(formData.password)\n    if (!passwordValidation.isValid) {\n      errors.password = passwordValidation.error!\n    }\n  }\n  \n  // Valida ragione sociale\n  const ragioneSocialeValidation = validateRagioneSociale(formData.ragione_sociale)\n  if (!ragioneSocialeValidation.isValid) {\n    errors.ragione_sociale = ragioneSocialeValidation.error!\n  }\n  \n  // Valida email (se presente)\n  if (formData.email) {\n    const emailValidation = validateEmail(formData.email)\n    if (!emailValidation.isValid) {\n      errors.email = emailValidation.error!\n    }\n  }\n  \n  // Valida VAT (se presente)\n  if (formData.vat) {\n    const vatValidation = validateVAT(formData.vat)\n    if (!vatValidation.isValid) {\n      errors.vat = vatValidation.error!\n    }\n  }\n  \n  // Valida campi testo opzionali\n  if (formData.indirizzo) {\n    const indirizzoValidation = validateText(formData.indirizzo, 200)\n    if (!indirizzoValidation.isValid) {\n      errors.indirizzo = indirizzoValidation.error!\n    }\n  }\n  \n  if (formData.nazione) {\n    const nazioneValidation = validateText(formData.nazione, 50)\n    if (!nazioneValidation.isValid) {\n      errors.nazione = nazioneValidation.error!\n    }\n  }\n  \n  if (formData.referente_aziendale) {\n    const referenteValidation = validateText(formData.referente_aziendale, 100)\n    if (!referenteValidation.isValid) {\n      errors.referente_aziendale = referenteValidation.error!\n    }\n  }\n  \n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2CAA2C;;;;;;;;;;;;;AAC3C,MAAM,kBAAkB;AACxB,MAAM,yBAAyB;AAC/B,MAAM,eAAe;AAKd,MAAM,gBAAgB,CAAC;IAC5B,IAAI,OAAO,UAAU,UAAU,OAAO;IAEtC,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,iBAAiB,IAAI,+BAA+B;KAC5D,OAAO,CAAC,QAAQ,KAAK,mBAAmB;KACxC,SAAS,CAAC,GAAG,MAAM,2BAA2B;;AACnD;AAKO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,YAAY,cAAc;IAEhC,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI,UAAU,MAAM,GAAG,IAAI;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyC;IAC3E;IAEA,IAAI,CAAC,oBAAoB,IAAI,CAAC,YAAY;QACxC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4E;IAC9G;IAEA,IAAI,gBAAgB,IAAI,CAAC,YAAY;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4D;IAC9F;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,YAAY,SAAS,MAAM,GAAG,GAAG;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO;YAA2C,UAAU;QAAE;IACzF;IAEA,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;YAA6C,UAAU;QAAE;IAC3F;IAEA,IAAI,WAAW;IAEf,iCAAiC;IACjC,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,eAAe,IAAI,CAAC,WAAW;IACnC,IAAI,SAAS,MAAM,IAAI,IAAI;IAE3B,IAAI,WAAW,GAAG;QAChB,OAAO;YACL,SAAS;YACT,OAAO;YACP;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB;QAAC;QAAY;QAAU;QAAS;QAAU;KAAU;IAC5E,IAAI,gBAAgB,IAAI,CAAC,CAAA,SAAU,SAAS,WAAW,GAAG,QAAQ,CAAC,UAAU;QAC3E,OAAO;YAAE,SAAS;YAAO,OAAO;YAA0B;QAAS;IACrE;IAEA,OAAO;QAAE,SAAS;QAAM;IAAS;AACnC;AAKO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,YAAY,cAAc;IAEhC,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuB;IACzD;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,wCAAwC;IACxC,MAAM,aAAa;IAEnB,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO;QAA2B;IAC7D;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,eAAe,CAAC,MAAc,YAAoB,GAAG;IAChE,MAAM,YAAY,cAAc;IAEhC,IAAI,UAAU,MAAM,GAAG,WAAW;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,wBAAwB,EAAE,UAAU,WAAW,CAAC;QAAC;IACpF;IAEA,wBAAwB;IACxB,IAAI,aAAa,IAAI,CAAC,OAAO;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,kCAAkC;IAClC,IAAI,uBAAuB,IAAI,CAAC,OAAO;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,cAAc;IAEhC,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAiC;IACnE;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmD;IACrF;IAEA,0DAA0D;IAC1D,IAAI,CAAC,yBAAyB,IAAI,CAAC,YAAY;QAC7C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoD;IACtF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,KAAK,OAAO;QAAE,SAAS;IAAK,EAAE,kBAAkB;;IAErD,MAAM,YAAY,cAAc,KAAK,OAAO,CAAC,OAAO,IAAI,gBAAgB;;IAExE,IAAI,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,IAAI;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuC;IACzE;IAEA,+CAA+C;IAC/C,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA;;CAEC,GACD,MAAM,iBAAiB,IAAI;AAEpB,MAAM,iBAAiB,CAAC,KAAa,aAAqB;IAC/D,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,SAAS,eAAe,GAAG,CAAC;IAElC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;QACrC,eAAe,GAAG,CAAC,KAAK;YAAE,OAAO;YAAG,WAAW,MAAM;QAAS;QAC9D,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,IAAI,aAAa;QAC/B,OAAO;IACT;IAEA,OAAO,KAAK;IACZ,OAAO;AACT;AAKO,MAAM,oBAAoB;IAC/B,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAC5E;AAKO,MAAM,mBAAmB,CAAC;IAU/B,MAAM,SAAiC,CAAC;IAExC,kBAAkB;IAClB,MAAM,qBAAqB,iBAAiB,SAAS,QAAQ;IAC7D,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAC/B,OAAO,QAAQ,GAAG,mBAAmB,KAAK;IAC5C;IAEA,gCAAgC;IAChC,IAAI,SAAS,QAAQ,EAAE;QACrB,MAAM,qBAAqB,iBAAiB,SAAS,QAAQ;QAC7D,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC/B,OAAO,QAAQ,GAAG,mBAAmB,KAAK;QAC5C;IACF;IAEA,yBAAyB;IACzB,MAAM,2BAA2B,uBAAuB,SAAS,eAAe;IAChF,IAAI,CAAC,yBAAyB,OAAO,EAAE;QACrC,OAAO,eAAe,GAAG,yBAAyB,KAAK;IACzD;IAEA,6BAA6B;IAC7B,IAAI,SAAS,KAAK,EAAE;QAClB,MAAM,kBAAkB,cAAc,SAAS,KAAK;QACpD,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,OAAO,KAAK,GAAG,gBAAgB,KAAK;QACtC;IACF;IAEA,2BAA2B;IAC3B,IAAI,SAAS,GAAG,EAAE;QAChB,MAAM,gBAAgB,YAAY,SAAS,GAAG;QAC9C,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,OAAO,GAAG,GAAG,cAAc,KAAK;QAClC;IACF;IAEA,+BAA+B;IAC/B,IAAI,SAAS,SAAS,EAAE;QACtB,MAAM,sBAAsB,aAAa,SAAS,SAAS,EAAE;QAC7D,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC,OAAO,SAAS,GAAG,oBAAoB,KAAK;QAC9C;IACF;IAEA,IAAI,SAAS,OAAO,EAAE;QACpB,MAAM,oBAAoB,aAAa,SAAS,OAAO,EAAE;QACzD,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC9B,OAAO,OAAO,GAAG,kBAAkB,KAAK;QAC1C;IACF;IAEA,IAAI,SAAS,mBAAmB,EAAE;QAChC,MAAM,sBAAsB,aAAa,SAAS,mBAAmB,EAAE;QACvE,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC,OAAO,mBAAmB,GAAG,oBAAoB,KAAK;QACxD;IACF;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/hooks/useSecurityMonitoring.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface SecurityEvent {\n  type: 'login_attempt' | 'form_submission' | 'suspicious_activity' | 'rate_limit_hit'\n  timestamp: number\n  details: Record<string, any>\n  severity: 'low' | 'medium' | 'high' | 'critical'\n}\n\ninterface SecurityMetrics {\n  totalEvents: number\n  loginAttempts: number\n  blockedRequests: number\n  suspiciousActivity: number\n  lastEventTime: number | null\n}\n\nexport const useSecurityMonitoring = () => {\n  const [events, setEvents] = useState<SecurityEvent[]>([])\n  const [metrics, setMetrics] = useState<SecurityMetrics>({\n    totalEvents: 0,\n    loginAttempts: 0,\n    blockedRequests: 0,\n    suspiciousActivity: 0,\n    lastEventTime: null\n  })\n\n  // Registra un evento di sicurezza\n  const logSecurityEvent = (\n    type: SecurityEvent['type'],\n    details: Record<string, any>,\n    severity: SecurityEvent['severity'] = 'medium'\n  ) => {\n    const event: SecurityEvent = {\n      type,\n      timestamp: Date.now(),\n      details,\n      severity\n    }\n\n    setEvents(prev => {\n      const newEvents = [...prev, event].slice(-100) // Mantieni solo ultimi 100 eventi\n      return newEvents\n    })\n\n    // Aggiorna metriche\n    setMetrics(prev => ({\n      totalEvents: prev.totalEvents + 1,\n      loginAttempts: prev.loginAttempts + (type === 'login_attempt' ? 1 : 0),\n      blockedRequests: prev.blockedRequests + (severity === 'high' || severity === 'critical' ? 1 : 0),\n      suspiciousActivity: prev.suspiciousActivity + (type === 'suspicious_activity' ? 1 : 0),\n      lastEventTime: event.timestamp\n    }))\n\n    // Log in console per debug\n    console.log({\n      type,\n      details,\n      timestamp: new Date(event.timestamp).toISOString()\n    })\n\n    // In produzione, inviare a sistema di monitoring\n    if (process.env.NODE_ENV === 'production' && (severity === 'high' || severity === 'critical')) {\n      // Qui integrare con servizi come Sentry, LogRocket, etc.\n    }\n  }\n\n  // Monitora eventi del browser\n  useEffect(() => {\n    // Monitora tentativi di manipolazione DevTools\n    const detectDevTools = () => {\n      const threshold = 160\n      if (window.outerHeight - window.innerHeight > threshold || \n          window.outerWidth - window.innerWidth > threshold) {\n        logSecurityEvent('suspicious_activity', {\n          action: 'devtools_detected',\n          windowSize: { outer: [window.outerWidth, window.outerHeight], inner: [window.innerWidth, window.innerHeight] }\n        }, 'low')\n      }\n    }\n\n    // Monitora copia/incolla sospetti\n    const handlePaste = (e: ClipboardEvent) => {\n      const pastedText = e.clipboardData?.getData('text') || ''\n      \n      // Controlla pattern sospetti\n      const suspiciousPatterns = [\n        /script/gi,\n        /javascript:/gi,\n        /vbscript:/gi,\n        /onload|onerror|onclick/gi,\n        /<iframe|<object|<embed/gi,\n        /union.*select/gi,\n        /drop.*table/gi\n      ]\n\n      if (suspiciousPatterns.some(pattern => pattern.test(pastedText))) {\n        e.preventDefault()\n        logSecurityEvent('suspicious_activity', {\n          action: 'malicious_paste_blocked',\n          content: pastedText.substring(0, 100) // Solo primi 100 caratteri per privacy\n        }, 'high')\n      }\n    }\n\n    // Monitora tentativi di accesso a localStorage/sessionStorage\n    const originalSetItem = Storage.prototype.setItem\n    Storage.prototype.setItem = function(key: string, value: string) {\n      // Controlla tentativi di inserire script\n      if (/<script|javascript:|vbscript:/gi.test(value)) {\n        logSecurityEvent('suspicious_activity', {\n          action: 'malicious_storage_attempt',\n          key,\n          value: value.substring(0, 50)\n        }, 'high')\n        return\n      }\n      return originalSetItem.call(this, key, value)\n    }\n\n    // Monitora errori JavaScript sospetti\n    const handleError = (event: ErrorEvent) => {\n      const message = event.message.toLowerCase()\n      \n      // Errori che potrebbero indicare attacchi\n      const suspiciousErrors = [\n        'script error',\n        'permission denied',\n        'access denied',\n        'blocked by cors',\n        'network error'\n      ]\n\n      if (suspiciousErrors.some(error => message.includes(error))) {\n        logSecurityEvent('suspicious_activity', {\n          action: 'suspicious_js_error',\n          message: event.message,\n          filename: event.filename,\n          lineno: event.lineno\n        }, 'medium')\n      }\n    }\n\n    // Monitora tentativi di navigazione sospetti\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\n      // Se l'utente sta lasciando la pagina in modo anomalo\n      if (performance.now() < 5000) { // Meno di 5 secondi sulla pagina\n        logSecurityEvent('suspicious_activity', {\n          action: 'rapid_page_exit',\n          timeOnPage: performance.now()\n        }, 'low')\n      }\n    }\n\n    // Aggiungi event listeners\n    window.addEventListener('resize', detectDevTools)\n    window.addEventListener('paste', handlePaste)\n    window.addEventListener('error', handleError)\n    window.addEventListener('beforeunload', handleBeforeUnload)\n\n    // Controllo periodico DevTools\n    const devToolsInterval = setInterval(detectDevTools, 5000)\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', detectDevTools)\n      window.removeEventListener('paste', handlePaste)\n      window.removeEventListener('error', handleError)\n      window.removeEventListener('beforeunload', handleBeforeUnload)\n      clearInterval(devToolsInterval)\n      \n      // Ripristina localStorage originale\n      Storage.prototype.setItem = originalSetItem\n    }\n  }, [])\n\n  // Funzioni di utilità\n  const getRecentEvents = (minutes: number = 10) => {\n    const cutoff = Date.now() - (minutes * 60 * 1000)\n    return events.filter(event => event.timestamp > cutoff)\n  }\n\n  const getEventsByType = (type: SecurityEvent['type']) => {\n    return events.filter(event => event.type === type)\n  }\n\n  const getEventsBySeverity = (severity: SecurityEvent['severity']) => {\n    return events.filter(event => event.severity === severity)\n  }\n\n  const isUnderAttack = () => {\n    const recentEvents = getRecentEvents(5) // Ultimi 5 minuti\n    const highSeverityEvents = recentEvents.filter(e => e.severity === 'high' || e.severity === 'critical')\n    \n    return highSeverityEvents.length > 3 // Più di 3 eventi critici in 5 minuti\n  }\n\n  const getThreatLevel = (): 'low' | 'medium' | 'high' | 'critical' => {\n    const recentEvents = getRecentEvents(10)\n    const criticalCount = recentEvents.filter(e => e.severity === 'critical').length\n    const highCount = recentEvents.filter(e => e.severity === 'high').length\n    \n    if (criticalCount > 0) return 'critical'\n    if (highCount > 2) return 'high'\n    if (recentEvents.length > 10) return 'medium'\n    return 'low'\n  }\n\n  // Funzioni per logging specifico\n  const logLoginAttempt = (username: string, success: boolean, details?: Record<string, any>) => {\n    logSecurityEvent('login_attempt', {\n      username,\n      success,\n      userAgent: navigator.userAgent,\n      timestamp: Date.now(),\n      ...details\n    }, success ? 'low' : 'medium')\n  }\n\n  const logFormSubmission = (formType: string, success: boolean, details?: Record<string, any>) => {\n    logSecurityEvent('form_submission', {\n      formType,\n      success,\n      userAgent: navigator.userAgent,\n      ...details\n    }, 'low')\n  }\n\n  const logSuspiciousActivity = (activity: string, details?: Record<string, any>) => {\n    logSecurityEvent('suspicious_activity', {\n      activity,\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n      ...details\n    }, 'high')\n  }\n\n  const logRateLimitHit = (endpoint: string, details?: Record<string, any>) => {\n    logSecurityEvent('rate_limit_hit', {\n      endpoint,\n      userAgent: navigator.userAgent,\n      ...details\n    }, 'medium')\n  }\n\n  return {\n    // Dati\n    events,\n    metrics,\n    \n    // Funzioni di analisi\n    getRecentEvents,\n    getEventsByType,\n    getEventsBySeverity,\n    isUnderAttack,\n    getThreatLevel,\n    \n    // Funzioni di logging\n    logSecurityEvent,\n    logLoginAttempt,\n    logFormSubmission,\n    logSuspiciousActivity,\n    logRateLimitHit\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAmBO,MAAM,wBAAwB;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACtD,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,oBAAoB;QACpB,eAAe;IACjB;IAEA,kCAAkC;IAClC,MAAM,mBAAmB,CACvB,MACA,SACA,WAAsC,QAAQ;QAE9C,MAAM,QAAuB;YAC3B;YACA,WAAW,KAAK,GAAG;YACnB;YACA;QACF;QAEA,UAAU,CAAA;YACR,MAAM,YAAY;mBAAI;gBAAM;aAAM,CAAC,KAAK,CAAC,CAAC,KAAK,kCAAkC;;YACjF,OAAO;QACT;QAEA,oBAAoB;QACpB,WAAW,CAAA,OAAQ,CAAC;gBAClB,aAAa,KAAK,WAAW,GAAG;gBAChC,eAAe,KAAK,aAAa,GAAG,CAAC,SAAS,kBAAkB,IAAI,CAAC;gBACrE,iBAAiB,KAAK,eAAe,GAAG,CAAC,aAAa,UAAU,aAAa,aAAa,IAAI,CAAC;gBAC/F,oBAAoB,KAAK,kBAAkB,GAAG,CAAC,SAAS,wBAAwB,IAAI,CAAC;gBACrF,eAAe,MAAM,SAAS;YAChC,CAAC;QAED,2BAA2B;QAC3B,QAAQ,GAAG,CAAC;YACV;YACA;YACA,WAAW,IAAI,KAAK,MAAM,SAAS,EAAE,WAAW;QAClD;QAEA,iDAAiD;QACjD,IAAI,oDAAyB,gBAAgB,CAAC,aAAa,UAAU,aAAa,UAAU,GAAG;QAC7F,yDAAyD;QAC3D;IACF;IAEA,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+CAA+C;QAC/C,MAAM,iBAAiB;YACrB,MAAM,YAAY;YAClB,IAAI,OAAO,WAAW,GAAG,OAAO,WAAW,GAAG,aAC1C,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,WAAW;gBACrD,iBAAiB,uBAAuB;oBACtC,QAAQ;oBACR,YAAY;wBAAE,OAAO;4BAAC,OAAO,UAAU;4BAAE,OAAO,WAAW;yBAAC;wBAAE,OAAO;4BAAC,OAAO,UAAU;4BAAE,OAAO,WAAW;yBAAC;oBAAC;gBAC/G,GAAG;YACL;QACF;QAEA,kCAAkC;QAClC,MAAM,cAAc,CAAC;YACnB,MAAM,aAAa,EAAE,aAAa,EAAE,QAAQ,WAAW;YAEvD,6BAA6B;YAC7B,MAAM,qBAAqB;gBACzB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,IAAI,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,cAAc;gBAChE,EAAE,cAAc;gBAChB,iBAAiB,uBAAuB;oBACtC,QAAQ;oBACR,SAAS,WAAW,SAAS,CAAC,GAAG,KAAK,uCAAuC;gBAC/E,GAAG;YACL;QACF;QAEA,8DAA8D;QAC9D,MAAM,kBAAkB,QAAQ,SAAS,CAAC,OAAO;QACjD,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAS,GAAW,EAAE,KAAa;YAC7D,yCAAyC;YACzC,IAAI,kCAAkC,IAAI,CAAC,QAAQ;gBACjD,iBAAiB,uBAAuB;oBACtC,QAAQ;oBACR;oBACA,OAAO,MAAM,SAAS,CAAC,GAAG;gBAC5B,GAAG;gBACH;YACF;YACA,OAAO,gBAAgB,IAAI,CAAC,IAAI,EAAE,KAAK;QACzC;QAEA,sCAAsC;QACtC,MAAM,cAAc,CAAC;YACnB,MAAM,UAAU,MAAM,OAAO,CAAC,WAAW;YAEzC,0CAA0C;YAC1C,MAAM,mBAAmB;gBACvB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,QAAS,QAAQ,QAAQ,CAAC,SAAS;gBAC3D,iBAAiB,uBAAuB;oBACtC,QAAQ;oBACR,SAAS,MAAM,OAAO;oBACtB,UAAU,MAAM,QAAQ;oBACxB,QAAQ,MAAM,MAAM;gBACtB,GAAG;YACL;QACF;QAEA,6CAA6C;QAC7C,MAAM,qBAAqB,CAAC;YAC1B,sDAAsD;YACtD,IAAI,YAAY,GAAG,KAAK,MAAM;gBAC5B,iBAAiB,uBAAuB;oBACtC,QAAQ;oBACR,YAAY,YAAY,GAAG;gBAC7B,GAAG;YACL;QACF;QAEA,2BAA2B;QAC3B,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,SAAS;QACjC,OAAO,gBAAgB,CAAC,SAAS;QACjC,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,+BAA+B;QAC/B,MAAM,mBAAmB,YAAY,gBAAgB;QAErD,UAAU;QACV,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,SAAS;YACpC,OAAO,mBAAmB,CAAC,SAAS;YACpC,OAAO,mBAAmB,CAAC,gBAAgB;YAC3C,cAAc;YAEd,oCAAoC;YACpC,QAAQ,SAAS,CAAC,OAAO,GAAG;QAC9B;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,kBAAkB,CAAC,UAAkB,EAAE;QAC3C,MAAM,SAAS,KAAK,GAAG,KAAM,UAAU,KAAK;QAC5C,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,GAAG;IAClD;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;IAC/C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IACnD;IAEA,MAAM,gBAAgB;QACpB,MAAM,eAAe,gBAAgB,GAAG,kBAAkB;;QAC1D,MAAM,qBAAqB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,EAAE,QAAQ,KAAK;QAE5F,OAAO,mBAAmB,MAAM,GAAG,EAAE,sCAAsC;;IAC7E;IAEA,MAAM,iBAAiB;QACrB,MAAM,eAAe,gBAAgB;QACrC,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;QAChF,MAAM,YAAY,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;QAExE,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,YAAY,GAAG,OAAO;QAC1B,IAAI,aAAa,MAAM,GAAG,IAAI,OAAO;QACrC,OAAO;IACT;IAEA,iCAAiC;IACjC,MAAM,kBAAkB,CAAC,UAAkB,SAAkB;QAC3D,iBAAiB,iBAAiB;YAChC;YACA;YACA,WAAW,UAAU,SAAS;YAC9B,WAAW,KAAK,GAAG;YACnB,GAAG,OAAO;QACZ,GAAG,UAAU,QAAQ;IACvB;IAEA,MAAM,oBAAoB,CAAC,UAAkB,SAAkB;QAC7D,iBAAiB,mBAAmB;YAClC;YACA;YACA,WAAW,UAAU,SAAS;YAC9B,GAAG,OAAO;QACZ,GAAG;IACL;IAEA,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,iBAAiB,uBAAuB;YACtC;YACA,WAAW,UAAU,SAAS;YAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;YACzB,GAAG,OAAO;QACZ,GAAG;IACL;IAEA,MAAM,kBAAkB,CAAC,UAAkB;QACzC,iBAAiB,kBAAkB;YACjC;YACA,WAAW,UAAU,SAAS;YAC9B,GAAG,OAAO;QACZ,GAAG;IACL;IAEA,OAAO;QACL,OAAO;QACP;QACA;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QACA;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cable, User, Building2, AlertCircle, Loader2, Shield, Mail, Lock, Eye, EyeOff } from 'lucide-react'\nimport { validateUsername, checkRateLimit } from '@/utils/securityValidation'\nimport { useSecurityMonitoring } from '@/hooks/useSecurityMonitoring'\n\nexport default function LoginPage() {\n  const [loginType, setLoginType] = useState<'user' | 'cantiere'>('user')\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    codice_cantiere: '',\n    password_cantiere: ''\n  })\n  const [error, setError] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n  const [showPassword, setShowPassword] = useState(false)\n  const [showPasswordCantiere, setShowPasswordCantiere] = useState(false)\n  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({})\n\n  const { login, loginCantiere } = useAuth()\n  const router = useRouter()\n  const { logLoginAttempt, logSuspiciousActivity, getThreatLevel } = useSecurityMonitoring()\n\n  // Verifica se il form è valido per abilitare il pulsante\n  const isFormValid = (): boolean => {\n    if (loginType === 'user') {\n      return formData.username.trim() !== '' && formData.password.trim() !== ''\n    } else {\n      return formData.codice_cantiere.trim() !== '' && formData.password_cantiere.trim() !== ''\n    }\n  }\n\n  // Validazione sicura del form\n  const validateForm = (): boolean => {\n    const errors: Record<string, string> = {}\n\n    if (loginType === 'user') {\n      // Validazione semplice per login (non registrazione)\n      if (!formData.username || formData.username.trim().length === 0) {\n        errors.username = 'Username è obbligatorio'\n      }\n\n      // Valida password\n      if (!formData.password) {\n        errors.password = 'Password è obbligatoria'\n      }\n    } else {\n      // Valida codice cantiere\n      if (!formData.codice_cantiere.trim()) {\n        errors.codice_cantiere = 'Codice cantiere è obbligatorio'\n      } else if (formData.codice_cantiere.length < 3) {\n        errors.codice_cantiere = 'Codice cantiere troppo corto'\n      }\n\n      // Valida password cantiere\n      if (!formData.password_cantiere) {\n        errors.password_cantiere = 'Password cantiere è obbligatoria'\n      }\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  // Validazione singolo campo\n  const validateField = (field: string, value: string) => {\n    const errors: Record<string, boolean> = { ...fieldErrors }\n\n    if (field === 'username' || field === 'codice_cantiere') {\n      errors[field] = value.trim() === ''\n    } else if (field === 'password' || field === 'password_cantiere') {\n      errors[field] = value.trim() === ''\n    }\n\n    setFieldErrors(errors)\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setValidationErrors({})\n\n    // Rate limiting per prevenire brute force\n    const rateLimitKey = loginType === 'user' ? formData.username : formData.codice_cantiere\n    if (!checkRateLimit(`login-${rateLimitKey}`, 5, 300000)) { // 5 tentativi per 5 minuti\n      setError('Troppi tentativi di login. Riprova tra 5 minuti.')\n      logSuspiciousActivity('rate_limit_exceeded', { loginType, identifier: rateLimitKey })\n      return\n    }\n\n    // Validazione form\n    if (!validateForm()) {\n      return\n    }\n\n    // Controlla livello di minaccia\n    const threatLevel = getThreatLevel()\n    if (threatLevel === 'critical') {\n      setError('Sistema temporaneamente non disponibile per motivi di sicurezza.')\n      logSuspiciousActivity('login_blocked_threat_level', { threatLevel })\n      return\n    }\n\n    setIsLoading(true)\n\n    try {\n      if (loginType === 'user') {\n        const result = await login(formData.username, formData.password)\n\n        if (result.success && result.user) {\n          // Log tentativo di login\n          logLoginAttempt(formData.username, true, { ruolo: result.user.ruolo })\n\n          // Reindirizza in base al ruolo\n          if (result.user.ruolo === 'owner') {\n            router.push('/admin')\n          } else if (result.user.ruolo === 'user') {\n            router.push('/cantieri')\n          } else if (result.user.ruolo === 'cantieri_user') {\n            router.push('/cavi')\n          } else {\n            router.push('/cantieri')\n          }\n        } else {\n          // Login fallito\n          logLoginAttempt(formData.username, false, {\n            error: result.error,\n            loginType\n          })\n          setError(result.error || 'Credenziali non valide')\n        }\n      } else {\n        const result = await loginCantiere(formData.codice_cantiere, formData.password_cantiere)\n\n        if (result.success) {\n          // Log tentativo di login cantiere\n          logLoginAttempt(formData.codice_cantiere, true, { type: 'cantiere' })\n\n          router.push('/cavi')\n        } else {\n          // Login cantiere fallito\n          logLoginAttempt(formData.codice_cantiere, false, {\n            error: result.error,\n            loginType\n          })\n          setError(result.error || 'Credenziali cantiere non valide')\n        }\n      }\n    } catch (error: any) {\n      const identifier = loginType === 'user' ? formData.username : formData.codice_cantiere\n\n      // Log tentativo fallito per errori imprevisti\n      logLoginAttempt(identifier, false, {\n        error: error.message || 'Errore imprevisto',\n        loginType\n      })\n\n      setError('Errore durante il login. Riprova.')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n\n    // Validazione in tempo reale solo se il campo è stato toccato\n    if (fieldErrors[field] !== undefined) {\n      validateField(field, value)\n    }\n\n    // Rimuovi l'errore generale se l'utente sta correggendo\n    if (error && value.trim() !== '') {\n      setError('')\n    }\n  }\n\n  const handleFieldBlur = (field: string, value: string) => {\n    validateField(field, value)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4\">\n      <div className=\"w-full max-w-md space-y-6\">\n        \n        {/* Header */}\n        <div className=\"text-center space-y-2\">\n          <div className=\"flex justify-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center\">\n              <Cable className=\"w-8 h-8 text-white\" />\n            </div>\n          </div>\n          <h1 className=\"text-2xl font-bold text-slate-900\">CABLYS</h1>\n          <p className=\"text-slate-600\">Cable Installation Advance System</p>\n          <p className=\"text-sm text-slate-700 font-medium\">Sistema di gestione cavi di nuova generazione</p>\n        </div>\n\n        {/* Login Type Selector */}\n        <div className=\"flex gap-0\" role=\"tablist\" aria-label=\"Tipo di accesso\">\n          <button\n            type=\"button\"\n            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 font-medium transition-all duration-200 relative focus:outline-none ${\n              loginType === 'user'\n                ? 'text-slate-900'\n                : 'text-slate-600 hover:text-slate-800'\n            }`}\n            onClick={() => setLoginType('user')}\n            aria-pressed={loginType === 'user'}\n            role=\"tab\"\n            aria-selected={loginType === 'user'}\n            id=\"tab-user\"\n            aria-controls=\"panel-user\"\n          >\n            <User className=\"w-4 h-4\" aria-hidden=\"true\" />\n            Accesso Utente\n            {loginType === 'user' && (\n              <div className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 rounded-full\" aria-hidden=\"true\" />\n            )}\n          </button>\n          <button\n            type=\"button\"\n            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 font-medium transition-all duration-200 relative focus:outline-none ${\n              loginType === 'cantiere'\n                ? 'text-slate-900'\n                : 'text-slate-600 hover:text-slate-800'\n            }`}\n            onClick={() => setLoginType('cantiere')}\n            aria-pressed={loginType === 'cantiere'}\n            role=\"tab\"\n            aria-selected={loginType === 'cantiere'}\n            id=\"tab-cantiere\"\n            aria-controls=\"panel-cantiere\"\n          >\n            <Building2 className=\"w-4 h-4\" aria-hidden=\"true\" />\n            Accesso Cantiere\n            {loginType === 'cantiere' && (\n              <div className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-green-600 rounded-full\" aria-hidden=\"true\" />\n            )}\n          </button>\n        </div>\n\n        {/* Login Form */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              {loginType === 'user' ? (\n                <>\n                  <User className=\"w-5 h-5 text-blue-600\" />\n                  Login Utente\n                </>\n              ) : (\n                <>\n                  <Building2 className=\"w-5 h-5 text-green-600\" />\n                  Login Cantiere\n                </>\n              )}\n            </CardTitle>\n            <CardDescription>\n              {loginType === 'user' \n                ? 'Accedi con le tue credenziali utente'\n                : 'Accedi con il codice cantiere'\n              }\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form\n              onSubmit={handleSubmit}\n              className=\"space-y-4\"\n              role=\"tabpanel\"\n              id={loginType === 'user' ? 'panel-user' : 'panel-cantiere'}\n              aria-labelledby={loginType === 'user' ? 'tab-user' : 'tab-cantiere'}\n              noValidate\n            >\n              \n              {loginType === 'user' ? (\n                <>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"username\">Username</Label>\n                    <div className=\"relative\">\n                      <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\" />\n                      <Input\n                        id=\"username\"\n                        type=\"text\"\n                        placeholder=\"Inserisci username\"\n                        value={formData.username}\n                        onChange={(e) => handleInputChange('username', e.target.value)}\n                        onBlur={(e) => handleFieldBlur('username', e.target.value)}\n                        required\n                        disabled={isLoading}\n                        className={`pl-10 ${fieldErrors.username ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}\n                        aria-invalid={fieldErrors.username}\n                        aria-describedby={fieldErrors.username ? 'username-error' : undefined}\n                        autoComplete=\"username\"\n                      />\n                      {fieldErrors.username && (\n                        <p id=\"username-error\" className=\"text-sm text-red-600 mt-1\">\n                          Il campo username è obbligatorio\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"password\">Password</Label>\n                    <div className=\"relative\">\n                      <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\" />\n                      <Input\n                        id=\"password\"\n                        type={showPassword ? 'text' : 'password'}\n                        placeholder=\"Inserisci password\"\n                        value={formData.password}\n                        onChange={(e) => handleInputChange('password', e.target.value)}\n                        onBlur={(e) => handleFieldBlur('password', e.target.value)}\n                        required\n                        disabled={isLoading}\n                        className={`pl-10 pr-10 ${fieldErrors.password ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}\n                        aria-invalid={fieldErrors.password}\n                        aria-describedby={fieldErrors.password ? 'password-error' : undefined}\n                        autoComplete=\"current-password\"\n                      />\n                      {fieldErrors.password && (\n                        <p id=\"password-error\" className=\"text-sm text-red-600 mt-1\">\n                          Il campo password è obbligatorio\n                        </p>\n                      )}\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowPassword(!showPassword)}\n                        className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600\"\n                        disabled={isLoading}\n                        aria-label={showPassword ? 'Nascondi password' : 'Mostra password'}\n                      >\n                        {showPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                      </button>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"codice_cantiere\">Codice Cantiere</Label>\n                    <div className=\"relative\">\n                      <Building2 className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\" />\n                      <Input\n                        id=\"codice_cantiere\"\n                        type=\"text\"\n                        placeholder=\"Inserisci codice cantiere\"\n                        value={formData.codice_cantiere}\n                        onChange={(e) => handleInputChange('codice_cantiere', e.target.value)}\n                        onBlur={(e) => handleFieldBlur('codice_cantiere', e.target.value)}\n                        required\n                        disabled={isLoading}\n                        className={`pl-10 ${fieldErrors.codice_cantiere ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}\n                        aria-invalid={fieldErrors.codice_cantiere}\n                        aria-describedby={fieldErrors.codice_cantiere ? 'codice-cantiere-error' : undefined}\n                        autoComplete=\"off\"\n                      />\n                      {fieldErrors.codice_cantiere && (\n                        <p id=\"codice-cantiere-error\" className=\"text-sm text-red-600 mt-1\">\n                          Il campo codice cantiere è obbligatorio\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"password_cantiere\">Password Cantiere</Label>\n                    <div className=\"relative\">\n                      <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\" />\n                      <Input\n                        id=\"password_cantiere\"\n                        type={showPasswordCantiere ? 'text' : 'password'}\n                        placeholder=\"Inserisci password cantiere\"\n                        value={formData.password_cantiere}\n                        onChange={(e) => handleInputChange('password_cantiere', e.target.value)}\n                        onBlur={(e) => handleFieldBlur('password_cantiere', e.target.value)}\n                        required\n                        disabled={isLoading}\n                        className={`pl-10 pr-10 ${fieldErrors.password_cantiere ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}\n                        aria-invalid={fieldErrors.password_cantiere}\n                        aria-describedby={fieldErrors.password_cantiere ? 'password-cantiere-error' : undefined}\n                        autoComplete=\"current-password\"\n                      />\n                      {fieldErrors.password_cantiere && (\n                        <p id=\"password-cantiere-error\" className=\"text-sm text-red-600 mt-1\">\n                          Il campo password cantiere è obbligatorio\n                        </p>\n                      )}\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowPasswordCantiere(!showPasswordCantiere)}\n                        className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600\"\n                        disabled={isLoading}\n                        aria-label={showPasswordCantiere ? 'Nascondi password' : 'Mostra password'}\n                      >\n                        {showPasswordCantiere ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                      </button>\n                    </div>\n                  </div>\n                </>\n              )}\n\n              {error && (\n                <div className=\"flex items-center gap-2 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\">\n                  <AlertCircle className=\"w-5 h-5 text-red-500 flex-shrink-0\" />\n                  <span className=\"text-sm text-red-800 font-medium\">{error}</span>\n                </div>\n              )}\n\n              <Button\n                type=\"submit\"\n                className={`w-full transition-all duration-200 ${\n                  !isFormValid() && !isLoading\n                    ? 'opacity-50 cursor-not-allowed bg-slate-300 hover:bg-slate-300 text-slate-500'\n                    : 'hover:shadow-md active:scale-[0.98] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'\n                }`}\n                disabled={isLoading || !isFormValid()}\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    Accesso in corso...\n                  </>\n                ) : (\n                  'Accedi'\n                )}\n              </Button>\n\n              {/* Link Password Dimenticata */}\n              {loginType === 'user' && (\n                <div className=\"text-center\">\n                  <button\n                    type=\"button\"\n                    className=\"text-sm text-blue-700 hover:text-blue-900 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-1 py-1 transition-colors duration-200\"\n                    onClick={() => router.push('/forgot-password')}\n                    aria-label=\"Recupera password dimenticata\"\n                  >\n                    Password dimenticata?\n                  </button>\n                </div>\n              )}\n            </form>\n          </CardContent>\n        </Card>\n\n        {/* Info */}\n        <div className=\"text-center space-y-2\">\n          <div className=\"flex justify-center gap-2\">\n            <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n              Next.js 15\n            </Badge>\n            <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n              PWA Ready\n            </Badge>\n          </div>\n          <p className=\"text-xs text-slate-500\">\n            Sistema di gestione cavi di nuova generazione\n          </p>\n        </div>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAZA;;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,mBAAmB;IACrB;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAEzE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD;IAEvF,yDAAyD;IACzD,MAAM,cAAc;QAClB,IAAI,cAAc,QAAQ;YACxB,OAAO,SAAS,QAAQ,CAAC,IAAI,OAAO,MAAM,SAAS,QAAQ,CAAC,IAAI,OAAO;QACzE,OAAO;YACL,OAAO,SAAS,eAAe,CAAC,IAAI,OAAO,MAAM,SAAS,iBAAiB,CAAC,IAAI,OAAO;QACzF;IACF;IAEA,8BAA8B;IAC9B,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,cAAc,QAAQ;YACxB,qDAAqD;YACrD,IAAI,CAAC,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;gBAC/D,OAAO,QAAQ,GAAG;YACpB;YAEA,kBAAkB;YAClB,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,OAAO,QAAQ,GAAG;YACpB;QACF,OAAO;YACL,yBAAyB;YACzB,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI;gBACpC,OAAO,eAAe,GAAG;YAC3B,OAAO,IAAI,SAAS,eAAe,CAAC,MAAM,GAAG,GAAG;gBAC9C,OAAO,eAAe,GAAG;YAC3B;YAEA,2BAA2B;YAC3B,IAAI,CAAC,SAAS,iBAAiB,EAAE;gBAC/B,OAAO,iBAAiB,GAAG;YAC7B;QACF;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC,OAAe;QACpC,MAAM,SAAkC;YAAE,GAAG,WAAW;QAAC;QAEzD,IAAI,UAAU,cAAc,UAAU,mBAAmB;YACvD,MAAM,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO;QACnC,OAAO,IAAI,UAAU,cAAc,UAAU,qBAAqB;YAChE,MAAM,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO;QACnC;QAEA,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,oBAAoB,CAAC;QAErB,0CAA0C;QAC1C,MAAM,eAAe,cAAc,SAAS,SAAS,QAAQ,GAAG,SAAS,eAAe;QACxF,IAAI,CAAC,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,GAAG,SAAS;YACvD,SAAS;YACT,sBAAsB,uBAAuB;gBAAE;gBAAW,YAAY;YAAa;YACnF;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gCAAgC;QAChC,MAAM,cAAc;QACpB,IAAI,gBAAgB,YAAY;YAC9B,SAAS;YACT,sBAAsB,8BAA8B;gBAAE;YAAY;YAClE;QACF;QAEA,aAAa;QAEb,IAAI;YACF,IAAI,cAAc,QAAQ;gBACxB,MAAM,SAAS,MAAM,MAAM,SAAS,QAAQ,EAAE,SAAS,QAAQ;gBAE/D,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,yBAAyB;oBACzB,gBAAgB,SAAS,QAAQ,EAAE,MAAM;wBAAE,OAAO,OAAO,IAAI,CAAC,KAAK;oBAAC;oBAEpE,+BAA+B;oBAC/B,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS;wBACjC,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ;wBACvC,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,iBAAiB;wBAChD,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,gBAAgB;oBAChB,gBAAgB,SAAS,QAAQ,EAAE,OAAO;wBACxC,OAAO,OAAO,KAAK;wBACnB;oBACF;oBACA,SAAS,OAAO,KAAK,IAAI;gBAC3B;YACF,OAAO;gBACL,MAAM,SAAS,MAAM,cAAc,SAAS,eAAe,EAAE,SAAS,iBAAiB;gBAEvF,IAAI,OAAO,OAAO,EAAE;oBAClB,kCAAkC;oBAClC,gBAAgB,SAAS,eAAe,EAAE,MAAM;wBAAE,MAAM;oBAAW;oBAEnE,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,yBAAyB;oBACzB,gBAAgB,SAAS,eAAe,EAAE,OAAO;wBAC/C,OAAO,OAAO,KAAK;wBACnB;oBACF;oBACA,SAAS,OAAO,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,aAAa,cAAc,SAAS,SAAS,QAAQ,GAAG,SAAS,eAAe;YAEtF,8CAA8C;YAC9C,gBAAgB,YAAY,OAAO;gBACjC,OAAO,MAAM,OAAO,IAAI;gBACxB;YACF;YAEA,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,8DAA8D;QAC9D,IAAI,WAAW,CAAC,MAAM,KAAK,WAAW;YACpC,cAAc,OAAO;QACvB;QAEA,wDAAwD;QACxD,IAAI,SAAS,MAAM,IAAI,OAAO,IAAI;YAChC,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC,OAAe;QACtC,cAAc,OAAO;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGrB,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;sCAC9B,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;8BAIpD,8OAAC;oBAAI,WAAU;oBAAa,MAAK;oBAAU,cAAW;;sCACpD,8OAAC;4BACC,MAAK;4BACL,WAAW,CAAC,4HAA4H,EACtI,cAAc,SACV,mBACA,uCACJ;4BACF,SAAS,IAAM,aAAa;4BAC5B,gBAAc,cAAc;4BAC5B,MAAK;4BACL,iBAAe,cAAc;4BAC7B,IAAG;4BACH,iBAAc;;8CAEd,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,eAAY;;;;;;gCAAS;gCAE9C,cAAc,wBACb,8OAAC;oCAAI,WAAU;oCAAkE,eAAY;;;;;;;;;;;;sCAGjG,8OAAC;4BACC,MAAK;4BACL,WAAW,CAAC,4HAA4H,EACtI,cAAc,aACV,mBACA,uCACJ;4BACF,SAAS,IAAM,aAAa;4BAC5B,gBAAc,cAAc;4BAC5B,MAAK;4BACL,iBAAe,cAAc;4BAC7B,IAAG;4BACH,iBAAc;;8CAEd,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;gCAAS;gCAEnD,cAAc,4BACb,8OAAC;oCAAI,WAAU;oCAAmE,eAAY;;;;;;;;;;;;;;;;;;8BAMpG,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,cAAc,uBACb;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA0B;;qEAI5C;;0DACE,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA2B;;;;;;;;8CAKtD,8OAAC,gIAAA,CAAA,kBAAe;8CACb,cAAc,SACX,yCACA;;;;;;;;;;;;sCAIR,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCACC,UAAU;gCACV,WAAU;gCACV,MAAK;gCACL,IAAI,cAAc,SAAS,eAAe;gCAC1C,mBAAiB,cAAc,SAAS,aAAa;gCACrD,UAAU;;oCAGT,cAAc,uBACb;;0DACE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC7D,QAAQ,CAAC,IAAM,gBAAgB,YAAY,EAAE,MAAM,CAAC,KAAK;gEACzD,QAAQ;gEACR,UAAU;gEACV,WAAW,CAAC,MAAM,EAAE,YAAY,QAAQ,GAAG,8EAA8E,IAAI;gEAC7H,gBAAc,YAAY,QAAQ;gEAClC,oBAAkB,YAAY,QAAQ,GAAG,mBAAmB;gEAC5D,cAAa;;;;;;4DAEd,YAAY,QAAQ,kBACnB,8OAAC;gEAAE,IAAG;gEAAiB,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;0DAMnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,eAAe,SAAS;gEAC9B,aAAY;gEACZ,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC7D,QAAQ,CAAC,IAAM,gBAAgB,YAAY,EAAE,MAAM,CAAC,KAAK;gEACzD,QAAQ;gEACR,UAAU;gEACV,WAAW,CAAC,YAAY,EAAE,YAAY,QAAQ,GAAG,8EAA8E,IAAI;gEACnI,gBAAc,YAAY,QAAQ;gEAClC,oBAAkB,YAAY,QAAQ,GAAG,mBAAmB;gEAC5D,cAAa;;;;;;4DAEd,YAAY,QAAQ,kBACnB,8OAAC;gEAAE,IAAG;gEAAiB,WAAU;0EAA4B;;;;;;0EAI/D,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,gBAAgB,CAAC;gEAChC,WAAU;gEACV,UAAU;gEACV,cAAY,eAAe,sBAAsB;0EAEhD,6BAAe,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAAe,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;qEAMxE;;0DACE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,eAAe;gEAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACpE,QAAQ,CAAC,IAAM,gBAAgB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEAChE,QAAQ;gEACR,UAAU;gEACV,WAAW,CAAC,MAAM,EAAE,YAAY,eAAe,GAAG,8EAA8E,IAAI;gEACpI,gBAAc,YAAY,eAAe;gEACzC,oBAAkB,YAAY,eAAe,GAAG,0BAA0B;gEAC1E,cAAa;;;;;;4DAEd,YAAY,eAAe,kBAC1B,8OAAC;gEAAE,IAAG;gEAAwB,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;0DAM1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAoB;;;;;;kEACnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,uBAAuB,SAAS;gEACtC,aAAY;gEACZ,OAAO,SAAS,iBAAiB;gEACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gEACtE,QAAQ,CAAC,IAAM,gBAAgB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gEAClE,QAAQ;gEACR,UAAU;gEACV,WAAW,CAAC,YAAY,EAAE,YAAY,iBAAiB,GAAG,8EAA8E,IAAI;gEAC5I,gBAAc,YAAY,iBAAiB;gEAC3C,oBAAkB,YAAY,iBAAiB,GAAG,4BAA4B;gEAC9E,cAAa;;;;;;4DAEd,YAAY,iBAAiB,kBAC5B,8OAAC;gEAAE,IAAG;gEAA0B,WAAU;0EAA4B;;;;;;0EAIxE,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,wBAAwB,CAAC;gEACxC,WAAU;gEACV,UAAU;gEACV,cAAY,uBAAuB,sBAAsB;0EAExD,qCAAuB,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAAe,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;oCAOjF,uBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAIxD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAW,CAAC,mCAAmC,EAC7C,CAAC,iBAAiB,CAAC,YACf,iFACA,4FACJ;wCACF,UAAU,aAAa,CAAC;kDAEvB,0BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;2DAInD;;;;;;oCAKH,cAAc,wBACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,cAAW;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAA4B;;;;;;8CAGjE,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAA8B;;;;;;;;;;;;sCAIrE,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA4ChB;;;;;AA1CX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,4KAAO,aAAA,EAAW,CAAA,UAAA,EAAa,IAAI,EAAE;IAC3C,MAAM,iNAAa,aAAA,EAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,CAAA,UAAA,EAAa,IAAI,EAAA;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,kNAAS,YAAA,EAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,kNAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///C:/CMS/webapp-nextjs_2/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1717, "column": 0}, "map": {"version": 3, "file": "mail.js", "sources": ["file:///C:/CMS/webapp-nextjs_2/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,EAAK;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1767, "column": 0}, "map": {"version": 3, "file": "lock.js", "sources": ["file:///C:/CMS/webapp-nextjs_2/node_modules/lucide-react/src/icons/lock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '11', x: '3', y: '11', rx: '2', ry: '2', key: '1w4ew1' }],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n];\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('lock', __iconNode);\n\nexport default Lock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file:///C:/CMS/webapp-nextjs_2/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "file": "eye-off.js", "sources": ["file:///C:/CMS/webapp-nextjs_2/node_modules/lucide-react/src/icons/eye-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49',\n      key: 'ct8e1f',\n    },\n  ],\n  ['path', { d: 'M14.084 14.158a3 3 0 0 1-4.242-4.242', key: '151rxh' }],\n  [\n    'path',\n    {\n      d: 'M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143',\n      key: '13bj9a',\n    },\n  ],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n];\n\n/**\n * @component @name EyeOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNzMzIDUuMDc2YTEwLjc0NCAxMC43NDQgMCAwIDEgMTEuMjA1IDYuNTc1IDEgMSAwIDAgMSAwIC42OTYgMTAuNzQ3IDEwLjc0NyAwIDAgMS0xLjQ0NCAyLjQ5IiAvPgogIDxwYXRoIGQ9Ik0xNC4wODQgMTQuMTU4YTMgMyAwIDAgMS00LjI0Mi00LjI0MiIgLz4KICA8cGF0aCBkPSJNMTcuNDc5IDE3LjQ5OWExMC43NSAxMC43NSAwIDAgMS0xNS40MTctNS4xNTEgMSAxIDAgMCAxIDAtLjY5NiAxMC43NSAxMC43NSAwIDAgMSA0LjQ0Ni01LjE0MyIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EyeOff = createLucideIcon('eye-off', __iconNode);\n\nexport default EyeOff;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAwC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACrE;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}