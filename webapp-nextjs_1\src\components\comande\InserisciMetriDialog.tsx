'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertTriangle, CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { comandeApi } from "@/lib/api"

interface Cavo {
  id_cavo: string
  tipologia: string
  formazione: string
  metratura_teorica: number
  metratura_reale?: number
  stato_installazione: string
}

interface InserisciMetriDialogProps {
  open: boolean
  onClose: () => void
  codiceComanda: string
  tipoComanda: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO'
  onSuccess?: (message: string) => void
  onError?: (error: string) => void
}

export default function InserisciMetriDialog({
  open,
  onClose,
  codiceComanda,
  tipoComanda,
  onSuccess,
  onError
}: InserisciMetriDialogProps) {
  const [cavi, setCavi] = useState<Cavo[]>([])
  const [datiMetri, setDatiMetri] = useState<Record<string, any>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Carica i cavi della comanda
  useEffect(() => {
    if (open && codiceComanda) {
      loadCavi()
    }
  }, [open, codiceComanda])

  const loadCavi = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // Ottieni il cantiere selezionato
      const cantiereId = localStorage.getItem('selectedCantiereId')
      if (!cantiereId) {
        throw new Error('Nessun cantiere selezionato')
      }

      const response = await comandeApi.getCaviComanda(codiceComanda)
      setCavi(response.data.cavi || [])
      
      // Inizializza i dati metri
      const initialData: Record<string, any> = {}
      response.data.cavi?.forEach((cavo: Cavo) => {
        initialData[cavo.id_cavo] = {
          metratura_reale: cavo.metratura_reale || 0,
          numero_persone_impiegate: 1,
          sistemazione: '',
          fascettatura: ''
        }
      })
      setDatiMetri(initialData)
      
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || err.message || 'Errore nel caricamento dei cavi'
      setError(errorMsg)
      onError?.(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }

  const handleMetriChange = (idCavo: string, field: string, value: any) => {
    setDatiMetri(prev => ({
      ...prev,
      [idCavo]: {
        ...prev[idCavo],
        [field]: value
      }
    }))
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)
      setError(null)

      // Prepara i dati in base al tipo di comanda
      let endpoint = ''
      let requestData: any = {}

      if (tipoComanda === 'POSA') {
        endpoint = 'dati-posa'
        requestData = { dati_posa: datiMetri }
      } else if (tipoComanda === 'COLLEGAMENTO_PARTENZA' || tipoComanda === 'COLLEGAMENTO_ARRIVO') {
        endpoint = 'dati-collegamento'
        requestData = { dati_collegamento: datiMetri }
      }

      await comandeApi.updateDatiComanda(codiceComanda, endpoint, requestData)
      
      const successMsg = tipoComanda === 'POSA' 
        ? 'Metri posati inseriti con successo'
        : 'Metri collegati inseriti con successo'
      
      onSuccess?.(successMsg)
      toast({
        title: "Successo",
        description: successMsg,
      })
      onClose()
      
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || err.message || 'Errore nel salvataggio'
      setError(errorMsg)
      onError?.(errorMsg)
      toast({
        title: "Errore",
        description: errorMsg,
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  const getTitoloDialog = () => {
    switch (tipoComanda) {
      case 'POSA':
        return 'Inserisci Metri Posati'
      case 'COLLEGAMENTO_PARTENZA':
        return 'Inserisci Metri Collegati - Partenza'
      case 'COLLEGAMENTO_ARRIVO':
        return 'Inserisci Metri Collegati - Arrivo'
      default:
        return 'Inserisci Metri'
    }
  }

  const getDescrizioneDialog = () => {
    switch (tipoComanda) {
      case 'POSA':
        return 'Inserisci i metri realmente posati per ogni cavo'
      case 'COLLEGAMENTO_PARTENZA':
        return 'Inserisci i metri collegati lato partenza'
      case 'COLLEGAMENTO_ARRIVO':
        return 'Inserisci i metri collegati lato arrivo'
      default:
        return 'Inserisci i metri'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-blue-600" />
            {getTitoloDialog()}
          </DialogTitle>
          <p className="text-sm text-gray-600">
            {getDescrizioneDialog()} - Comanda: {codiceComanda}
          </p>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Caricamento cavi...
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertTriangle className="h-5 w-5 mr-2" />
            {error}
          </div>
        ) : cavi.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Nessun cavo trovato per questa comanda
          </div>
        ) : (
          <div className="space-y-4">
            {cavi.map((cavo) => (
              <div key={cavo.id_cavo} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-semibold text-blue-600">{cavo.id_cavo}</h4>
                    <p className="text-sm text-gray-600">
                      {cavo.tipologia} - {cavo.formazione} - {cavo.metratura_teorica}m teorici
                    </p>
                  </div>
                  <Badge variant={cavo.stato_installazione === 'Installato' ? 'default' : 'secondary'}>
                    {cavo.stato_installazione}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor={`metri-${cavo.id_cavo}`}>
                      {tipoComanda === 'POSA' ? 'Metri Posati' : 'Metri Collegati'}
                    </Label>
                    <Input
                      id={`metri-${cavo.id_cavo}`}
                      type="number"
                      min="0"
                      step="0.1"
                      value={datiMetri[cavo.id_cavo]?.metratura_reale || 0}
                      onChange={(e) => handleMetriChange(cavo.id_cavo, 'metratura_reale', parseFloat(e.target.value) || 0)}
                      className="mt-1"
                    />
                  </div>

                  {tipoComanda === 'POSA' && (
                    <>
                      <div>
                        <Label htmlFor={`persone-${cavo.id_cavo}`}>Persone Impiegate</Label>
                        <Input
                          id={`persone-${cavo.id_cavo}`}
                          type="number"
                          min="1"
                          value={datiMetri[cavo.id_cavo]?.numero_persone_impiegate || 1}
                          onChange={(e) => handleMetriChange(cavo.id_cavo, 'numero_persone_impiegate', parseInt(e.target.value) || 1)}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor={`sistemazione-${cavo.id_cavo}`}>Sistemazione</Label>
                        <Input
                          id={`sistemazione-${cavo.id_cavo}`}
                          value={datiMetri[cavo.id_cavo]?.sistemazione || ''}
                          onChange={(e) => handleMetriChange(cavo.id_cavo, 'sistemazione', e.target.value)}
                          className="mt-1"
                          placeholder="Es: Interrato, Aereo..."
                        />
                      </div>

                      <div>
                        <Label htmlFor={`fascettatura-${cavo.id_cavo}`}>Fascettatura</Label>
                        <Input
                          id={`fascettatura-${cavo.id_cavo}`}
                          value={datiMetri[cavo.id_cavo]?.fascettatura || ''}
                          onChange={(e) => handleMetriChange(cavo.id_cavo, 'fascettatura', e.target.value)}
                          className="mt-1"
                          placeholder="Es: Standard, Rinforzata..."
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            Annulla
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={isSaving || cavi.length === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Salvando...
              </>
            ) : (
              'Salva Metri'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
