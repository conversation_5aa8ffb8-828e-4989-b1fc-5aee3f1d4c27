'use client'

import React from 'react'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface NoteEStatoProps {
  formData: Partial<CertificazioneCavoCreate>
  onInputChange: (field: string, value: any) => void
}

export function NoteEStato({
  formData,
  onInputChange
}: NoteEStatoProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Note */}
      <div className="space-y-1 col-span-full">
        <Label htmlFor="note" className="text-xs font-medium text-gray-700">
          Note Aggiuntive
        </Label>
        <Textarea
          id="note"
          value={formData.note || ''}
          onChange={(e) => onInputChange('note', e.target.value)}
          placeholder="Inserisci eventuali note, osservazioni o anomalie riscontrate durante la certificazione..."
          rows={4}
          className="resize-none text-sm border-gray-300"
        />
        <p className="text-xs text-gray-500">
          Descrivi eventuali condizioni particolari o osservazioni rilevanti
        </p>
      </div>

      {/* Stato Certificazione */}
      <div className="space-y-1">
        <Label htmlFor="stato_certificato" className="text-xs font-medium text-gray-700">
          Stato Certificazione
        </Label>
        <Select
          value={formData.stato_certificato || 'BOZZA'}
          onValueChange={(value) => onInputChange('stato_certificato', value)}
        >
          <SelectTrigger className="h-11 text-sm border-gray-300">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="BOZZA">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                <span className="text-sm">Bozza</span>
                </div>
            </SelectItem>
            <SelectItem value="CONFORME">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm">✅ Conforme</span>
              </div>
            </SelectItem>
            <SelectItem value="NON_CONFORME">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-sm">❌ Non Conforme</span>
              </div>
            </SelectItem>
            <SelectItem value="CONFORME_CON_RISERVA">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-sm">⚠️ Conforme con Riserva</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-500">
          Lo stato verrà determinato automaticamente in base ai valori misurati
        </p>
      </div>
    </div>
  )
}
