'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

interface RouteProtectionOptions {
  requiredRole?: 'owner' | 'user' | 'cantieri_user'
  requiresUser?: boolean
  requiresCantiere?: boolean
  redirectTo?: string
  onUnauthorized?: () => void
}

export function useRouteProtection(options: RouteProtectionOptions = {}) {
  const {
    requiredRole,
    requiresUser = false,
    requiresCantiere = false,
    redirectTo = '/login',
    onUnauthorized
  } = options

  const { user, cantiere, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Non fare nulla se ancora in caricamento
    if (isLoading) return

    const checkAccess = () => {
      console.log('🛡️ useRouteProtection: Controllo accesso', {
        isAuthenticated,
        user,
        cantiere,
        requiredRole,
        requiresUser,
        requiresCantiere
      })

      // Se non autenticato
      if (!isAuthenticated) {
        console.log('❌ useRouteProtection: Non autenticato')
        if (onUnauthorized) {
          onUnauthorized()
        } else {
          router.replace(redirectTo)
        }
        return false
      }

      // Se richiede specificamente un utente ma abbiamo solo cantiere
      if (requiresUser && !user) {
        console.log('❌ useRouteProtection: Richiede utente ma abbiamo solo cantiere')
        if (onUnauthorized) {
          onUnauthorized()
        } else {
          router.replace('/login')
        }
        return false
      }

      // Se richiede specificamente un cantiere ma abbiamo solo utente
      if (requiresCantiere && !cantiere) {
        console.log('❌ useRouteProtection: Richiede cantiere ma abbiamo solo utente')
        if (onUnauthorized) {
          onUnauthorized()
        } else {
          router.replace('/login')
        }
        return false
      }

      // Se richiede un ruolo specifico
      if (requiredRole && user) {
        if (user.ruolo !== requiredRole) {
          console.log('❌ useRouteProtection: Ruolo non autorizzato', {
            required: requiredRole,
            actual: user.ruolo
          })
          
          if (onUnauthorized) {
            onUnauthorized()
          } else {
            // Reindirizza alla pagina appropriata per il ruolo dell'utente
            switch (user.ruolo) {
              case 'owner':
                router.replace('/admin')
                break
              case 'user':
                router.replace('/cantieri')
                break
              case 'cantieri_user':
                router.replace('/cavi')
                break
              default:
                router.replace('/login')
            }
          }
          return false
        }
      }

      console.log('✅ useRouteProtection: Accesso autorizzato')
      return true
    }

    checkAccess()
  }, [
    isAuthenticated,
    user,
    cantiere,
    isLoading,
    requiredRole,
    requiresUser,
    requiresCantiere,
    router,
    redirectTo,
    onUnauthorized
  ])

  return {
    isAuthenticated,
    user,
    cantiere,
    isLoading,
    hasAccess: isAuthenticated && 
      (!requiresUser || !!user) && 
      (!requiresCantiere || !!cantiere) && 
      (!requiredRole || (user && user.ruolo === requiredRole))
  }
}
