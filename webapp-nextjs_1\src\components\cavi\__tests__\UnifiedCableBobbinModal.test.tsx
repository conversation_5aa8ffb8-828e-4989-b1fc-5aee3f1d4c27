import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { UnifiedCableBobbinModal } from '../modals/BobinaManagementModals'
import { Cavo } from '@/types'

// Mock delle dipendenze
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: {
      cantiere: {
        id_cantiere: 1,
        commessa: 'TEST-001'
      }
    }
  })
}))

jest.mock('@/lib/api', () => ({
  parcoCaviApi: {
    getBobine: jest.fn().mockResolvedValue({
      data: [
        {
          id_bobina: 'BOB001',
          numero_bobina: '001',
          tipologia: 'LIYCY',
          sezione: '3X2.5MM',
          metri_residui: 100,
          stato_bobina: 'Disponibile',
          compatible: true
        },
        {
          id_bobina: 'BOB002',
          numero_bobina: '002',
          tipologia: 'LIYY',
          sezione: '4X1.5MM',
          metri_residui: 50,
          stato_bobina: 'Disponibile',
          compatible: false
        }
      ]
    })
  }
}))

const mockCavo: Cavo = {
  id_cavo: 'C001',
  id_cantiere: 1,
  revisione_ufficiale: 'Rev 1.0',
  tipologia: 'LIYCY',
  sezione: '3X2.5MM',
  formazione: '3X2.5MM',
  da: 'Quadro A',
  a: 'Quadro B',
  ubicazione_partenza: 'Quadro A',
  ubicazione_arrivo: 'Quadro B',
  metri_teorici: 150,
  metri_posati: 75,
  metratura_reale: 75,
  stato_installazione: 'installato',
  id_bobina: 'BOB001'
}

const mockCantiere = {
  id_cantiere: 1,
  commessa: 'TEST-001'
}

describe('UnifiedCableBobbinModal', () => {
  const mockOnSave = jest.fn()
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Modalità Aggiungi Metri', () => {
    it('dovrebbe renderizzare correttamente in modalità aggiungi_metri', async () => {
      render(
        <UnifiedCableBobbinModal
          mode="aggiungi_metri"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      expect(screen.getByText('Inserisci Metri Posati')).toBeInTheDocument()
      expect(screen.getByText('Metri da Installare')).toBeInTheDocument()
      expect(screen.getByLabelText('Metri Posati *')).toBeInTheDocument()
      expect(screen.getByText('Selezione Bobina')).toBeInTheDocument()
    })

    it('dovrebbe validare correttamente l\'input metri', async () => {
      const user = userEvent.setup()
      
      render(
        <UnifiedCableBobbinModal
          mode="aggiungi_metri"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      const metersInput = screen.getByLabelText('Metri Posati *')
      const saveButton = screen.getByText('Salva')

      // Test input vuoto
      await user.clear(metersInput)
      expect(saveButton).toBeDisabled()

      // Test input negativo
      await user.type(metersInput, '-10')
      expect(screen.getByText('I metri non possono essere negativi')).toBeInTheDocument()

      // Test input zero
      await user.clear(metersInput)
      await user.type(metersInput, '0')
      expect(screen.getByText('I metri devono essere maggiori di zero')).toBeInTheDocument()

      // Test input valido
      await user.clear(metersInput)
      await user.type(metersInput, '50')
      expect(saveButton).toBeEnabled()
    })

    it('dovrebbe permettere la selezione di BOBINA VUOTA', async () => {
      const user = userEvent.setup()
      
      render(
        <UnifiedCableBobbinModal
          mode="aggiungi_metri"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      const bobinaVuotaButton = screen.getByText('🔄 BOBINA VUOTA')
      await user.click(bobinaVuotaButton)

      expect(bobinaVuotaButton).toHaveClass('bg-blue-50', 'border-blue-300')
    })
  })

  describe('Modalità Modifica Bobina', () => {
    it('dovrebbe renderizzare correttamente in modalità modifica_bobina', async () => {
      render(
        <UnifiedCableBobbinModal
          mode="modifica_bobina"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      expect(screen.getByText('Modifica Bobina Cavo')).toBeInTheDocument()
      expect(screen.getByText('Opzioni di Modifica')).toBeInTheDocument()
      expect(screen.getByLabelText('Cambia bobina')).toBeInTheDocument()
      expect(screen.getByLabelText('Bobina vuota')).toBeInTheDocument()
      expect(screen.getByLabelText('Annulla posa')).toBeInTheDocument()
    })

    it('dovrebbe mostrare la selezione bobina solo quando si seleziona "Cambia bobina"', async () => {
      const user = userEvent.setup()
      
      render(
        <UnifiedCableBobbinModal
          mode="modifica_bobina"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      // Inizialmente la selezione bobina non dovrebbe essere visibile
      expect(screen.queryByText('Selezione Bobina')).not.toBeInTheDocument()

      // Seleziona "Cambia bobina"
      const cambiaBobinaRadio = screen.getByLabelText('Cambia bobina')
      await user.click(cambiaBobinaRadio)

      // Ora la selezione bobina dovrebbe essere visibile
      expect(screen.getByText('Selezione Bobina')).toBeInTheDocument()
    })

    it('dovrebbe validare la selezione delle opzioni', async () => {
      const user = userEvent.setup()
      
      render(
        <UnifiedCableBobbinModal
          mode="modifica_bobina"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      const saveButton = screen.getByText('Salva Modifiche')
      
      // Senza selezione, il pulsante dovrebbe essere disabilitato
      expect(saveButton).toBeDisabled()

      // Seleziona un'opzione
      const bobinaVuotaRadio = screen.getByLabelText('Bobina vuota')
      await user.click(bobinaVuotaRadio)

      // Ora il pulsante dovrebbe essere abilitato
      expect(saveButton).toBeEnabled()
    })
  })

  describe('Gestione Errori e Accessibilità', () => {
    it('dovrebbe gestire la chiusura con ESC', async () => {
      const user = userEvent.setup()
      
      render(
        <UnifiedCableBobbinModal
          mode="aggiungi_metri"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      await user.keyboard('{Escape}')
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('dovrebbe avere attributi di accessibilità corretti', () => {
      render(
        <UnifiedCableBobbinModal
          mode="aggiungi_metri"
          open={true}
          onClose={mockOnClose}
          cavo={mockCavo}
          cantiere={mockCantiere}
          onSave={mockOnSave}
        />
      )

      const metersInput = screen.getByLabelText('Metri Posati *')
      expect(metersInput).toHaveAttribute('aria-invalid', 'false')
      expect(metersInput).toHaveAttribute('min', '0')
      expect(metersInput).toHaveAttribute('max', '10000')
    })
  })
})
