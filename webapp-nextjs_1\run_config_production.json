{"system": {"name": "CABLYS Next.js Webapp - Production", "version": "2.0.0", "description": "Sistema di gestione cavi avanzato - Ambiente di produzione"}, "backend": {"enabled": true, "host": "0.0.0.0", "port": 8001, "reload": false, "workers": 4, "timeout": 120, "relative_path": "../webapp", "module": "backend.main:app"}, "frontend": {"enabled": true, "port": 3000, "turbopack": false, "command": "npm start", "timeout": 60}, "database": {"host": "localhost", "port": 5432, "name": "cantieri", "user": "postgres", "check_connection": true}, "development": {"auto_open_browser": false, "show_logs": true, "restart_on_failure": true, "max_restart_attempts": 5}, "monitoring": {"health_check_interval": 60, "log_level": "WARNING", "save_logs": true, "log_file": "cablys_production.log"}}