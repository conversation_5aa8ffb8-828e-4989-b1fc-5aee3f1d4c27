'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Package, Search, CheckCircle } from 'lucide-react'
import { Cavo } from '@/types'
import { parcoCaviApi, caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface Bobina {
  id_bobina: string
  tipologia: string
  sezione: string
  metri_residui: number
  fornitore?: string
}

interface Cantiere {
  id_cantiere: number
  commessa: string
}

interface ModificaBobinaDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  cantiere: Cantiere | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function ModificaBobinaDialog({
  open,
  onClose,
  cavo,
  cantiere: cantiereProp,
  onSuccess,
  onError
}: ModificaBobinaDialogProps) {
  const { cantiere: cantiereAuth } = useAuth()

  // Usa cantiere dalla prop o da auth come fallback
  const cantiere = cantiereProp || cantiereAuth

  const [bobine, setBobine] = useState<Bobina[]>([])
  const [selectedOption, setSelectedOption] = useState('')
  const [selectedBobina, setSelectedBobina] = useState('')
  const [searchText, setSearchText] = useState('')
  const [loading, setLoading] = useState(false)
  const [loadingBobine, setLoadingBobine] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')

  const loadBobineCompatibili = async () => {
    if (!cavo) {
      return
    }

    try {
      setLoadingBobine(true)

      // Fallback per cantiere come negli altri dialog
      let cantiereId = cantiere?.id_cantiere

      // Se non abbiamo cantiere dalla prop, prova localStorage
      if (!cantiereId && typeof window !== 'undefined') {
        const storedId = localStorage.getItem('selectedCantiereId')
        cantiereId = storedId ? parseInt(storedId) : 0

        // FALLBACK TEMPORANEO PER DEBUG - se non c'è niente, usa cantiere 1
        if (!cantiereId || cantiereId === 0) {
          cantiereId = 1
        }
      }

      if (!cantiereId || cantiereId === 0) {
        setBobine([])
        setError('Nessun cantiere selezionato. Seleziona un cantiere e riprova.')
        return
      }

      // Carica bobine compatibili dall'API - sezione nel DB = formazione sistema
      const response = await parcoCaviApi.getBobineCompatibili(cantiereId, {
        tipologia: cavo.tipologia,
        n_conduttori: cavo.n_conduttori,
        sezione: cavo.sezione // sezione nel DB = formazione sistema
      })

      // Gestisce diversi formati di risposta come in InserisciMetriDialog
      let bobineData: any[] = []
      if (Array.isArray(response)) {
        bobineData = response
      } else if (response && Array.isArray((response as any).data)) {
        bobineData = (response as any).data
      } else if (response && Array.isArray((response as any).bobine)) {
        bobineData = (response as any).bobine
      } else {
        throw new Error('Formato risposta API non valido')
      }

      const bobineCompatibili: Bobina[] = bobineData.map((b: any) => ({
        id_bobina: b.id_bobina,
        tipologia: b.tipologia,
        sezione: b.sezione, // sezione nel DB = formazione sistema
        metri_residui: b.metri_residui,
        fornitore: b.fornitore
      }))

      // Aggiungi sempre BOBINA_VUOTA come opzione
      const bobineConVuota: Bobina[] = [
        {
          id_bobina: 'BOBINA_VUOTA',
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0
        },
        ...bobineCompatibili
      ]

      // Se il cavo ha già una bobina, assicurati che sia nella lista
      if (cavo.id_bobina && !bobineConVuota.find(b => b.id_bobina === cavo.id_bobina)) {
        bobineConVuota.push({
          id_bobina: cavo.id_bobina,
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0 // Bobina attualmente in uso
        })
      }

      setBobine(bobineConVuota)
    } catch (error) {
      // Fallback con solo BOBINA_VUOTA e bobina corrente
      const fallbackBobine: Bobina[] = [
        {
          id_bobina: 'BOBINA_VUOTA',
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0
        }
      ]

      if (cavo.id_bobina) {
        fallbackBobine.push({
          id_bobina: cavo.id_bobina,
          tipologia: cavo.tipologia || '',
          sezione: cavo.sezione || '',
          metri_residui: 0
        })
      }

      setBobine(fallbackBobine)
      onError('Errore nel caricamento delle bobine disponibili')
    } finally {
      setLoadingBobine(false)
    }
  }

  // Carica bobine compatibili quando si apre il dialog
  useEffect(() => {

    if (open && cavo) {
      loadBobineCompatibili()
      setSelectedOption('')
      setSelectedBobina('')
      setSearchText('')
      setError('')
    }
  }, [open, cavo, cantiere, cantiereProp, cantiereAuth])

  // Filtra bobine compatibili
  const getBobineCompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione
      const matchesSearch = !searchText ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase())
      return isCompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  // Filtra bobine incompatibili
  const getBobineIncompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isIncompatible = bobina.tipologia !== cavo.tipologia || bobina.sezione !== cavo.sezione
      const matchesSearch = !searchText ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase())
      return isIncompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  const bobineCompatibili = getBobineCompatibili()
  const bobineIncompatibili = getBobineIncompatibili()

  const handleSave = async () => {
    if (!cavo) return

    // Validazione basata su opzione selezionata
    if (!selectedOption) {
      setError('Selezionare un\'operazione')
      return
    }

    if (selectedOption === 'assegna_nuova' && !selectedBobina) {
      setError('Selezionare una bobina dalla lista')
      return
    }

    // Determina la nuova bobina e valida
    let newBobinaId: string = ''

    if (selectedOption === 'annulla_installazione') {
      // Per annullamento installazione, non serve bobina specifica
      newBobinaId = '' // Non usato per questa operazione
    } else if (selectedOption === 'assegna_nuova') {
      newBobinaId = selectedBobina
      // Controlla se la bobina è già associata
      if (newBobinaId === cavo.id_bobina) {
        setError('La bobina selezionata è già associata al cavo')
        return
      }
    } else if (selectedOption === 'rimuovi_bobina') {
      newBobinaId = 'BOBINA_VUOTA'
      // Controlla se il cavo ha già BOBINA_VUOTA
      if (newBobinaId === cavo.id_bobina) {
        setError('Il cavo ha già la bobina vuota assegnata')
        return
      }
    } else {
      setError('Opzione non valida')
      return
    }

    // Verifica metri sufficienti solo per bobine reali
    if (selectedOption === 'assegna_nuova') {
      const bobina = bobine.find(b => b.id_bobina === selectedBobina)
      const metriPosati = cavo.metri_posati || 0
      if (bobina && metriPosati > bobina.metri_residui) {
        setError(`La bobina selezionata ha solo ${bobina.metri_residui}m disponibili, ma il cavo ha ${metriPosati}m posati`)
        return
      }
    }

    try {
      setLoading(true)
      setError('')

      if (!cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      // Gestisci diverse operazioni con endpoint appropriati
      console.log({
        cantiere: cantiere.id_cantiere,
        cavo: cavo.id_cavo,
        operazione: selectedOption,
        nuovaBobina: newBobinaId
      })

      if (selectedOption === 'annulla_installazione') {
        // Annulla installazione: resetta completamente il cavo (metri = 0, stato = "da installare")
        await caviApi.cancelInstallation(cantiere.id_cantiere, cavo.id_cavo)
      } else {
        // Assegna nuova bobina o rimuovi bobina: usa updateBobina
        await caviApi.updateBobina(
          cantiere.id_cantiere,
          cavo.id_cavo,
          newBobinaId,
          true  // force_over: true per permettere bobine incompatibili
        )
      }

      let message = ''
      switch (selectedOption) {
        case 'assegna_nuova':
          message = `Nuova bobina ${newBobinaId} assegnata al cavo ${cavo.id_cavo}`
          break
        case 'rimuovi_bobina':
          message = `Bobina rimossa dal cavo ${cavo.id_cavo} (metri restituiti alla bobina precedente)`
          break
        case 'annulla_installazione':
          message = `Installazione annullata per il cavo ${cavo.id_cavo} (metri restituiti, stato resettato a "da installare")`
          break
        default:
          message = `Operazione completata per il cavo ${cavo.id_cavo}`
      }

      onSuccess(message)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setSelectedOption('')
      setSelectedBobina('')
      setSearchText('')
      setError('')
      setActiveTab('compatibili')
      onClose()
    }
  }

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>
            Modifica Bobina Cavo {cavo.id_cavo}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden space-y-6">
          {/* Sezione Cavo Selezionato */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <h3 className="font-medium">Cavo Selezionato</h3>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex flex-wrap gap-4 text-sm">
                <div>
                  <span className="text-gray-600">ID:</span>
                  <span className="ml-1 font-medium">{cavo.id_cavo}</span>
                </div>
                <div>
                  <span className="text-gray-600">Tipologia:</span>
                  <span className="ml-1 font-medium">{cavo.tipologia}</span>
                </div>
                <div>
                  <span className="text-gray-600">Formazione:</span>
                  <span className="ml-1 font-medium">{cavo.sezione}</span>
                </div>
                <div>
                  <span className="text-gray-600">Metri:</span>
                  <span className="ml-1 font-medium">{cavo.metri_teorici} m</span>
                </div>
                <div>
                  <span className="text-gray-600">Bobina:</span>
                  <span className="ml-1 font-medium">{cavo.id_bobina}</span>
                </div>
                <div>
                  <span className="text-gray-600">Stato:</span>
                  <span className="ml-1 px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                    {cavo.stato_installazione}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Sezione Opzioni di modifica */}
          <div className="space-y-3">
            <h3 className="font-medium">Opzioni di modifica</h3>

            <div className="space-y-2">
              <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                <input
                  type="radio"
                  name="operazione"
                  value="assegna_nuova"
                  checked={selectedOption === 'assegna_nuova'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="w-4 h-4 text-blue-600"
                />
                <span className="text-sm">Assegna nuova bobina</span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                <input
                  type="radio"
                  name="operazione"
                  value="rimuovi_bobina"
                  checked={selectedOption === 'rimuovi_bobina'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="w-4 h-4 text-blue-600"
                />
                <span className="text-sm">Rimuovi bobina attuale</span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                <input
                  type="radio"
                  name="operazione"
                  value="annulla_installazione"
                  checked={selectedOption === 'annulla_installazione'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                  className="w-4 h-4 text-blue-600"
                />
                <span className="text-sm">Annulla installazione</span>
              </label>
            </div>
          </div>

          {/* Sezione Seleziona bobina - solo se "Assegna nuova bobina" è selezionato */}
          {selectedOption === 'assegna_nuova' && (
            <div className="space-y-3">
              <h3 className="font-medium">Seleziona bobina</h3>

              {/* Campo ricerca */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cerca bobina per ID, tipologia o numero..."
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Tab per bobine compatibili/incompatibili */}
              <div className="flex space-x-1 border-b">
                <button
                  onClick={() => setActiveTab('compatibili')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'compatibili'
                      ? 'border-green-500 text-green-600 bg-green-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Bobine Compatibili ({bobineCompatibili.length})</span>
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('incompatibili')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'incompatibili'
                      ? 'border-orange-500 text-orange-600 bg-orange-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4" />
                    <span>Bobine Incompatibili ({bobineIncompatibili.length})</span>
                  </div>
                </button>
              </div>

              {/* Lista bobine */}
              <div className="border rounded-lg h-64 overflow-y-auto">
                {loadingBobine ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm text-gray-600">Caricamento bobine...</span>
                    </div>
                  </div>
                ) : (
                  <div className="p-2">
                    {activeTab === 'compatibili' ? (
                      bobineCompatibili.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 text-sm">
                          Nessuna bobina compatibile trovata
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {bobineCompatibili.map((bobina) => (
                            <div
                              key={bobina.id_bobina}
                              onClick={() => setSelectedBobina(bobina.id_bobina)}
                              className={`p-3 rounded cursor-pointer transition-colors ${
                                selectedBobina === bobina.id_bobina
                                  ? 'bg-blue-100 border border-blue-300'
                                  : 'hover:bg-gray-50 border border-transparent'
                              }`}
                            >
                              <div className="flex justify-between items-center">
                                <div>
                                  <div className="font-medium text-sm">{bobina.id_bobina}</div>
                                  <div className="text-xs text-gray-500">{bobina.tipologia} - {bobina.sezione}</div>
                                </div>
                                <div className="text-right">
                                  <div className="text-xs text-gray-600">
                                    {bobina.metri_residui}m
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )
                    ) : (
                      bobineIncompatibili.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 text-sm">
                          Nessuna bobina incompatibile trovata
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {bobineIncompatibili.map((bobina) => (
                            <div
                              key={bobina.id_bobina}
                              onClick={() => setSelectedBobina(bobina.id_bobina)}
                              className={`p-3 rounded cursor-pointer transition-colors ${
                                selectedBobina === bobina.id_bobina
                                  ? 'bg-orange-100 border border-orange-300'
                                  : 'hover:bg-gray-50 border border-transparent'
                              }`}
                            >
                              <div className="flex justify-between items-center">
                                <div>
                                  <div className="font-medium text-sm">{bobina.id_bobina}</div>
                                  <div className="text-xs text-gray-500">{bobina.tipologia} - {bobina.sezione}</div>
                                </div>
                                <div className="text-right">
                                  <div className="text-xs text-orange-600 font-medium">30m</div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Errori */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <DialogFooter className="flex justify-end space-x-2">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={
              loading ||
              !selectedOption ||
              (selectedOption === 'bobina_compatibile' && !selectedBobina)
            }
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salva
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
