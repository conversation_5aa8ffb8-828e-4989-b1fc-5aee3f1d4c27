{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/utils/softColors.ts"], "sourcesContent": ["/**\n * Palette di colori morbidi per il sistema CMS\n * Evita il rosso fuoco e usa tonalità più professionali\n */\n\nexport const SOFT_COLORS = {\n  // Stati di successo - Verde morbido\n  SUCCESS: {\n    bg: 'bg-emerald-50',\n    text: 'text-emerald-700',\n    border: 'border-emerald-200',\n    hover: 'hover:bg-emerald-100',\n    hex: '#10b981' // emerald-500\n  },\n\n  // Stati di warning - Giallo ambra/senape\n  WARNING: {\n    bg: 'bg-amber-50',\n    text: 'text-amber-700',\n    border: 'border-amber-200',\n    hover: 'hover:bg-amber-100',\n    hex: '#f59e0b' // amber-500\n  },\n\n  // Stati di attenzione - Arancione tenue\n  ATTENTION: {\n    bg: 'bg-orange-50',\n    text: 'text-orange-700',\n    border: 'border-orange-200',\n    hover: 'hover:bg-orange-100',\n    hex: '#ea580c' // orange-600\n  },\n\n  // Stati di errore - Rosso morbido (non fuoco)\n  ERROR: {\n    bg: 'bg-rose-50',\n    text: 'text-rose-700',\n    border: 'border-rose-200',\n    hover: 'hover:bg-rose-100',\n    hex: '#e11d48' // rose-600\n  },\n\n  // Stati informativi - Blu morbido\n  INFO: {\n    bg: 'bg-sky-50',\n    text: 'text-sky-700',\n    border: 'border-sky-200',\n    hover: 'hover:bg-sky-100',\n    hex: '#0284c7' // sky-600\n  },\n\n  // Stati neutri - Grigio\n  NEUTRAL: {\n    bg: 'bg-slate-50',\n    text: 'text-slate-700',\n    border: 'border-slate-200',\n    hover: 'hover:bg-slate-100',\n    hex: '#475569' // slate-600\n  },\n\n  // Stati di progresso - Indaco\n  PROGRESS: {\n    bg: 'bg-indigo-50',\n    text: 'text-indigo-700',\n    border: 'border-indigo-200',\n    hover: 'hover:bg-indigo-100',\n    hex: '#4f46e5' // indigo-600\n  }\n}\n\n/**\n * Colori specifici per stati bobine\n */\nexport const BOBINA_COLORS = {\n  DISPONIBILE: SOFT_COLORS.SUCCESS,\n  IN_USO: SOFT_COLORS.PROGRESS,\n  TERMINATA: SOFT_COLORS.NEUTRAL,\n  OVER: SOFT_COLORS.WARNING, // Giallo ambra invece di rosso\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati cavi\n */\nexport const CAVO_COLORS = {\n  DA_INSTALLARE: SOFT_COLORS.NEUTRAL,\n  INSTALLATO: SOFT_COLORS.SUCCESS,\n  COLLEGATO_PARTENZA: SOFT_COLORS.INFO,\n  COLLEGATO_ARRIVO: SOFT_COLORS.INFO,\n  COLLEGATO: SOFT_COLORS.PROGRESS,\n  CERTIFICATO: SOFT_COLORS.SUCCESS,\n  SPARE: SOFT_COLORS.WARNING,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati comande\n */\nexport const COMANDA_COLORS = {\n  ATTIVA: SOFT_COLORS.SUCCESS,\n  COMPLETATA: SOFT_COLORS.PROGRESS,\n  ANNULLATA: SOFT_COLORS.NEUTRAL,\n  IN_CORSO: SOFT_COLORS.INFO,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Funzioni helper per ottenere classi CSS\n */\nexport const getSoftColorClasses = (colorType: keyof typeof SOFT_COLORS) => {\n  const color = SOFT_COLORS[colorType]\n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getBobinaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS\n  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getCavoColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof CAVO_COLORS\n  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getComandaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof COMANDA_COLORS\n  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\n/**\n * Colori per percentuali di progresso\n */\nexport const getProgressColor = (percentage: number) => {\n  if (percentage >= 90) return SOFT_COLORS.SUCCESS\n  if (percentage >= 70) return SOFT_COLORS.PROGRESS\n  if (percentage >= 50) return SOFT_COLORS.INFO\n  if (percentage >= 30) return SOFT_COLORS.WARNING\n  return SOFT_COLORS.ATTENTION\n}\n\n/**\n * Colori per priorità\n */\nexport const PRIORITY_COLORS = {\n  ALTA: SOFT_COLORS.ERROR,\n  MEDIA: SOFT_COLORS.WARNING,\n  BASSA: SOFT_COLORS.INFO,\n  NORMALE: SOFT_COLORS.NEUTRAL\n}\n\nexport const getPriorityColorClasses = (priority: string) => {\n  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS\n  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAEM,MAAM,cAAc;IACzB,oCAAoC;IACpC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,cAAc;IAC/B;IAEA,yCAAyC;IACzC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,wCAAwC;IACxC,WAAW;QACT,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;IAEA,8CAA8C;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,WAAW;IAC5B;IAEA,kCAAkC;IAClC,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,UAAU;IAC3B;IAEA,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,8BAA8B;IAC9B,UAAU;QACR,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;AACF;AAKO,MAAM,gBAAgB;IAC3B,aAAa,YAAY,OAAO;IAChC,QAAQ,YAAY,QAAQ;IAC5B,WAAW,YAAY,OAAO;IAC9B,MAAM,YAAY,OAAO;IACzB,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,cAAc;IACzB,eAAe,YAAY,OAAO;IAClC,YAAY,YAAY,OAAO;IAC/B,oBAAoB,YAAY,IAAI;IACpC,kBAAkB,YAAY,IAAI;IAClC,WAAW,YAAY,QAAQ;IAC/B,aAAa,YAAY,OAAO;IAChC,OAAO,YAAY,OAAO;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,iBAAiB;IAC5B,QAAQ,YAAY,OAAO;IAC3B,YAAY,YAAY,QAAQ;IAChC,WAAW,YAAY,OAAO;IAC9B,UAAU,YAAY,IAAI;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,sBAAsB,CAAC;IAClC,MAAM,QAAQ,WAAW,CAAC,UAAU;IACpC,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,kBAAkB,OAAO;IAC/B,MAAM,QAAQ,aAAa,CAAC,gBAAgB,IAAI,cAAc,MAAM;IAEpE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,WAAW,CAAC,gBAAgB,IAAI,YAAY,MAAM;IAEhE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,cAAc,CAAC,gBAAgB,IAAI,eAAe,MAAM;IAEtE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,IAAI,cAAc,IAAI,OAAO,YAAY,QAAQ;IACjD,IAAI,cAAc,IAAI,OAAO,YAAY,IAAI;IAC7C,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,OAAO,YAAY,SAAS;AAC9B;AAKO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,KAAK;IACvB,OAAO,YAAY,OAAO;IAC1B,OAAO,YAAY,IAAI;IACvB,SAAS,YAAY,OAAO;AAC9B;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,qBAAqB,UAAU;IACrC,MAAM,QAAQ,eAAe,CAAC,mBAAmB,IAAI,gBAAgB,OAAO;IAE5E,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/utils/comandeValidation.ts"], "sourcesContent": ["/**\n * Utilità per validazioni del sistema COMANDE\n */\n\nexport interface CavoValidation {\n  id_cavo: string\n  isValid: boolean\n  errors: string[]\n  warnings: string[]\n  info: string[]\n}\n\nexport interface ComandaValidationResult {\n  isValid: boolean\n  errors: string[]\n  warnings: string[]\n  info: string[]\n  caviValidi: any[]\n  caviProblematici: Array<{\n    cavo: any\n    issues: string[]\n  }>\n}\n\nexport const COMMAND_TYPES = {\n  POSA: 'POSA',\n  COLLEGAMENTO_PARTENZA: 'COLLEGAMENTO_PARTENZA', \n  COLLEGAMENTO_ARRIVO: 'COLLEGAMENTO_ARRIVO',\n  CERTIFICAZIONE: 'CERTIFICAZIONE'\n} as const\n\nexport const CABLE_STATES = {\n  NON_INSTALLATO: 'Non Installato',\n  DA_INSTALLARE: 'Da installare', \n  IN_CORSO: 'In corso',\n  INSTALLATO: 'Installato'\n} as const\n\nexport const VALIDATION_TYPES = {\n  CABLE_STATE: 'CABLE_STATE',\n  EXISTING_COMMAND: 'EXISTING_COMMAND',\n  PREREQUISITE: 'PREREQUISITE',\n  RESPONSIBLE_CONFLICT: 'RESPONSIBLE_CONFLICT'\n} as const\n\nexport const SEVERITY_LEVELS = {\n  ERROR: 'ERROR',\n  WARNING: 'WARNING',\n  INFO: 'INFO'\n} as const\n\n/**\n * Valida un singolo cavo per l'assegnazione a una comanda\n */\nexport function validateSingleCavo(\n  cavo: any,\n  tipoComanda: string,\n  responsabile: string\n): CavoValidation {\n  const result: CavoValidation = {\n    id_cavo: cavo.id_cavo,\n    isValid: true,\n    errors: [],\n    warnings: [],\n    info: []\n  }\n\n  // 1. Controlli stato cavo\n  const stateChecks = checkCableState(cavo, tipoComanda)\n  result.errors.push(...stateChecks.errors)\n  result.warnings.push(...stateChecks.warnings)\n  result.info.push(...stateChecks.info)\n\n  // 2. Controlli comande esistenti\n  const existingCommandChecks = checkExistingCommands(cavo, tipoComanda)\n  result.errors.push(...existingCommandChecks.errors)\n  result.warnings.push(...existingCommandChecks.warnings)\n  result.info.push(...existingCommandChecks.info)\n\n  // 3. Controlli prerequisiti\n  const prerequisiteChecks = checkPrerequisites(cavo, tipoComanda)\n  result.warnings.push(...prerequisiteChecks.warnings)\n  result.info.push(...prerequisiteChecks.info)\n\n  // 4. Controlli responsabile\n  const responsibleChecks = checkResponsibleConflicts(cavo, tipoComanda, responsabile)\n  result.warnings.push(...responsibleChecks.warnings)\n  result.info.push(...responsibleChecks.info)\n\n  result.isValid = result.errors.length === 0\n  return result\n}\n\n/**\n * Valida una lista di cavi per l'assegnazione a una comanda\n */\nexport function validateCaviForComanda(\n  cavi: any[],\n  tipoComanda: string,\n  responsabile: string\n): ComandaValidationResult {\n  const result: ComandaValidationResult = {\n    isValid: true,\n    errors: [],\n    warnings: [],\n    info: [],\n    caviValidi: [],\n    caviProblematici: []\n  }\n\n  if (!cavi || cavi.length === 0) {\n    result.errors.push('Nessun cavo selezionato per la comanda')\n    result.isValid = false\n    return result\n  }\n\n  if (!responsabile || responsabile.trim() === '') {\n    result.errors.push('Responsabile non specificato')\n    result.isValid = false\n    return result\n  }\n\n  cavi.forEach(cavo => {\n    const cavoValidation = validateSingleCavo(cavo, tipoComanda, responsabile)\n    \n    // Aggrega i risultati\n    result.errors.push(...cavoValidation.errors)\n    result.warnings.push(...cavoValidation.warnings)\n    result.info.push(...cavoValidation.info)\n    \n    if (cavoValidation.isValid) {\n      result.caviValidi.push(cavo)\n    } else {\n      result.caviProblematici.push({\n        cavo: cavo,\n        issues: cavoValidation.errors\n      })\n    }\n  })\n\n  // La validazione generale è valida se non ci sono errori bloccanti\n  result.isValid = result.errors.length === 0\n  \n  return result\n}\n\n/**\n * Controlla lo stato del cavo per determinare se può essere assegnato alla comanda\n */\nfunction checkCableState(cavo: any, tipoComanda: string) {\n  const result = { errors: [] as string[], warnings: [] as string[], info: [] as string[] }\n  \n  const isInstalled = cavo.stato_installazione === CABLE_STATES.INSTALLATO\n  const hasMeters = cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0\n  const isConnected = cavo.collegamenti && parseInt(cavo.collegamenti) > 0\n  const isCertified = cavo.stato_certificazione === 'CERTIFICATO'\n\n  switch (tipoComanda) {\n    case COMMAND_TYPES.POSA:\n      if (isInstalled) {\n        result.errors.push(`Cavo ${cavo.id_cavo} è già installato e non può essere assegnato a comanda POSA`)\n      }\n      if (hasMeters) {\n        result.warnings.push(`Cavo ${cavo.id_cavo} ha già metratura reale registrata`)\n      }\n      break\n\n    case COMMAND_TYPES.COLLEGAMENTO_PARTENZA:\n    case COMMAND_TYPES.COLLEGAMENTO_ARRIVO:\n      if (!isInstalled && !hasMeters) {\n        result.warnings.push(`Cavo ${cavo.id_cavo} non risulta installato. Verificare prerequisiti.`)\n      }\n      if (isConnected) {\n        result.warnings.push(`Cavo ${cavo.id_cavo} risulta già collegato`)\n      }\n      break\n\n    case COMMAND_TYPES.CERTIFICAZIONE:\n      if (!isInstalled) {\n        result.errors.push(`Cavo ${cavo.id_cavo} deve essere installato per la certificazione`)\n      }\n      if (!isConnected) {\n        result.warnings.push(`Cavo ${cavo.id_cavo} non risulta collegato. Verificare prerequisiti.`)\n      }\n      if (isCertified) {\n        result.warnings.push(`Cavo ${cavo.id_cavo} è già certificato`)\n      }\n      break\n  }\n\n  return result\n}\n\n/**\n * Controlla se il cavo ha già comande assegnate per il tipo specificato\n */\nfunction checkExistingCommands(cavo: any, tipoComanda: string) {\n  const result = { errors: [] as string[], warnings: [] as string[], info: [] as string[] }\n\n  switch (tipoComanda) {\n    case COMMAND_TYPES.POSA:\n      if (cavo.comanda_posa) {\n        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda POSA assegnata: ${cavo.comanda_posa}`)\n      }\n      break\n\n    case COMMAND_TYPES.COLLEGAMENTO_PARTENZA:\n      if (cavo.comanda_partenza) {\n        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda COLLEGAMENTO_PARTENZA assegnata: ${cavo.comanda_partenza}`)\n      }\n      break\n\n    case COMMAND_TYPES.COLLEGAMENTO_ARRIVO:\n      if (cavo.comanda_arrivo) {\n        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda COLLEGAMENTO_ARRIVO assegnata: ${cavo.comanda_arrivo}`)\n      }\n      break\n\n    case COMMAND_TYPES.CERTIFICAZIONE:\n      if (cavo.comanda_certificazione) {\n        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda CERTIFICAZIONE assegnata: ${cavo.comanda_certificazione}`)\n      }\n      break\n  }\n\n  return result\n}\n\n/**\n * Controlla i prerequisiti per il tipo di comanda\n */\nfunction checkPrerequisites(cavo: any, tipoComanda: string) {\n  const result = { warnings: [] as string[], info: [] as string[] }\n\n  switch (tipoComanda) {\n    case COMMAND_TYPES.COLLEGAMENTO_PARTENZA:\n    case COMMAND_TYPES.COLLEGAMENTO_ARRIVO:\n      // Prerequisito: deve esistere comanda posa completata o cavo installato\n      if (!cavo.comanda_posa && (!cavo.metratura_reale || parseFloat(cavo.metratura_reale) <= 0)) {\n        result.warnings.push(`Cavo ${cavo.id_cavo} non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti.`)\n      }\n      break\n\n    case COMMAND_TYPES.CERTIFICAZIONE:\n      // Prerequisito: deve avere collegamenti completati\n      if (!cavo.comanda_partenza && !cavo.comanda_arrivo) {\n        result.warnings.push(`Cavo ${cavo.id_cavo} non ha comande di collegamento assegnate. Verificare prerequisiti.`)\n      }\n      break\n  }\n\n  return result\n}\n\n/**\n * Controlla conflitti di responsabili\n */\nfunction checkResponsibleConflicts(cavo: any, tipoComanda: string, nuovoResponsabile: string) {\n  const result = { warnings: [] as string[], info: [] as string[] }\n\n  // Raccoglie tutti i responsabili esistenti per il cavo\n  const responsabili: Record<string, string> = {\n    posa: cavo.responsabile_posa || '',\n    partenza: cavo.responsabile_partenza || '',\n    arrivo: cavo.responsabile_arrivo || '',\n    certificazione: cavo.responsabile_certificazione || ''\n  }\n\n  // Controlla se ci sono responsabili diversi per lo stesso cavo\n  const responsabiliAttivi = Object.values(responsabili).filter(r => r && r.trim() !== '')\n  const responsabiliUnici = [...new Set(responsabiliAttivi)]\n\n  if (responsabiliUnici.length > 1 && !responsabiliUnici.includes(nuovoResponsabile)) {\n    result.warnings.push(`Cavo ${cavo.id_cavo} ha già responsabili diversi (${responsabiliUnici.join(', ')}). Nuovo responsabile: ${nuovoResponsabile}`)\n  }\n\n  return result\n}\n\n/**\n * Valida i dati di un responsabile\n */\nexport function validateResponsabile(responsabileData: any) {\n  const errors: string[] = []\n\n  if (!responsabileData.nome_responsabile || !responsabileData.nome_responsabile.trim()) {\n    errors.push('Il nome del responsabile è obbligatorio')\n  }\n\n  if (!responsabileData.mail && !responsabileData.numero_telefono) {\n    errors.push('Almeno uno tra email e telefono deve essere specificato')\n  }\n\n  if (responsabileData.mail && !isValidEmail(responsabileData.mail)) {\n    errors.push('Formato email non valido')\n  }\n\n  if (responsabileData.numero_telefono && !isValidPhone(responsabileData.numero_telefono)) {\n    errors.push('Formato telefono non valido')\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors: errors\n  }\n}\n\n/**\n * Valida formato email\n */\nfunction isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n/**\n * Valida formato telefono\n */\nfunction isValidPhone(phone: string): boolean {\n  // Accetta numeri con o senza prefisso, spazi, trattini, parentesi\n  const phoneRegex = /^[\\+]?[0-9\\s\\-\\(\\)]{8,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n/**\n * Formatta i risultati di validazione per la visualizzazione\n */\nexport function formatValidationResults(validation: ComandaValidationResult) {\n  const messages: string[] = []\n\n  if (validation.errors.length > 0) {\n    messages.push(`❌ Errori (${validation.errors.length}):`)\n    validation.errors.forEach(error => messages.push(`  • ${error}`))\n  }\n\n  if (validation.warnings.length > 0) {\n    messages.push(`⚠️ Avvisi (${validation.warnings.length}):`)\n    validation.warnings.forEach(warning => messages.push(`  • ${warning}`))\n  }\n\n  if (validation.info.length > 0) {\n    messages.push(`ℹ️ Informazioni (${validation.info.length}):`)\n    validation.info.forEach(info => messages.push(`  • ${info}`))\n  }\n\n  if (validation.caviValidi.length > 0) {\n    messages.push(`✅ Cavi validi: ${validation.caviValidi.length}`)\n  }\n\n  if (validation.caviProblematici.length > 0) {\n    messages.push(`❌ Cavi problematici: ${validation.caviProblematici.length}`)\n  }\n\n  return messages.join('\\n')\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;AAsBM,MAAM,gBAAgB;IAC3B,MAAM;IACN,uBAAuB;IACvB,qBAAqB;IACrB,gBAAgB;AAClB;AAEO,MAAM,eAAe;IAC1B,gBAAgB;IAChB,eAAe;IACf,UAAU;IACV,YAAY;AACd;AAEO,MAAM,mBAAmB;IAC9B,aAAa;IACb,kBAAkB;IAClB,cAAc;IACd,sBAAsB;AACxB;AAEO,MAAM,kBAAkB;IAC7B,OAAO;IACP,SAAS;IACT,MAAM;AACR;AAKO,SAAS,mBACd,IAAS,EACT,WAAmB,EACnB,YAAoB;IAEpB,MAAM,SAAyB;QAC7B,SAAS,KAAK,OAAO;QACrB,SAAS;QACT,QAAQ,EAAE;QACV,UAAU,EAAE;QACZ,MAAM,EAAE;IACV;IAEA,0BAA0B;IAC1B,MAAM,cAAc,gBAAgB,MAAM;IAC1C,OAAO,MAAM,CAAC,IAAI,IAAI,YAAY,MAAM;IACxC,OAAO,QAAQ,CAAC,IAAI,IAAI,YAAY,QAAQ;IAC5C,OAAO,IAAI,CAAC,IAAI,IAAI,YAAY,IAAI;IAEpC,iCAAiC;IACjC,MAAM,wBAAwB,sBAAsB,MAAM;IAC1D,OAAO,MAAM,CAAC,IAAI,IAAI,sBAAsB,MAAM;IAClD,OAAO,QAAQ,CAAC,IAAI,IAAI,sBAAsB,QAAQ;IACtD,OAAO,IAAI,CAAC,IAAI,IAAI,sBAAsB,IAAI;IAE9C,4BAA4B;IAC5B,MAAM,qBAAqB,mBAAmB,MAAM;IACpD,OAAO,QAAQ,CAAC,IAAI,IAAI,mBAAmB,QAAQ;IACnD,OAAO,IAAI,CAAC,IAAI,IAAI,mBAAmB,IAAI;IAE3C,4BAA4B;IAC5B,MAAM,oBAAoB,0BAA0B,MAAM,aAAa;IACvE,OAAO,QAAQ,CAAC,IAAI,IAAI,kBAAkB,QAAQ;IAClD,OAAO,IAAI,CAAC,IAAI,IAAI,kBAAkB,IAAI;IAE1C,OAAO,OAAO,GAAG,OAAO,MAAM,CAAC,MAAM,KAAK;IAC1C,OAAO;AACT;AAKO,SAAS,uBACd,IAAW,EACX,WAAmB,EACnB,YAAoB;IAEpB,MAAM,SAAkC;QACtC,SAAS;QACT,QAAQ,EAAE;QACV,UAAU,EAAE;QACZ,MAAM,EAAE;QACR,YAAY,EAAE;QACd,kBAAkB,EAAE;IACtB;IAEA,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO,OAAO,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,CAAC,gBAAgB,aAAa,IAAI,OAAO,IAAI;QAC/C,OAAO,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO,OAAO,GAAG;QACjB,OAAO;IACT;IAEA,KAAK,OAAO,CAAC,CAAA;QACX,MAAM,iBAAiB,mBAAmB,MAAM,aAAa;QAE7D,sBAAsB;QACtB,OAAO,MAAM,CAAC,IAAI,IAAI,eAAe,MAAM;QAC3C,OAAO,QAAQ,CAAC,IAAI,IAAI,eAAe,QAAQ;QAC/C,OAAO,IAAI,CAAC,IAAI,IAAI,eAAe,IAAI;QAEvC,IAAI,eAAe,OAAO,EAAE;YAC1B,OAAO,UAAU,CAAC,IAAI,CAAC;QACzB,OAAO;YACL,OAAO,gBAAgB,CAAC,IAAI,CAAC;gBAC3B,MAAM;gBACN,QAAQ,eAAe,MAAM;YAC/B;QACF;IACF;IAEA,mEAAmE;IACnE,OAAO,OAAO,GAAG,OAAO,MAAM,CAAC,MAAM,KAAK;IAE1C,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,gBAAgB,IAAS,EAAE,WAAmB;IACrD,MAAM,SAAS;QAAE,QAAQ,EAAE;QAAc,UAAU,EAAE;QAAc,MAAM,EAAE;IAAa;IAExF,MAAM,cAAc,KAAK,mBAAmB,KAAK,aAAa,UAAU;IACxE,MAAM,YAAY,KAAK,eAAe,IAAI,WAAW,KAAK,eAAe,IAAI;IAC7E,MAAM,cAAc,KAAK,YAAY,IAAI,SAAS,KAAK,YAAY,IAAI;IACvE,MAAM,cAAc,KAAK,oBAAoB,KAAK;IAElD,OAAQ;QACN,KAAK,cAAc,IAAI;YACrB,IAAI,aAAa;gBACf,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,2DAA2D,CAAC;YACtG;YACA,IAAI,WAAW;gBACb,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,kCAAkC,CAAC;YAC/E;YACA;QAEF,KAAK,cAAc,qBAAqB;QACxC,KAAK,cAAc,mBAAmB;YACpC,IAAI,CAAC,eAAe,CAAC,WAAW;gBAC9B,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,iDAAiD,CAAC;YAC9F;YACA,IAAI,aAAa;gBACf,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,sBAAsB,CAAC;YACnE;YACA;QAEF,KAAK,cAAc,cAAc;YAC/B,IAAI,CAAC,aAAa;gBAChB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,6CAA6C,CAAC;YACxF;YACA,IAAI,CAAC,aAAa;gBAChB,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,gDAAgD,CAAC;YAC7F;YACA,IAAI,aAAa;gBACf,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,kBAAkB,CAAC;YAC/D;YACA;IACJ;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,IAAS,EAAE,WAAmB;IAC3D,MAAM,SAAS;QAAE,QAAQ,EAAE;QAAc,UAAU,EAAE;QAAc,MAAM,EAAE;IAAa;IAExF,OAAQ;QACN,KAAK,cAAc,IAAI;YACrB,IAAI,KAAK,YAAY,EAAE;gBACrB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,gCAAgC,EAAE,KAAK,YAAY,EAAE;YAC/F;YACA;QAEF,KAAK,cAAc,qBAAqB;YACtC,IAAI,KAAK,gBAAgB,EAAE;gBACzB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,iDAAiD,EAAE,KAAK,gBAAgB,EAAE;YACpH;YACA;QAEF,KAAK,cAAc,mBAAmB;YACpC,IAAI,KAAK,cAAc,EAAE;gBACvB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,+CAA+C,EAAE,KAAK,cAAc,EAAE;YAChH;YACA;QAEF,KAAK,cAAc,cAAc;YAC/B,IAAI,KAAK,sBAAsB,EAAE;gBAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,0CAA0C,EAAE,KAAK,sBAAsB,EAAE;YACnH;YACA;IACJ;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,mBAAmB,IAAS,EAAE,WAAmB;IACxD,MAAM,SAAS;QAAE,UAAU,EAAE;QAAc,MAAM,EAAE;IAAa;IAEhE,OAAQ;QACN,KAAK,cAAc,qBAAqB;QACxC,KAAK,cAAc,mBAAmB;YACpC,wEAAwE;YACxE,IAAI,CAAC,KAAK,YAAY,IAAI,CAAC,CAAC,KAAK,eAAe,IAAI,WAAW,KAAK,eAAe,KAAK,CAAC,GAAG;gBAC1F,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,iFAAiF,CAAC;YAC9H;YACA;QAEF,KAAK,cAAc,cAAc;YAC/B,mDAAmD;YACnD,IAAI,CAAC,KAAK,gBAAgB,IAAI,CAAC,KAAK,cAAc,EAAE;gBAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,mEAAmE,CAAC;YAChH;YACA;IACJ;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,0BAA0B,IAAS,EAAE,WAAmB,EAAE,iBAAyB;IAC1F,MAAM,SAAS;QAAE,UAAU,EAAE;QAAc,MAAM,EAAE;IAAa;IAEhE,uDAAuD;IACvD,MAAM,eAAuC;QAC3C,MAAM,KAAK,iBAAiB,IAAI;QAChC,UAAU,KAAK,qBAAqB,IAAI;QACxC,QAAQ,KAAK,mBAAmB,IAAI;QACpC,gBAAgB,KAAK,2BAA2B,IAAI;IACtD;IAEA,+DAA+D;IAC/D,MAAM,qBAAqB,OAAO,MAAM,CAAC,cAAc,MAAM,CAAC,CAAA,IAAK,KAAK,EAAE,IAAI,OAAO;IACrF,MAAM,oBAAoB;WAAI,IAAI,IAAI;KAAoB;IAE1D,IAAI,kBAAkB,MAAM,GAAG,KAAK,CAAC,kBAAkB,QAAQ,CAAC,oBAAoB;QAClF,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,8BAA8B,EAAE,kBAAkB,IAAI,CAAC,MAAM,uBAAuB,EAAE,mBAAmB;IACrJ;IAEA,OAAO;AACT;AAKO,SAAS,qBAAqB,gBAAqB;IACxD,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,iBAAiB,iBAAiB,IAAI,CAAC,iBAAiB,iBAAiB,CAAC,IAAI,IAAI;QACrF,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,eAAe,EAAE;QAC/D,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,iBAAiB,IAAI,IAAI,CAAC,aAAa,iBAAiB,IAAI,GAAG;QACjE,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,iBAAiB,eAAe,IAAI,CAAC,aAAa,iBAAiB,eAAe,GAAG;QACvF,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B,QAAQ;IACV;AACF;AAEA;;CAEC,GACD,SAAS,aAAa,KAAa;IACjC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA;;CAEC,GACD,SAAS,aAAa,KAAa;IACjC,kEAAkE;IAClE,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAKO,SAAS,wBAAwB,UAAmC;IACzE,MAAM,WAAqB,EAAE;IAE7B,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,GAAG;QAChC,SAAS,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QACvD,WAAW,MAAM,CAAC,OAAO,CAAC,CAAA,QAAS,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,OAAO;IACjE;IAEA,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;QAClC,SAAS,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1D,WAAW,QAAQ,CAAC,OAAO,CAAC,CAAA,UAAW,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,SAAS;IACvE;IAEA,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,GAAG;QAC9B,SAAS,IAAI,CAAC,CAAC,iBAAiB,EAAE,WAAW,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5D,WAAW,IAAI,CAAC,OAAO,CAAC,CAAA,OAAQ,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM;IAC7D;IAEA,IAAI,WAAW,UAAU,CAAC,MAAM,GAAG,GAAG;QACpC,SAAS,IAAI,CAAC,CAAC,eAAe,EAAE,WAAW,UAAU,CAAC,MAAM,EAAE;IAChE;IAEA,IAAI,WAAW,gBAAgB,CAAC,MAAM,GAAG,GAAG;QAC1C,SAAS,IAAI,CAAC,CAAC,qBAAqB,EAAE,WAAW,gBAAgB,CAAC,MAAM,EAAE;IAC5E;IAEA,OAAO,SAAS,IAAI,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/comande/CreaComandaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, ClipboardList, Users, Calendar, User } from 'lucide-react'\nimport { comandeApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { validateCaviForComanda, formatValidationResults, COMMAND_TYPES } from '@/utils/comandeValidation'\n\ninterface CreaComandaDialogProps {\n  open: boolean\n  onClose: () => void\n  caviSelezionati?: string[]\n  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n  onComandaCreated?: () => void\n}\n\ninterface Responsabile {\n  id_responsabile: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\ninterface FormData {\n  tipo_comanda: string\n  responsabile: string\n  descrizione: string\n  data_scadenza: string\n  numero_componenti_squadra: number\n}\n\nexport default function CreaComandaDialog({\n  open,\n  onClose,\n  caviSelezionati = [],\n  tipoComanda,\n  onSuccess,\n  onError,\n  onComandaCreated\n}: CreaComandaDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [validationResults, setValidationResults] = useState<string>('')\n  const [showValidation, setShowValidation] = useState(false)\n\n  const { cantiere } = useAuth()\n\n  // Get cantiere ID con fallback al localStorage\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n    }\n  }, [cantiere])\n\n  const [formData, setFormData] = useState<FormData>({\n    tipo_comanda: tipoComanda || 'POSA',\n    responsabile: '',\n    descrizione: '',\n    data_scadenza: '',\n    numero_componenti_squadra: 1\n  })\n\n  // Carica responsabili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId > 0) {\n      loadResponsabili()\n    }\n  }, [open, cantiereId])\n\n  // Reset form quando si chiude il dialog\n  useEffect(() => {\n    if (!open) {\n      setFormData({\n        tipo_comanda: tipoComanda || 'POSA',\n        responsabile: '',\n        descrizione: '',\n        data_scadenza: '',\n        numero_componenti_squadra: 1\n      })\n      setError('')\n    }\n  }, [open, tipoComanda])\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiereId)\n      const responsabiliData = response?.data || response || []\n      setResponsabili(Array.isArray(responsabiliData) ? responsabiliData : [])\n    } catch (error: any) {\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const validateCavi = () => {\n    if (caviSelezionati.length === 0) {\n      setValidationResults('Nessun cavo selezionato per la validazione')\n      setShowValidation(true)\n      return\n    }\n\n    const validation = validateCaviForComanda(\n      caviSelezionati,\n      formData.tipo_comanda,\n      formData.responsabile\n    )\n\n    const formattedResults = formatValidationResults(validation)\n    setValidationResults(formattedResults)\n    setShowValidation(true)\n  }\n\n  const handleSubmit = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      // Validazioni base\n      if (!formData.tipo_comanda) {\n        setError('Seleziona il tipo di comanda')\n        return\n      }\n\n      if (!formData.responsabile) {\n        setError('Seleziona un responsabile')\n        return\n      }\n\n      if (!cantiereId || cantiereId <= 0) {\n        setError('Cantiere non selezionato')\n        return\n      }\n\n      // Validazione cavi se presenti\n      if (caviSelezionati.length > 0) {\n        const validation = validateCaviForComanda(\n          caviSelezionati,\n          formData.tipo_comanda,\n          formData.responsabile\n        )\n\n        if (!validation.isValid) {\n          setError('Validazione cavi fallita. Controllare i dettagli nella sezione validazione.')\n          setValidationResults(formatValidationResults(validation))\n          setShowValidation(true)\n          return\n        }\n\n        // Mostra avvisi se presenti ma permetti di continuare\n        if (validation.warnings.length > 0) {\n          setValidationResults(formatValidationResults(validation))\n          setShowValidation(true)\n        }\n      }\n\n      // Prepara i dati per la creazione\n      const comandaData = {\n        tipo_comanda: formData.tipo_comanda,\n        responsabile: formData.responsabile,\n        descrizione: formData.descrizione || null,\n        data_scadenza: formData.data_scadenza || null,\n        numero_componenti_squadra: formData.numero_componenti_squadra\n      }\n\n      let response\n      if (caviSelezionati && caviSelezionati.length > 0) {\n        // Crea comanda con cavi pre-selezionati\n        response = await comandeApi.createComandaWithCavi(\n          cantiereId,\n          comandaData,\n          caviSelezionati\n        )\n      } else {\n        // Crea comanda vuota\n        response = await comandeApi.createComanda(cantiereId, comandaData)\n      }\n\n      const codiceComanda = response?.data?.codice_comanda || response?.codice_comanda\n      const successMessage = caviSelezionati && caviSelezionati.length > 0\n        ? `Comanda ${codiceComanda} creata con successo per ${caviSelezionati.length} cavi`\n        : `Comanda ${codiceComanda} creata con successo`\n\n      onSuccess(successMessage)\n      onComandaCreated?.()\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const tipoComandaOptions = [\n    { value: 'POSA', label: '🔧 Posa Cavi', description: 'Installazione e posa dei cavi' },\n    { value: 'COLLEGAMENTO_PARTENZA', label: '🔌 Collegamento Partenza', description: 'Collegamento lato partenza' },\n    { value: 'COLLEGAMENTO_ARRIVO', label: '⚡ Collegamento Arrivo', description: 'Collegamento lato arrivo' },\n    { value: 'CERTIFICAZIONE', label: '📋 Certificazione', description: 'Test e certificazione' }\n  ]\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <ClipboardList className=\"h-5 w-5\" />\n            Crea Nuova Comanda\n          </DialogTitle>\n          <DialogDescription>\n            {caviSelezionati && caviSelezionati.length > 0\n              ? `Crea una comanda per ${caviSelezionati.length} cavi selezionati`\n              : 'Crea una nuova comanda di lavoro'\n            }\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6 py-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Sezione validazione cavi */}\n          {caviSelezionati.length > 0 && (\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <h4 className=\"text-sm font-medium\">Validazione Cavi ({caviSelezionati.length} selezionati)</h4>\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={validateCavi}\n                  disabled={!formData.tipo_comanda || !formData.responsabile}\n                >\n                  Valida Cavi\n                </Button>\n              </div>\n\n              {showValidation && validationResults && (\n                <Alert>\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <AlertDescription>\n                    <pre className=\"whitespace-pre-wrap text-xs font-mono\">\n                      {validationResults}\n                    </pre>\n                  </AlertDescription>\n                </Alert>\n              )}\n            </div>\n          )}\n\n          {/* Tipo comanda */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"tipo\">Tipo Comanda *</Label>\n            <Select\n              value={formData.tipo_comanda}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {tipoComandaOptions.map((option) => (\n                  <SelectItem key={option.value} value={option.value}>\n                    <div>\n                      <div className=\"font-medium\">{option.label}</div>\n                      <div className=\"text-sm text-slate-500\">{option.description}</div>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Responsabile */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\">Responsabile *</Label>\n            {loadingResponsabili ? (\n              <div className=\"flex items-center gap-2 p-2 text-sm text-slate-500\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                Caricamento responsabili...\n              </div>\n            ) : (\n              <Select\n                value={formData.responsabile}\n                onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Seleziona responsabile\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {responsabili.map((resp) => (\n                    <SelectItem key={resp.id_responsabile} value={resp.nome_responsabile}>\n                      <div className=\"flex items-center gap-2\">\n                        <User className=\"h-4 w-4\" />\n                        <div>\n                          <div className=\"font-medium\">{resp.nome_responsabile}</div>\n                          {resp.numero_telefono && (\n                            <div className=\"text-sm text-slate-500\">{resp.numero_telefono}</div>\n                          )}\n                        </div>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            )}\n          </div>\n\n          {/* Descrizione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"descrizione\">Descrizione</Label>\n            <Textarea\n              id=\"descrizione\"\n              placeholder=\"Descrizione opzionale della comanda...\"\n              value={formData.descrizione}\n              onChange={(e) => setFormData(prev => ({ ...prev, descrizione: e.target.value }))}\n              rows={3}\n            />\n          </div>\n\n          {/* Data scadenza e numero componenti squadra */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"data_scadenza\">Data Scadenza</Label>\n              <Input\n                id=\"data_scadenza\"\n                type=\"date\"\n                value={formData.data_scadenza}\n                onChange={(e) => setFormData(prev => ({ ...prev, data_scadenza: e.target.value }))}\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"squadra\">Componenti Squadra</Label>\n              <Input\n                id=\"squadra\"\n                type=\"number\"\n                min=\"1\"\n                max=\"20\"\n                value={formData.numero_componenti_squadra}\n                onChange={(e) => setFormData(prev => ({ ...prev, numero_componenti_squadra: parseInt(e.target.value) || 1 }))}\n              />\n            </div>\n          </div>\n\n          {/* Info cavi selezionati */}\n          {caviSelezionati && caviSelezionati.length > 0 && (\n            <div className=\"p-3 bg-blue-50 rounded-lg border border-blue-200\">\n              <div className=\"flex items-center gap-2 text-blue-700\">\n                <ClipboardList className=\"h-4 w-4\" />\n                <span className=\"font-medium\">Cavi da assegnare: {caviSelezionati.length}</span>\n              </div>\n              <div className=\"text-sm text-blue-600 mt-1\">\n                I cavi selezionati verranno automaticamente assegnati a questa comanda\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button onClick={handleSubmit} disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            Crea Comanda\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AA1BA;;;;;;;;;;;;;AAqDe,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,kBAAkB,EAAE,EACpB,WAAW,EACX,SAAS,EACT,OAAO,EACP,gBAAgB,EACO;;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,UAAU,eAAe,SAAS,aAAa,OAAO,CAAC,yBAAyB;gBACjG,cAAc;YAChB;QACF;sCAAG;QAAC;KAAS;IAEb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,cAAc,eAAe;QAC7B,cAAc;QACd,aAAa;QACb,eAAe;QACf,2BAA2B;IAC7B;IAEA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,QAAQ,aAAa,GAAG;gBAC1B;YACF;QACF;sCAAG;QAAC;QAAM;KAAW;IAErB,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,MAAM;gBACT,YAAY;oBACV,cAAc,eAAe;oBAC7B,cAAc;oBACd,aAAa;oBACb,eAAe;oBACf,2BAA2B;gBAC7B;gBACA,SAAS;YACX;QACF;sCAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,mBAAmB;QACvB,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,oHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;YACvD,MAAM,mBAAmB,UAAU,QAAQ,YAAY,EAAE;YACzD,gBAAgB,MAAM,OAAO,CAAC,oBAAoB,mBAAmB,EAAE;QACzE,EAAE,OAAO,OAAY;YACnB,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,qBAAqB;YACrB,kBAAkB;YAClB;QACF;QAEA,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EACtC,iBACA,SAAS,YAAY,EACrB,SAAS,YAAY;QAGvB,MAAM,mBAAmB,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;QACjD,qBAAqB;QACrB,kBAAkB;IACpB;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,SAAS;YAET,mBAAmB;YACnB,IAAI,CAAC,SAAS,YAAY,EAAE;gBAC1B,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,YAAY,EAAE;gBAC1B,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,SAAS;gBACT;YACF;YAEA,+BAA+B;YAC/B,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EACtC,iBACA,SAAS,YAAY,EACrB,SAAS,YAAY;gBAGvB,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,SAAS;oBACT,qBAAqB,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;oBAC7C,kBAAkB;oBAClB;gBACF;gBAEA,sDAAsD;gBACtD,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;oBAClC,qBAAqB,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;oBAC7C,kBAAkB;gBACpB;YACF;YAEA,kCAAkC;YAClC,MAAM,cAAc;gBAClB,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY;gBACnC,aAAa,SAAS,WAAW,IAAI;gBACrC,eAAe,SAAS,aAAa,IAAI;gBACzC,2BAA2B,SAAS,yBAAyB;YAC/D;YAEA,IAAI;YACJ,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;gBACjD,wCAAwC;gBACxC,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,qBAAqB,CAC/C,YACA,aACA;YAEJ,OAAO;gBACL,qBAAqB;gBACrB,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,YAAY;YACxD;YAEA,MAAM,gBAAgB,UAAU,MAAM,kBAAkB,UAAU;YAClE,MAAM,iBAAiB,mBAAmB,gBAAgB,MAAM,GAAG,IAC/D,CAAC,QAAQ,EAAE,cAAc,yBAAyB,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC,GACjF,CAAC,QAAQ,EAAE,cAAc,oBAAoB,CAAC;YAElD,UAAU;YACV;YACA;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB;YAAE,OAAO;YAAQ,OAAO;YAAgB,aAAa;QAAgC;QACrF;YAAE,OAAO;YAAyB,OAAO;YAA4B,aAAa;QAA6B;QAC/G;YAAE,OAAO;YAAuB,OAAO;YAAyB,aAAa;QAA2B;QACxG;YAAE,OAAO;YAAkB,OAAO;YAAqB,aAAa;QAAwB;KAC7F;IAED,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,mBAAmB,gBAAgB,MAAM,GAAG,IACzC,CAAC,qBAAqB,EAAE,gBAAgB,MAAM,CAAC,iBAAiB,CAAC,GACjE;;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAKtB,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAsB;gDAAmB,gBAAgB,MAAM;gDAAC;;;;;;;sDAC9E,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,YAAY;sDAC3D;;;;;;;;;;;;gCAKF,kBAAkB,mCACjB,6LAAC,oIAAA,CAAA,QAAK;;sDACJ,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC,oIAAA,CAAA,mBAAgB;sDACf,cAAA,6LAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;;;;;;;;;;;;sCASb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;;sDAE/E,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;sDACX,mBAAmB,GAAG,CAAC,CAAC,uBACvB,6LAAC,qIAAA,CAAA,aAAU;oDAAoB,OAAO,OAAO,KAAK;8DAChD,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,OAAO,KAAK;;;;;;0EAC1C,6LAAC;gEAAI,WAAU;0EAA0B,OAAO,WAAW;;;;;;;;;;;;mDAH9C,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAYrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;gCAC7B,oCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAyB;;;;;;yDAI9C,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;;sDAE/E,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;oDAA4B,OAAO,KAAK,iBAAiB;8DAClE,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAe,KAAK,iBAAiB;;;;;;oEACnD,KAAK,eAAe,kBACnB,6LAAC;wEAAI,WAAU;kFAA0B,KAAK,eAAe;;;;;;;;;;;;;;;;;;mDANpD,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;sCAkB/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAC9E,MAAM;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,aAAa;4CAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;;;;;;;;;;;;8CAGpF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO,SAAS,yBAAyB;4CACzC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,2BAA2B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDAAE,CAAC;;;;;;;;;;;;;;;;;;wBAMhH,mBAAmB,gBAAgB,MAAM,GAAG,mBAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAK,WAAU;;gDAAc;gDAAoB,gBAAgB,MAAM;;;;;;;;;;;;;8CAE1E,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;8BAOlD,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAc,UAAU;;gCACtC,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;;;;;;;;;;;;;;;;;;AAO1E;GA7VwB;;QAgBD,kIAAA,CAAA,UAAO;;;KAhBN", "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/comande/GestisciResponsabiliDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { \n  Loader2, \n  AlertCircle, \n  Users, \n  Plus, \n  Edit, \n  Trash2, \n  Phone, \n  Mail,\n  User,\n  Save,\n  X\n} from 'lucide-react'\nimport { responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { validateResponsabile } from '@/utils/comandeValidation'\n\ninterface GestisciResponsabiliDialogProps {\n  open: boolean\n  onClose: () => void\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id_responsabile: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n  id_cantiere: number\n}\n\ninterface FormData {\n  nome_responsabile: string\n  numero_telefono: string\n  mail: string\n}\n\nexport default function GestisciResponsabiliDialog({\n  open,\n  onClose,\n  onSuccess,\n  onError\n}: GestisciResponsabiliDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [editingId, setEditingId] = useState<number | null>(null)\n  const [showAddForm, setShowAddForm] = useState(false)\n\n  const { cantiere } = useAuth()\n\n  // Get cantiere ID con fallback al localStorage\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n    }\n  }, [cantiere])\n\n  const [formData, setFormData] = useState<FormData>({\n    nome_responsabile: '',\n    numero_telefono: '',\n    mail: ''\n  })\n\n  // Carica responsabili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId > 0) {\n      loadResponsabili()\n    }\n  }, [open, cantiereId])\n\n  // Reset form quando si chiude il dialog\n  useEffect(() => {\n    if (!open) {\n      setFormData({\n        nome_responsabile: '',\n        numero_telefono: '',\n        mail: ''\n      })\n      setError('')\n      setEditingId(null)\n      setShowAddForm(false)\n    }\n  }, [open])\n\n  const loadResponsabili = async () => {\n    try {\n      setLoading(true)\n      const response = await responsabiliApi.getResponsabili(cantiereId)\n      const responsabiliData = response?.data || response || []\n      setResponsabili(Array.isArray(responsabiliData) ? responsabiliData : [])\n    } catch (error: any) {\n      setError('Errore durante il caricamento dei responsabili')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleAdd = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      const responsabileData = {\n        nome_responsabile: formData.nome_responsabile.trim(),\n        numero_telefono: formData.numero_telefono.trim() || null,\n        mail: formData.mail.trim() || null\n      }\n\n      // Validazione con utility\n      const validation = validateResponsabile(responsabileData)\n      if (!validation.isValid) {\n        setError(`Errori di validazione: ${validation.errors.join(', ')}`)\n        return\n      }\n\n      await responsabiliApi.createResponsabile(cantiereId, responsabileData)\n      \n      onSuccess('Responsabile aggiunto con successo')\n      setFormData({\n        nome_responsabile: '',\n        numero_telefono: '',\n        mail: ''\n      })\n      setShowAddForm(false)\n      loadResponsabili()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || 'Errore durante la creazione del responsabile'\n      setError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleEdit = (responsabile: Responsabile) => {\n    setFormData({\n      nome_responsabile: responsabile.nome_responsabile,\n      numero_telefono: responsabile.numero_telefono || '',\n      mail: responsabile.mail || ''\n    })\n    setEditingId(responsabile.id_responsabile)\n    setShowAddForm(false)\n  }\n\n  const handleUpdate = async () => {\n    if (!editingId) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Validazioni\n      if (!formData.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio')\n        return\n      }\n\n      if (formData.mail && !formData.mail.includes('@')) {\n        setError('Inserisci un indirizzo email valido')\n        return\n      }\n\n      const responsabileData = {\n        nome_responsabile: formData.nome_responsabile.trim(),\n        numero_telefono: formData.numero_telefono.trim() || null,\n        mail: formData.mail.trim() || null\n      }\n\n      await responsabiliApi.updateResponsabile(cantiereId, editingId, responsabileData)\n      \n      onSuccess('Responsabile aggiornato con successo')\n      setFormData({\n        nome_responsabile: '',\n        numero_telefono: '',\n        mail: ''\n      })\n      setEditingId(null)\n      loadResponsabili()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || 'Errore durante l\\'aggiornamento del responsabile'\n      setError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: number, nome: string) => {\n    if (!confirm(`Sei sicuro di voler eliminare il responsabile \"${nome}\"?`)) {\n      return\n    }\n\n    try {\n      setLoading(true)\n      await responsabiliApi.deleteResponsabile(cantiereId, id)\n      onSuccess('Responsabile eliminato con successo')\n      loadResponsabili()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || 'Errore durante l\\'eliminazione del responsabile'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const cancelEdit = () => {\n    setEditingId(null)\n    setShowAddForm(false)\n    setFormData({\n      nome_responsabile: '',\n      numero_telefono: '',\n      mail: ''\n    })\n    setError('')\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[700px] max-h-[80vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Gestisci Responsabili\n          </DialogTitle>\n          <DialogDescription>\n            Gestisci i responsabili per il cantiere {typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') || cantiereId : cantiereId}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6 py-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Pulsante aggiungi nuovo */}\n          {!showAddForm && !editingId && (\n            <Button \n              onClick={() => setShowAddForm(true)}\n              className=\"w-full\"\n              variant=\"outline\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Aggiungi Nuovo Responsabile\n            </Button>\n          )}\n\n          {/* Form per aggiungere/modificare */}\n          {(showAddForm || editingId) && (\n            <Card className=\"border-2 border-blue-200\">\n              <CardContent className=\"p-4 space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"font-medium\">\n                    {editingId ? 'Modifica Responsabile' : 'Nuovo Responsabile'}\n                  </h4>\n                  <Button variant=\"ghost\" size=\"sm\" onClick={cancelEdit}>\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n\n                <div className=\"grid grid-cols-1 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"nome\">Nome Responsabile *</Label>\n                    <Input\n                      id=\"nome\"\n                      placeholder=\"Nome e cognome\"\n                      value={formData.nome_responsabile}\n                      onChange={(e) => setFormData(prev => ({ ...prev, nome_responsabile: e.target.value }))}\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"telefono\">Numero Telefono</Label>\n                      <Input\n                        id=\"telefono\"\n                        placeholder=\"+39 ************\"\n                        value={formData.numero_telefono}\n                        onChange={(e) => setFormData(prev => ({ ...prev, numero_telefono: e.target.value }))}\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"email\">Email</Label>\n                      <Input\n                        id=\"email\"\n                        type=\"email\"\n                        placeholder=\"<EMAIL>\"\n                        value={formData.mail}\n                        onChange={(e) => setFormData(prev => ({ ...prev, mail: e.target.value }))}\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex gap-2 pt-2\">\n                  <Button \n                    onClick={editingId ? handleUpdate : handleAdd}\n                    disabled={loading}\n                    className=\"flex-1\"\n                  >\n                    {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                    <Save className=\"mr-2 h-4 w-4\" />\n                    {editingId ? 'Aggiorna' : 'Aggiungi'}\n                  </Button>\n                  <Button variant=\"outline\" onClick={cancelEdit}>\n                    Annulla\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Lista responsabili */}\n          <div className=\"space-y-3\">\n            <h4 className=\"font-medium flex items-center gap-2\">\n              <Users className=\"h-4 w-4\" />\n              Responsabili Esistenti ({responsabili.length})\n            </h4>\n\n            {loading && responsabili.length === 0 ? (\n              <div className=\"flex items-center justify-center py-8\">\n                <div className=\"flex items-center gap-2\">\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  Caricamento responsabili...\n                </div>\n              </div>\n            ) : responsabili.length === 0 ? (\n              <div className=\"text-center py-8 text-slate-500\">\n                Nessun responsabile trovato\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                {responsabili.map((responsabile) => (\n                  <Card key={responsabile.id_responsabile} className={editingId === responsabile.id_responsabile ? 'border-blue-300' : ''}>\n                    <CardContent className=\"p-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <User className=\"h-4 w-4 text-slate-500\" />\n                            <span className=\"font-medium\">{responsabile.nome_responsabile}</span>\n                          </div>\n                          <div className=\"space-y-1 text-sm text-slate-600\">\n                            {responsabile.numero_telefono && (\n                              <div className=\"flex items-center gap-2\">\n                                <Phone className=\"h-3 w-3\" />\n                                {responsabile.numero_telefono}\n                              </div>\n                            )}\n                            {responsabile.mail && (\n                              <div className=\"flex items-center gap-2\">\n                                <Mail className=\"h-3 w-3\" />\n                                {responsabile.mail}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                        <div className=\"flex gap-2\">\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleEdit(responsabile)}\n                            disabled={loading || editingId === responsabile.id_responsabile}\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleDelete(responsabile.id_responsabile, responsabile.nome_responsabile)}\n                            disabled={loading}\n                            className=\"text-red-600 hover:text-red-700\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose}>\n            Chiudi\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;;;AAhCA;;;;;;;;;;;;AAuDe,SAAS,2BAA2B,EACjD,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACyB;;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,UAAU,eAAe,SAAS,aAAa,OAAO,CAAC,yBAAyB;gBACjG,cAAc;YAChB;QACF;+CAAG;QAAC;KAAS;IAEb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,mBAAmB;QACnB,iBAAiB;QACjB,MAAM;IACR;IAEA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,QAAQ,aAAa,GAAG;gBAC1B;YACF;QACF;+CAAG;QAAC;QAAM;KAAW;IAErB,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,CAAC,MAAM;gBACT,YAAY;oBACV,mBAAmB;oBACnB,iBAAiB;oBACjB,MAAM;gBACR;gBACA,SAAS;gBACT,aAAa;gBACb,eAAe;YACjB;QACF;+CAAG;QAAC;KAAK;IAET,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,oHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;YACvD,MAAM,mBAAmB,UAAU,QAAQ,YAAY,EAAE;YACzD,gBAAgB,MAAM,OAAO,CAAC,oBAAoB,mBAAmB,EAAE;QACzE,EAAE,OAAO,OAAY;YACnB,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,mBAAmB;gBACvB,mBAAmB,SAAS,iBAAiB,CAAC,IAAI;gBAClD,iBAAiB,SAAS,eAAe,CAAC,IAAI,MAAM;gBACpD,MAAM,SAAS,IAAI,CAAC,IAAI,MAAM;YAChC;YAEA,0BAA0B;YAC1B,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EAAE;YACxC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,SAAS,CAAC,uBAAuB,EAAE,WAAW,MAAM,CAAC,IAAI,CAAC,OAAO;gBACjE;YACF;YAEA,MAAM,oHAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,YAAY;YAErD,UAAU;YACV,YAAY;gBACV,mBAAmB;gBACnB,iBAAiB;gBACjB,MAAM;YACR;YACA,eAAe;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU;YACrD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,YAAY;YACV,mBAAmB,aAAa,iBAAiB;YACjD,iBAAiB,aAAa,eAAe,IAAI;YACjD,MAAM,aAAa,IAAI,IAAI;QAC7B;QACA,aAAa,aAAa,eAAe;QACzC,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,WAAW;YACX,SAAS;YAET,cAAc;YACd,IAAI,CAAC,SAAS,iBAAiB,CAAC,IAAI,IAAI;gBACtC,SAAS;gBACT;YACF;YAEA,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;gBACjD,SAAS;gBACT;YACF;YAEA,MAAM,mBAAmB;gBACvB,mBAAmB,SAAS,iBAAiB,CAAC,IAAI;gBAClD,iBAAiB,SAAS,eAAe,CAAC,IAAI,MAAM;gBACpD,MAAM,SAAS,IAAI,CAAC,IAAI,MAAM;YAChC;YAEA,MAAM,oHAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,YAAY,WAAW;YAEhE,UAAU;YACV,YAAY;gBACV,mBAAmB;gBACnB,iBAAiB;gBACjB,MAAM;YACR;YACA,aAAa;YACb;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU;YACrD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO,IAAY;QACtC,IAAI,CAAC,QAAQ,CAAC,+CAA+C,EAAE,KAAK,EAAE,CAAC,GAAG;YACxE;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,oHAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,YAAY;YACrD,UAAU;YACV;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU;YACrD,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,eAAe;QACf,YAAY;YACV,mBAAmB;YACnB,iBAAiB;YACjB,MAAM;QACR;QACA,SAAS;IACX;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACwB,uCAAgC,aAAa,OAAO,CAAC,2BAA2B;;;;;;;;;;;;;8BAI7H,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAKtB,CAAC,eAAe,CAAC,2BAChB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,SAAQ;;8CAER,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAMpC,CAAC,eAAe,SAAS,mBACxB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,YAAY,0BAA0B;;;;;;0DAEzC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DACzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,aAAY;wDACZ,OAAO,SAAS,iBAAiB;wDACjC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;;;;;;;;;;;;0DAIxF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,aAAY;gEACZ,OAAO,SAAS,eAAe;gEAC/B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;;;;;;;;;;;;kEAGtF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAM/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,YAAY,eAAe;gDACpC,UAAU;gDACV,WAAU;;oDAET,yBAAW,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEAC/B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,YAAY,aAAa;;;;;;;0DAE5B,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS;0DAAY;;;;;;;;;;;;;;;;;;;;;;;sCASvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;wCACJ,aAAa,MAAM;wCAAC;;;;;;;gCAG9C,WAAW,aAAa,MAAM,KAAK,kBAClC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAyB;;;;;;;;;;;2CAI9C,aAAa,MAAM,KAAK,kBAC1B,6LAAC;oCAAI,WAAU;8CAAkC;;;;;yDAIjD,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,6BACjB,6LAAC,mIAAA,CAAA,OAAI;4CAAoC,WAAW,cAAc,aAAa,eAAe,GAAG,oBAAoB;sDACnH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFAAe,aAAa,iBAAiB;;;;;;;;;;;;8EAE/D,6LAAC;oEAAI,WAAU;;wEACZ,aAAa,eAAe,kBAC3B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAChB,aAAa,eAAe;;;;;;;wEAGhC,aAAa,IAAI,kBAChB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFACf,aAAa,IAAI;;;;;;;;;;;;;;;;;;;sEAK1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,WAAW;oEAC1B,UAAU,WAAW,cAAc,aAAa,eAAe;8EAE/D,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,aAAa,aAAa,eAAe,EAAE,aAAa,iBAAiB;oEACxF,UAAU;oEACV,WAAU;8EAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAvCjB,aAAa,eAAe;;;;;;;;;;;;;;;;;;;;;;8BAmDjD,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;kCAAS;;;;;;;;;;;;;;;;;;;;;;AAOtD;GAvWwB;;QAYD,kIAAA,CAAA,UAAO;;;KAZN", "debugId": null}}, {"offset": {"line": 2827, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 2872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/comande/DettagliComandaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON><PERSON>Footer,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { \n  Loader2, \n  AlertCircle, \n  ClipboardList, \n  Users, \n  Calendar, \n  Clock,\n  CheckCircle,\n  Play,\n  Pause,\n  User,\n  Phone,\n  Mail,\n  MapPin,\n  Cable,\n  Activity\n} from 'lucide-react'\nimport { comandeApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { getComandaColorClasses } from '@/utils/softColors'\n\ninterface DettagliComandaDialogProps {\n  open: boolean\n  onClose: () => void\n  codiceComanda: string | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface ComandaDettagli {\n  codice_comanda: string\n  tipo_comanda: string\n  stato: string\n  responsabile: string\n  descrizione?: string\n  data_creazione: string\n  data_scadenza?: string\n  data_completamento?: string\n  numero_componenti_squadra: number\n  cavi_assegnati?: any[]\n  progresso?: {\n    totale: number\n    completati: number\n    percentuale: number\n  }\n  responsabile_dettagli?: {\n    nome_responsabile: string\n    numero_telefono?: string\n    mail?: string\n  }\n}\n\nexport default function DettagliComandaDialog({\n  open,\n  onClose,\n  codiceComanda,\n  onSuccess,\n  onError\n}: DettagliComandaDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [comanda, setComanda] = useState<ComandaDettagli | null>(null)\n\n  const { cantiere } = useAuth()\n\n  // Get cantiere ID con fallback al localStorage\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n    }\n  }, [cantiere])\n\n  // Carica dettagli comanda quando si apre il dialog\n  useEffect(() => {\n    if (open && codiceComanda && cantiereId > 0) {\n      loadComandaDettagli()\n    }\n  }, [open, codiceComanda, cantiereId])\n\n  // Reset quando si chiude il dialog\n  useEffect(() => {\n    if (!open) {\n      setComanda(null)\n      setError('')\n    }\n  }, [open])\n\n  const loadComandaDettagli = async () => {\n    if (!codiceComanda) return\n\n    try {\n      setLoading(true)\n      setError('')\n      \n      const response = await comandeApi.getComanda(cantiereId, codiceComanda)\n      const comandaData = response?.data || response\n      \n      setComanda(comandaData)\n    } catch (error: any) {\n      setError('Errore durante il caricamento dei dettagli della comanda')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getTipoBadge = (tipo: string) => {\n    const tipoLabels: Record<string, { label: string; icon: string }> = {\n      'POSA': { label: 'Posa Cavi', icon: '🔧' },\n      'COLLEGAMENTO_PARTENZA': { label: 'Collegamento Partenza', icon: '🔌' },\n      'COLLEGAMENTO_ARRIVO': { label: 'Collegamento Arrivo', icon: '⚡' },\n      'CERTIFICAZIONE': { label: 'Certificazione', icon: '📋' }\n    }\n    \n    const tipoInfo = tipoLabels[tipo] || { label: tipo, icon: '❓' }\n    \n    return (\n      <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200\">\n        {tipoInfo.icon} {tipoInfo.label}\n      </Badge>\n    )\n  }\n\n  const getStatusBadge = (stato: string) => {\n    const colors = getComandaColorClasses(stato)\n    return (\n      <Badge className={colors.badge}>\n        {stato}\n      </Badge>\n    )\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('it-IT', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  const getProgressColor = (percentuale: number) => {\n    if (percentuale >= 80) return 'bg-green-500'\n    if (percentuale >= 50) return 'bg-yellow-500'\n    return 'bg-blue-500'\n  }\n\n  if (!codiceComanda) {\n    return null\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[800px] max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <ClipboardList className=\"h-5 w-5\" />\n            Dettagli Comanda {codiceComanda}\n          </DialogTitle>\n          <DialogDescription>\n            Visualizza tutti i dettagli e lo stato della comanda\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6 py-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {loading ? (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"flex items-center gap-2\">\n                <Loader2 className=\"h-5 w-5 animate-spin\" />\n                Caricamento dettagli comanda...\n              </div>\n            </div>\n          ) : comanda ? (\n            <div className=\"space-y-6\">\n              {/* Header con info principali */}\n              <Card>\n                <CardHeader>\n                  <div className=\"flex items-center justify-between\">\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Activity className=\"h-5 w-5\" />\n                      Informazioni Generali\n                    </CardTitle>\n                    <div className=\"flex gap-2\">\n                      {getTipoBadge(comanda.tipo_comanda)}\n                      {getStatusBadge(comanda.stato)}\n                    </div>\n                  </div>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center gap-2\">\n                        <Calendar className=\"h-4 w-4 text-slate-500\" />\n                        <div>\n                          <p className=\"text-sm text-slate-500\">Data Creazione</p>\n                          <p className=\"font-medium\">{formatDate(comanda.data_creazione)}</p>\n                        </div>\n                      </div>\n                      \n                      {comanda.data_scadenza && (\n                        <div className=\"flex items-center gap-2\">\n                          <Clock className=\"h-4 w-4 text-slate-500\" />\n                          <div>\n                            <p className=\"text-sm text-slate-500\">Scadenza</p>\n                            <p className=\"font-medium\">{formatDate(comanda.data_scadenza)}</p>\n                          </div>\n                        </div>\n                      )}\n                      \n                      {comanda.data_completamento && (\n                        <div className=\"flex items-center gap-2\">\n                          <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                          <div>\n                            <p className=\"text-sm text-slate-500\">Completamento</p>\n                            <p className=\"font-medium text-green-700\">{formatDate(comanda.data_completamento)}</p>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                    \n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center gap-2\">\n                        <Users className=\"h-4 w-4 text-slate-500\" />\n                        <div>\n                          <p className=\"text-sm text-slate-500\">Componenti Squadra</p>\n                          <p className=\"font-medium\">{comanda.numero_componenti_squadra} persone</p>\n                        </div>\n                      </div>\n                      \n                      {comanda.descrizione && (\n                        <div>\n                          <p className=\"text-sm text-slate-500\">Descrizione</p>\n                          <p className=\"font-medium\">{comanda.descrizione}</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Responsabile */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <User className=\"h-5 w-5\" />\n                    Responsabile\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex items-start gap-4\">\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-lg\">{comanda.responsabile || 'Non assegnato'}</p>\n                      {comanda.responsabile_dettagli && (\n                        <div className=\"mt-2 space-y-1 text-sm text-slate-600\">\n                          {comanda.responsabile_dettagli.numero_telefono && (\n                            <div className=\"flex items-center gap-2\">\n                              <Phone className=\"h-3 w-3\" />\n                              {comanda.responsabile_dettagli.numero_telefono}\n                            </div>\n                          )}\n                          {comanda.responsabile_dettagli.mail && (\n                            <div className=\"flex items-center gap-2\">\n                              <Mail className=\"h-3 w-3\" />\n                              {comanda.responsabile_dettagli.mail}\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Progresso */}\n              {comanda.progresso && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Activity className=\"h-5 w-5\" />\n                      Progresso Lavori\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm font-medium\">Completamento</span>\n                        <span className=\"text-sm font-medium\">{comanda.progresso.percentuale}%</span>\n                      </div>\n                      <Progress \n                        value={comanda.progresso.percentuale} \n                        className=\"h-2\"\n                      />\n                      <div className=\"flex justify-between text-sm text-slate-600\">\n                        <span>{comanda.progresso.completati} completati</span>\n                        <span>{comanda.progresso.totale} totali</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Cavi assegnati */}\n              {comanda.cavi_assegnati && comanda.cavi_assegnati.length > 0 && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Cable className=\"h-5 w-5\" />\n                      Cavi Assegnati ({comanda.cavi_assegnati.length})\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                      {comanda.cavi_assegnati.map((cavo, index) => (\n                        <div key={index} className=\"flex items-center justify-between p-2 bg-slate-50 rounded\">\n                          <span className=\"font-mono text-sm\">{cavo.id_cavo || cavo}</span>\n                          {cavo.stato && (\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {cavo.stato}\n                            </Badge>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-slate-500\">\n              Nessun dettaglio disponibile\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose}>\n            Chiudi\n          </Button>\n          {comanda && (\n            <Button\n              onClick={() => {\n                // TODO: Aprire dialog di modifica\n                onSuccess('Funzione di modifica in sviluppo')\n              }}\n            >\n              Modifica Comanda\n            </Button>\n          )}\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;;;AAnCA;;;;;;;;;;;;AAoEe,SAAS,sBAAsB,EAC5C,IAAI,EACJ,OAAO,EACP,aAAa,EACb,SAAS,EACT,OAAO,EACoB;;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAE/D,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,UAAU,eAAe,SAAS,aAAa,OAAO,CAAC,yBAAyB;gBACjG,cAAc;YAChB;QACF;0CAAG;QAAC;KAAS;IAEb,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,QAAQ,iBAAiB,aAAa,GAAG;gBAC3C;YACF;QACF;0CAAG;QAAC;QAAM;QAAe;KAAW;IAEpC,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,MAAM;gBACT,WAAW;gBACX,SAAS;YACX;QACF;0CAAG;QAAC;KAAK;IAET,MAAM,sBAAsB;QAC1B,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,UAAU,CAAC,YAAY;YACzD,MAAM,cAAc,UAAU,QAAQ;YAEtC,WAAW;QACb,EAAE,OAAO,OAAY;YACnB,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,aAA8D;YAClE,QAAQ;gBAAE,OAAO;gBAAa,MAAM;YAAK;YACzC,yBAAyB;gBAAE,OAAO;gBAAyB,MAAM;YAAK;YACtE,uBAAuB;gBAAE,OAAO;gBAAuB,MAAM;YAAI;YACjE,kBAAkB;gBAAE,OAAO;gBAAkB,MAAM;YAAK;QAC1D;QAEA,MAAM,WAAW,UAAU,CAAC,KAAK,IAAI;YAAE,OAAO;YAAM,MAAM;QAAI;QAE9D,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;;gBAChC,SAAS,IAAI;gBAAC;gBAAE,SAAS,KAAK;;;;;;;IAGrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD,EAAE;QACtC,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,WAAW,OAAO,KAAK;sBAC3B;;;;;;IAGP;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,IAAI,OAAO;QAC9B,OAAO;IACT;IAEA,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;gCACnB;;;;;;;sCAEpB,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAItB,wBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;;;;;mCAI9C,wBACF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGlC,6LAAC;wDAAI,WAAU;;4DACZ,aAAa,QAAQ,YAAY;4DACjC,eAAe,QAAQ,KAAK;;;;;;;;;;;;;;;;;;sDAInC,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAyB;;;;;;0FACtC,6LAAC;gFAAE,WAAU;0FAAe,WAAW,QAAQ,cAAc;;;;;;;;;;;;;;;;;;4DAIhE,QAAQ,aAAa,kBACpB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAyB;;;;;;0FACtC,6LAAC;gFAAE,WAAU;0FAAe,WAAW,QAAQ,aAAa;;;;;;;;;;;;;;;;;;4DAKjE,QAAQ,kBAAkB,kBACzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,8NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAyB;;;;;;0FACtC,6LAAC;gFAAE,WAAU;0FAA8B,WAAW,QAAQ,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;kEAMxF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAyB;;;;;;0FACtC,6LAAC;gFAAE,WAAU;;oFAAe,QAAQ,yBAAyB;oFAAC;;;;;;;;;;;;;;;;;;;4DAIjE,QAAQ,WAAW,kBAClB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAyB;;;;;;kFACtC,6LAAC;wEAAE,WAAU;kFAAe,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS3D,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIhC,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAuB,QAAQ,YAAY,IAAI;;;;;;wDAC3D,QAAQ,qBAAqB,kBAC5B,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,qBAAqB,CAAC,eAAe,kBAC5C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;gEAGjD,QAAQ,qBAAqB,CAAC,IAAI,kBACjC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEACf,QAAQ,qBAAqB,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAWlD,QAAQ,SAAS,kBAChB,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,6LAAC;gEAAK,WAAU;;oEAAuB,QAAQ,SAAS,CAAC,WAAW;oEAAC;;;;;;;;;;;;;kEAEvE,6LAAC,uIAAA,CAAA,WAAQ;wDACP,OAAO,QAAQ,SAAS,CAAC,WAAW;wDACpC,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,QAAQ,SAAS,CAAC,UAAU;oEAAC;;;;;;;0EACpC,6LAAC;;oEAAM,QAAQ,SAAS,CAAC,MAAM;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQzC,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,mBACzD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;oDACZ,QAAQ,cAAc,CAAC,MAAM;oDAAC;;;;;;;;;;;;sDAGnD,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAK,WAAU;0EAAqB,KAAK,OAAO,IAAI;;;;;;4DACpD,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,KAAK,KAAK;;;;;;;uDAJP;;;;;;;;;;;;;;;;;;;;;;;;;;iDAetB,6LAAC;4BAAI,WAAU;sCAAkC;;;;;;;;;;;;8BAMrD,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAS;;;;;;wBAG3C,yBACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;gCACP,kCAAkC;gCAClC,UAAU;4BACZ;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAtTwB;;QAWD,kIAAA,CAAA,UAAO;;;KAXN", "debugId": null}}, {"offset": {"line": 3758, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/comande/InserisciMetriDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Loader2, AlertTriangle, CheckCircle } from \"lucide-react\"\nimport { useToast } from \"@/hooks/use-toast\"\nimport { comandeApi } from \"@/lib/api\"\n\ninterface Cavo {\n  id_cavo: string\n  tipologia: string\n  formazione: string\n  metratura_teorica: number\n  metratura_reale?: number\n  stato_installazione: string\n}\n\ninterface InserisciMetriDialogProps {\n  open: boolean\n  onClose: () => void\n  codiceComanda: string\n  tipoComanda: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO'\n  onSuccess?: (message: string) => void\n  onError?: (error: string) => void\n}\n\nexport default function InserisciMetriDialog({\n  open,\n  onClose,\n  codiceComanda,\n  tipoComanda,\n  onSuccess,\n  onError\n}: InserisciMetriDialogProps) {\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [datiMetri, setDatiMetri] = useState<Record<string, any>>({})\n  const [isLoading, setIsLoading] = useState(false)\n  const [isSaving, setIsSaving] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const { toast } = useToast()\n\n  // Carica i cavi della comanda\n  useEffect(() => {\n    if (open && codiceComanda) {\n      loadCavi()\n    }\n  }, [open, codiceComanda])\n\n  const loadCavi = async () => {\n    try {\n      setIsLoading(true)\n      setError(null)\n      \n      // Ottieni il cantiere selezionato\n      const cantiereId = localStorage.getItem('selectedCantiereId')\n      if (!cantiereId) {\n        throw new Error('Nessun cantiere selezionato')\n      }\n\n      const response = await comandeApi.getCaviComanda(codiceComanda)\n      setCavi(response.data.cavi || [])\n      \n      // Inizializza i dati metri\n      const initialData: Record<string, any> = {}\n      response.data.cavi?.forEach((cavo: Cavo) => {\n        initialData[cavo.id_cavo] = {\n          metratura_reale: cavo.metratura_reale || 0,\n          numero_persone_impiegate: 1,\n          sistemazione: '',\n          fascettatura: ''\n        }\n      })\n      setDatiMetri(initialData)\n      \n    } catch (err: any) {\n      const errorMsg = err.response?.data?.detail || err.message || 'Errore nel caricamento dei cavi'\n      setError(errorMsg)\n      onError?.(errorMsg)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleMetriChange = (idCavo: string, field: string, value: any) => {\n    setDatiMetri(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        [field]: value\n      }\n    }))\n  }\n\n  const handleSave = async () => {\n    try {\n      setIsSaving(true)\n      setError(null)\n\n      // Prepara i dati in base al tipo di comanda\n      let endpoint = ''\n      let requestData: any = {}\n\n      if (tipoComanda === 'POSA') {\n        endpoint = 'dati-posa'\n        requestData = { dati_posa: datiMetri }\n      } else if (tipoComanda === 'COLLEGAMENTO_PARTENZA' || tipoComanda === 'COLLEGAMENTO_ARRIVO') {\n        endpoint = 'dati-collegamento'\n        requestData = { dati_collegamento: datiMetri }\n      }\n\n      await comandeApi.updateDatiComanda(codiceComanda, endpoint, requestData)\n      \n      const successMsg = tipoComanda === 'POSA' \n        ? 'Metri posati inseriti con successo'\n        : 'Metri collegati inseriti con successo'\n      \n      onSuccess?.(successMsg)\n      toast({\n        title: \"Successo\",\n        description: successMsg,\n      })\n      onClose()\n      \n    } catch (err: any) {\n      const errorMsg = err.response?.data?.detail || err.message || 'Errore nel salvataggio'\n      setError(errorMsg)\n      onError?.(errorMsg)\n      toast({\n        title: \"Errore\",\n        description: errorMsg,\n        variant: \"destructive\"\n      })\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const getTitoloDialog = () => {\n    switch (tipoComanda) {\n      case 'POSA':\n        return 'Inserisci Metri Posati'\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Inserisci Metri Collegati - Partenza'\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Inserisci Metri Collegati - Arrivo'\n      default:\n        return 'Inserisci Metri'\n    }\n  }\n\n  const getDescrizioneDialog = () => {\n    switch (tipoComanda) {\n      case 'POSA':\n        return 'Inserisci i metri realmente posati per ogni cavo'\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Inserisci i metri collegati lato partenza'\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Inserisci i metri collegati lato arrivo'\n      default:\n        return 'Inserisci i metri'\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[80vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <CheckCircle className=\"h-5 w-5 text-blue-600\" />\n            {getTitoloDialog()}\n          </DialogTitle>\n          <p className=\"text-sm text-gray-600\">\n            {getDescrizioneDialog()} - Comanda: {codiceComanda}\n          </p>\n        </DialogHeader>\n\n        {isLoading ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n            Caricamento cavi...\n          </div>\n        ) : error ? (\n          <div className=\"flex items-center justify-center py-8 text-red-600\">\n            <AlertTriangle className=\"h-5 w-5 mr-2\" />\n            {error}\n          </div>\n        ) : cavi.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            Nessun cavo trovato per questa comanda\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {cavi.map((cavo) => (\n              <div key={cavo.id_cavo} className=\"border rounded-lg p-4 bg-gray-50\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div>\n                    <h4 className=\"font-semibold text-blue-600\">{cavo.id_cavo}</h4>\n                    <p className=\"text-sm text-gray-600\">\n                      {cavo.tipologia} - {cavo.formazione} - {cavo.metratura_teorica}m teorici\n                    </p>\n                  </div>\n                  <Badge variant={cavo.stato_installazione === 'Installato' ? 'default' : 'secondary'}>\n                    {cavo.stato_installazione}\n                  </Badge>\n                </div>\n\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  <div>\n                    <Label htmlFor={`metri-${cavo.id_cavo}`}>\n                      {tipoComanda === 'POSA' ? 'Metri Posati' : 'Metri Collegati'}\n                    </Label>\n                    <Input\n                      id={`metri-${cavo.id_cavo}`}\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.1\"\n                      value={datiMetri[cavo.id_cavo]?.metratura_reale || 0}\n                      onChange={(e) => handleMetriChange(cavo.id_cavo, 'metratura_reale', parseFloat(e.target.value) || 0)}\n                      className=\"mt-1\"\n                    />\n                  </div>\n\n                  {tipoComanda === 'POSA' && (\n                    <>\n                      <div>\n                        <Label htmlFor={`persone-${cavo.id_cavo}`}>Persone Impiegate</Label>\n                        <Input\n                          id={`persone-${cavo.id_cavo}`}\n                          type=\"number\"\n                          min=\"1\"\n                          value={datiMetri[cavo.id_cavo]?.numero_persone_impiegate || 1}\n                          onChange={(e) => handleMetriChange(cavo.id_cavo, 'numero_persone_impiegate', parseInt(e.target.value) || 1)}\n                          className=\"mt-1\"\n                        />\n                      </div>\n\n                      <div>\n                        <Label htmlFor={`sistemazione-${cavo.id_cavo}`}>Sistemazione</Label>\n                        <Input\n                          id={`sistemazione-${cavo.id_cavo}`}\n                          value={datiMetri[cavo.id_cavo]?.sistemazione || ''}\n                          onChange={(e) => handleMetriChange(cavo.id_cavo, 'sistemazione', e.target.value)}\n                          className=\"mt-1\"\n                          placeholder=\"Es: Interrato, Aereo...\"\n                        />\n                      </div>\n\n                      <div>\n                        <Label htmlFor={`fascettatura-${cavo.id_cavo}`}>Fascettatura</Label>\n                        <Input\n                          id={`fascettatura-${cavo.id_cavo}`}\n                          value={datiMetri[cavo.id_cavo]?.fascettatura || ''}\n                          onChange={(e) => handleMetriChange(cavo.id_cavo, 'fascettatura', e.target.value)}\n                          className=\"mt-1\"\n                          placeholder=\"Es: Standard, Rinforzata...\"\n                        />\n                      </div>\n                    </>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        <div className=\"flex justify-end gap-2 pt-4 border-t\">\n          <Button variant=\"outline\" onClick={onClose} disabled={isSaving}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleSave} \n            disabled={isSaving || cavi.length === 0}\n            className=\"bg-blue-600 hover:bg-blue-700\"\n          >\n            {isSaving ? (\n              <>\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                Salvando...\n              </>\n            ) : (\n              'Salva Metri'\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAVA;;;;;;;;;;AA8Be,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,aAAa,EACb,WAAW,EACX,SAAS,EACT,OAAO,EACmB;;IAC1B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ,eAAe;gBACzB;YACF;QACF;yCAAG;QAAC;QAAM;KAAc;IAExB,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,kCAAkC;YAClC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,CAAC,YAAY;gBACf,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,cAAc,CAAC;YACjD,QAAQ,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;YAEhC,2BAA2B;YAC3B,MAAM,cAAmC,CAAC;YAC1C,SAAS,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;gBAC3B,WAAW,CAAC,KAAK,OAAO,CAAC,GAAG;oBAC1B,iBAAiB,KAAK,eAAe,IAAI;oBACzC,0BAA0B;oBAC1B,cAAc;oBACd,cAAc;gBAChB;YACF;YACA,aAAa;QAEf,EAAE,OAAO,KAAU;YACjB,MAAM,WAAW,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;YAC9D,SAAS;YACT,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC,QAAgB,OAAe;QACxD,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAAO;oBACf,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,YAAY;YACZ,SAAS;YAET,4CAA4C;YAC5C,IAAI,WAAW;YACf,IAAI,cAAmB,CAAC;YAExB,IAAI,gBAAgB,QAAQ;gBAC1B,WAAW;gBACX,cAAc;oBAAE,WAAW;gBAAU;YACvC,OAAO,IAAI,gBAAgB,2BAA2B,gBAAgB,uBAAuB;gBAC3F,WAAW;gBACX,cAAc;oBAAE,mBAAmB;gBAAU;YAC/C;YAEA,MAAM,oHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,eAAe,UAAU;YAE5D,MAAM,aAAa,gBAAgB,SAC/B,uCACA;YAEJ,YAAY;YACZ,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA;QAEF,EAAE,OAAO,KAAU;YACjB,MAAM,WAAW,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;YAC9D,SAAS;YACT,UAAU;YACV,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB;;;;;;;sCAEH,6LAAC;4BAAE,WAAU;;gCACV;gCAAuB;gCAAa;;;;;;;;;;;;;gBAIxC,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAA8B;;;;;;2BAGjD,sBACF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBACxB;;;;;;2BAED,KAAK,MAAM,KAAK,kBAClB,6LAAC;oBAAI,WAAU;8BAAiC;;;;;yCAIhD,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,qBACT,6LAAC;4BAAuB,WAAU;;8CAChC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B,KAAK,OAAO;;;;;;8DACzD,6LAAC;oDAAE,WAAU;;wDACV,KAAK,SAAS;wDAAC;wDAAI,KAAK,UAAU;wDAAC;wDAAI,KAAK,iBAAiB;wDAAC;;;;;;;;;;;;;sDAGnE,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAS,KAAK,mBAAmB,KAAK,eAAe,YAAY;sDACrE,KAAK,mBAAmB;;;;;;;;;;;;8CAI7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAS,CAAC,MAAM,EAAE,KAAK,OAAO,EAAE;8DACpC,gBAAgB,SAAS,iBAAiB;;;;;;8DAE7C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAI,CAAC,MAAM,EAAE,KAAK,OAAO,EAAE;oDAC3B,MAAK;oDACL,KAAI;oDACJ,MAAK;oDACL,OAAO,SAAS,CAAC,KAAK,OAAO,CAAC,EAAE,mBAAmB;oDACnD,UAAU,CAAC,IAAM,kBAAkB,KAAK,OAAO,EAAE,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDAClG,WAAU;;;;;;;;;;;;wCAIb,gBAAgB,wBACf;;8DACE,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE;sEAAE;;;;;;sEAC3C,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAI,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE;4DAC7B,MAAK;4DACL,KAAI;4DACJ,OAAO,SAAS,CAAC,KAAK,OAAO,CAAC,EAAE,4BAA4B;4DAC5D,UAAU,CAAC,IAAM,kBAAkB,KAAK,OAAO,EAAE,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DACzG,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;sEAAE;;;;;;sEAChD,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAI,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;4DAClC,OAAO,SAAS,CAAC,KAAK,OAAO,CAAC,EAAE,gBAAgB;4DAChD,UAAU,CAAC,IAAM,kBAAkB,KAAK,OAAO,EAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/E,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;sEAAE;;;;;;sEAChD,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAI,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;4DAClC,OAAO,SAAS,CAAC,KAAK,OAAO,CAAC,EAAE,gBAAgB;4DAChD,UAAU,CAAC,IAAM,kBAAkB,KAAK,OAAO,EAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/E,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;2BA7Dd,KAAK,OAAO;;;;;;;;;;8BAwE5B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAU;;;;;;sCAGhE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,YAAY,KAAK,MAAM,KAAK;4BACtC,WAAU;sCAET,yBACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAOd;GArQwB;;QAaJ,+HAAA,CAAA,WAAQ;;;KAbJ", "debugId": null}}, {"offset": {"line": 4245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 4296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/comande/InserisciMetriPosatiDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alog<PERSON>ontent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, CheckCircle, AlertTriangle, Cable, AlertCircle, Wrench } from 'lucide-react'\nimport { comandeApi, parcoCaviApi } from '@/lib/api'\nimport { useToast } from '@/hooks/use-toast'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface Cavo {\n  id_cavo: string\n  tipologia: string\n  formazione: string\n  metratura_teorica: number\n  metratura_reale?: number\n  stato_installazione: string\n  id_bobina?: string\n}\n\ninterface <PERSON><PERSON> {\n  id_bobina: string\n  tipologia: string\n  sezione: string\n  metri_residui: number\n  stato: string\n}\n\ninterface DatiPosaCavo {\n  metratura_reale: number\n  numero_persone_impiegate: number\n  sistemazione: boolean\n  fascettatura: boolean\n  id_bobina: string\n  force_over?: boolean\n}\n\ninterface DatiPosa {\n  [cavoId: string]: DatiPosaCavo\n}\n\ninterface InserisciMetriPosatiDialogProps {\n  open: boolean\n  onClose: () => void\n  codiceComanda: string\n  onSuccess?: (message: string) => void\n  onError?: (error: string) => void\n}\n\nexport default function InserisciMetriPosatiDialog({\n  open,\n  onClose,\n  codiceComanda,\n  onSuccess,\n  onError\n}: InserisciMetriPosatiDialogProps) {\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [datiPosa, setDatiPosa] = useState<DatiPosa>({})\n  const [validationErrors, setValidationErrors] = useState<{[cavoId: string]: string}>({})\n  const [isLoading, setIsLoading] = useState(false)\n  const [isSaving, setIsSaving] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [selectedBobina, setSelectedBobina] = useState<string>('')\n  const [metriResiduiSimulati, setMetriResiduiSimulati] = useState<number>(0)\n  const [caviBloccati, setCaviBloccati] = useState<string[]>([])\n  const [cavoCheCausaOver, setCavoCheCausaOver] = useState<string | null>(null)\n  \n  const { toast } = useToast()\n  const { cantiere } = useAuth()\n\n  // Carica cavi della comanda\n  useEffect(() => {\n    if (open && codiceComanda) {\n      loadCaviComanda()\n    }\n  }, [open, codiceComanda])\n\n  // Carica bobine quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiere?.id_cantiere) {\n      loadBobine()\n    }\n  }, [open, cantiere?.id_cantiere])\n\n  // Reset quando si chiude il dialog\n  useEffect(() => {\n    if (!open) {\n      resetDialog()\n    }\n  }, [open])\n\n  const resetDialog = () => {\n    setCavi([])\n    setBobine([])\n    setDatiPosa({})\n    setValidationErrors({})\n    setError(null)\n    setSelectedBobina('')\n    setMetriResiduiSimulati(0)\n    setCaviBloccati([])\n    setCavoCheCausaOver(null)\n  }\n\n  const loadCaviComanda = async () => {\n    try {\n      setIsLoading(true)\n      setError(null)\n      \n      const response = await comandeApi.getCaviComanda(codiceComanda)\n      const caviData = response?.data || response || []\n      \n      // Filtra solo i cavi non installati per POSA\n      const caviNonInstallati = caviData.filter((cavo: Cavo) => \n        cavo.stato_installazione !== 'Installato'\n      )\n      \n      setCavi(caviNonInstallati)\n      \n      // Inizializza dati posa per ogni cavo\n      const initialDatiPosa: DatiPosa = {}\n      caviNonInstallati.forEach((cavo: Cavo) => {\n        initialDatiPosa[cavo.id_cavo] = {\n          metratura_reale: 0,\n          numero_persone_impiegate: 1,\n          sistemazione: false,\n          fascettatura: false,\n          id_bobina: 'BOBINA_VUOTA'\n        }\n      })\n      setDatiPosa(initialDatiPosa)\n      \n    } catch (error: any) {\n      setError('Errore durante il caricamento dei cavi della comanda')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loadBobine = async () => {\n    try {\n      if (!cantiere?.id_cantiere) return\n      \n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n      const bobineData = response?.data || response || []\n      \n      // Filtra solo bobine disponibili\n      const bobineDisponibili = bobineData.filter((bobina: Bobina) => \n        bobina.stato === 'disponibile' && bobina.metri_residui > 0\n      )\n      \n      setBobine(bobineDisponibili)\n      \n    } catch (error: any) {\n      setError('Errore durante il caricamento delle bobine')\n    }\n  }\n\n  const handleBobinaSelectionChange = (bobinaId: string) => {\n    setSelectedBobina(bobinaId)\n    \n    // Reset stati OVER\n    setCaviBloccati([])\n    setCavoCheCausaOver(null)\n    \n    // Aggiorna tutti i cavi con la nuova bobina\n    setDatiPosa(prev => {\n      const newDatiPosa = { ...prev }\n      Object.keys(newDatiPosa).forEach(cavoId => {\n        newDatiPosa[cavoId] = {\n          ...newDatiPosa[cavoId],\n          id_bobina: bobinaId\n        }\n      })\n      return newDatiPosa\n    })\n    \n    // Calcola metri residui iniziali\n    if (bobinaId && bobinaId !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === bobinaId)\n      if (bobina) {\n        setMetriResiduiSimulati(bobina.metri_residui)\n      }\n    } else {\n      setMetriResiduiSimulati(0)\n    }\n  }\n\n  const handleMetriChange = (cavoId: string, value: string) => {\n    const metri = parseFloat(value) || 0\n    \n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [cavoId]: {\n          ...prev[cavoId],\n          metratura_reale: metri\n        }\n      }\n      \n      // LOGICA OVER PROGRESSIVA\n      if (selectedBobina && selectedBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === selectedBobina)\n        if (bobina) {\n          // Calcola metri totali richiesti da tutti i cavi\n          let metriTotaliRichiesti = 0\n          Object.keys(newDatiPosa).forEach(id => {\n            const metri = newDatiPosa[id]?.metratura_reale || 0\n            if (metri > 0) {\n              metriTotaliRichiesti += metri\n            }\n          })\n          \n          // Aggiorna metri residui simulati\n          const nuoviMetriResidui = bobina.metri_residui - metriTotaliRichiesti\n          setMetriResiduiSimulati(nuoviMetriResidui)\n          \n          // Identifica il cavo che causa OVER e blocca i successivi\n          if (nuoviMetriResidui < 0 && !cavoCheCausaOver) {\n            setCavoCheCausaOver(cavoId)\n            \n            // Blocca tutti i cavi che non hanno ancora metri inseriti\n            const caviDaBloccare: string[] = []\n            Object.keys(newDatiPosa).forEach(id => {\n              const metri = newDatiPosa[id]?.metratura_reale || 0\n              if (metri === 0 && id !== cavoId) {\n                caviDaBloccare.push(id)\n              }\n            })\n            setCaviBloccati(caviDaBloccare)\n            \n          } else if (nuoviMetriResidui >= 0 && cavoCheCausaOver === cavoId) {\n            setCavoCheCausaOver(null)\n            setCaviBloccati([]) // Sblocca tutti i cavi\n          }\n        }\n      }\n      \n      return newDatiPosa\n    })\n    \n    // Validazione\n    validateMetri(cavoId, metri)\n  }\n\n  const validateMetri = (cavoId: string, metri: number) => {\n    const cavo = cavi.find(c => c.id_cavo === cavoId)\n    if (!cavo) return\n    \n    setValidationErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors[cavoId] // Rimuovi errore precedente\n      \n      if (metri > 0) {\n        // Warning se metri > teorici + 10% (non bloccante)\n        const soglia = cavo.metratura_teorica * 1.1\n        if (metri > soglia) {\n          // Non è un errore bloccante, solo un warning\n        }\n      }\n      \n      return newErrors\n    })\n  }\n\n  const handlePersoneChange = (cavoId: string, value: string) => {\n    const persone = parseInt(value) || 1\n    setDatiPosa(prev => ({\n      ...prev,\n      [cavoId]: {\n        ...prev[cavoId],\n        numero_persone_impiegate: persone\n      }\n    }))\n  }\n\n  const handleSistemazioneChange = (cavoId: string, checked: boolean) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [cavoId]: {\n        ...prev[cavoId],\n        sistemazione: checked\n      }\n    }))\n  }\n\n  const handleFascettaturaChange = (cavoId: string, checked: boolean) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [cavoId]: {\n        ...prev[cavoId],\n        fascettatura: checked\n      }\n    }))\n  }\n\n  const handleSave = async () => {\n    try {\n      setIsSaving(true)\n      setError(null)\n\n      // Validazione finale\n      const hasErrors = Object.keys(validationErrors).length > 0\n      if (hasErrors) {\n        setError('Correggere gli errori di validazione prima di salvare')\n        return\n      }\n\n      // Filtra solo i cavi con metri > 0 e imposta force_over se necessario\n      const datiPosaFiltrati: DatiPosa = {}\n      Object.keys(datiPosa).forEach(cavoId => {\n        const datiCavo = datiPosa[cavoId]\n        const metri = datiCavo?.metratura_reale || 0\n\n        if (metri > 0) {\n          // Imposta force_over se il cavo causa OVER o se la bobina è in stato OVER\n          const needsForceOver = cavoCheCausaOver === cavoId || metriResiduiSimulati < 0\n\n          datiPosaFiltrati[cavoId] = {\n            ...datiCavo,\n            id_bobina: selectedBobina || 'BOBINA_VUOTA',\n            force_over: needsForceOver\n          }\n        }\n      })\n\n      if (Object.keys(datiPosaFiltrati).length === 0) {\n        setError('Inserire almeno un metro per almeno un cavo')\n        return\n      }\n\n      console.log({\n        codiceComanda,\n        caviDaSalvare: Object.keys(datiPosaFiltrati).length,\n        datiPosaFiltrati,\n        selectedBobina,\n        metriResiduiSimulati,\n        cavoCheCausaOver\n      })\n\n      // Chiama API per aggiornare dati posa con bobine (POST endpoint)\n      const response = await fetch(`/api/comande/${codiceComanda}/dati-posa-bobine`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(datiPosaFiltrati)\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.detail || 'Errore durante il salvataggio')\n      }\n\n      const successMsg = `Metri posati inseriti con successo per ${Object.keys(datiPosaFiltrati).length} cavi`\n\n      onSuccess?.(successMsg)\n      toast({\n        title: \"Successo\",\n        description: successMsg,\n      })\n      onClose()\n\n    } catch (error: any) {\n      const errorMsg = error?.response?.data?.detail || 'Errore durante il salvataggio dei metri posati'\n      setError(errorMsg)\n      onError?.(errorMsg)\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Wrench className=\"h-5 w-5 text-blue-600\" />\n            Inserisci Metri Posati - Comanda {codiceComanda}\n          </DialogTitle>\n          <p className=\"text-sm text-gray-600\">\n            Inserisci i metri posati per ogni cavo della comanda POSA\n          </p>\n        </DialogHeader>\n\n        {error && (\n          <Alert variant=\"destructive\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        {isLoading ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n            Caricamento cavi...\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            {/* Selezione Bobina Principale */}\n            <div className=\"border rounded-lg p-4 bg-blue-50\">\n              <h3 className=\"font-medium text-blue-900 mb-3\">Selezione Bobina Principale</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"bobina-principale\">Bobina da Utilizzare</Label>\n                  <Select value={selectedBobina} onValueChange={handleBobinaSelectionChange}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Seleziona bobina principale...\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"BOBINA_VUOTA\">🔄 BOBINA_VUOTA (Assegna dopo)</SelectItem>\n                      {bobine.map((bobina) => (\n                        <SelectItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                          ✅ {bobina.id_bobina} - {bobina.tipologia} {bobina.sezione} ({bobina.metri_residui}m)\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n                \n                {selectedBobina && selectedBobina !== 'BOBINA_VUOTA' && (\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"text-sm\">\n                      <span className=\"font-medium\">Metri Residui: </span>\n                      <span className={metriResiduiSimulati < 0 ? 'text-red-600 font-bold' : 'text-green-600'}>\n                        {metriResiduiSimulati.toFixed(1)}m\n                      </span>\n                    </div>\n                    {metriResiduiSimulati < 0 && (\n                      <Badge variant=\"destructive\" className=\"text-xs\">\n                        OVER\n                      </Badge>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Lista Cavi */}\n            <div className=\"space-y-4\">\n              <h3 className=\"font-medium\">Cavi da Installare ({cavi.length})</h3>\n              \n              {cavi.map((cavo) => {\n                const datiCavo = datiPosa[cavo.id_cavo]\n                const hasError = validationErrors[cavo.id_cavo]\n                const isBloccato = caviBloccati.includes(cavo.id_cavo)\n                const causaOver = cavoCheCausaOver === cavo.id_cavo\n                const isOverState = metriResiduiSimulati < 0 && selectedBobina !== 'BOBINA_VUOTA'\n                \n                return (\n                  <div key={cavo.id_cavo} className={`border rounded-lg p-4 space-y-4 ${isBloccato ? 'bg-red-50 border-red-200' : causaOver ? 'bg-orange-50 border-orange-200' : ''}`}>\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h4 className=\"font-medium flex items-center gap-2\">\n                          {cavo.id_cavo}\n                          {isBloccato && (\n                            <Badge variant=\"destructive\" className=\"text-xs\">\n                              BLOCCATO\n                            </Badge>\n                          )}\n                          {causaOver && (\n                            <Badge variant=\"outline\" className=\"text-xs border-orange-500 text-orange-700\">\n                              CAUSA OVER\n                            </Badge>\n                          )}\n                        </h4>\n                        <p className=\"text-sm text-gray-600\">\n                          {cavo.tipologia} - {cavo.formazione} - {cavo.metratura_teorica}m teorici\n                        </p>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        <Badge variant={cavo.stato_installazione === 'Installato' ? 'default' : 'secondary'}>\n                          {cavo.stato_installazione}\n                        </Badge>\n                        {isOverState && (\n                          <Badge variant=\"destructive\" className=\"text-xs\">\n                            OVER\n                          </Badge>\n                        )}\n                      </div>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                      <div>\n                        <Label htmlFor={`metri-${cavo.id_cavo}`}>Metri Posati *</Label>\n                        <Input\n                          id={`metri-${cavo.id_cavo}`}\n                          type=\"number\"\n                          min=\"0\"\n                          step=\"0.1\"\n                          value={datiCavo?.metratura_reale || ''}\n                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                          className={hasError ? 'border-red-500' : isBloccato ? 'border-red-300 bg-red-50' : ''}\n                          placeholder={isBloccato ? 'Bloccato (OVER)' : '0.0'}\n                          disabled={isBloccato}\n                        />\n                        {hasError && (\n                          <p className=\"text-xs text-red-500 mt-1\">{hasError}</p>\n                        )}\n                        {isBloccato && (\n                          <p className=\"text-xs text-red-600 mt-1\">\n                            ⚠️ Cavo bloccato: bobina in stato OVER\n                          </p>\n                        )}\n                        {causaOver && !isBloccato && (\n                          <p className=\"text-xs text-orange-600 mt-1\">\n                            ⚠️ Questo cavo causa lo stato OVER della bobina\n                          </p>\n                        )}\n                      </div>\n                      \n                      <div>\n                        <Label htmlFor={`persone-${cavo.id_cavo}`}>Persone Impiegate</Label>\n                        <Input\n                          id={`persone-${cavo.id_cavo}`}\n                          type=\"number\"\n                          min=\"1\"\n                          value={datiCavo?.numero_persone_impiegate || 1}\n                          onChange={(e) => handlePersoneChange(cavo.id_cavo, e.target.value)}\n                        />\n                      </div>\n                      \n                      <div className=\"flex items-center space-x-2\">\n                        <Checkbox\n                          id={`sistemazione-${cavo.id_cavo}`}\n                          checked={datiCavo?.sistemazione || false}\n                          onCheckedChange={(checked) => handleSistemazioneChange(cavo.id_cavo, !!checked)}\n                        />\n                        <Label htmlFor={`sistemazione-${cavo.id_cavo}`}>Sistemazione</Label>\n                      </div>\n                      \n                      <div className=\"flex items-center space-x-2\">\n                        <Checkbox\n                          id={`fascettatura-${cavo.id_cavo}`}\n                          checked={datiCavo?.fascettatura || false}\n                          onCheckedChange={(checked) => handleFascettaturaChange(cavo.id_cavo, !!checked)}\n                        />\n                        <Label htmlFor={`fascettatura-${cavo.id_cavo}`}>Fascettatura</Label>\n                      </div>\n                    </div>\n                    \n                    {/* Mostra bobina assegnata */}\n                    <div className=\"text-sm text-gray-600\">\n                      <span className=\"font-medium\">Bobina assegnata: </span>\n                      <span className={selectedBobina === 'BOBINA_VUOTA' ? 'text-orange-600' : 'text-blue-600'}>\n                        {selectedBobina || 'Nessuna'}\n                      </span>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex justify-end gap-2 pt-4 border-t\">\n          <Button variant=\"outline\" onClick={onClose}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={isSaving || isLoading || cavi.length === 0}\n          >\n            {isSaving ? (\n              <>\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                Salvando...\n              </>\n            ) : (\n              <>\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Salva Metri Posati\n              </>\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;AA4De,SAAS,2BAA2B,EACjD,IAAI,EACJ,OAAO,EACP,aAAa,EACb,SAAS,EACT,OAAO,EACyB;;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,CAAC;IACpD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE3B,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,QAAQ,eAAe;gBACzB;YACF;QACF;+CAAG;QAAC;QAAM;KAAc;IAExB,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,QAAQ,UAAU,aAAa;gBACjC;YACF;QACF;+CAAG;QAAC;QAAM,UAAU;KAAY;IAEhC,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,CAAC,MAAM;gBACT;YACF;QACF;+CAAG;QAAC;KAAK;IAET,MAAM,cAAc;QAClB,QAAQ,EAAE;QACV,UAAU,EAAE;QACZ,YAAY,CAAC;QACb,oBAAoB,CAAC;QACrB,SAAS;QACT,kBAAkB;QAClB,wBAAwB;QACxB,gBAAgB,EAAE;QAClB,oBAAoB;IACtB;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,cAAc,CAAC;YACjD,MAAM,WAAW,UAAU,QAAQ,YAAY,EAAE;YAEjD,6CAA6C;YAC7C,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAC,OACzC,KAAK,mBAAmB,KAAK;YAG/B,QAAQ;YAER,sCAAsC;YACtC,MAAM,kBAA4B,CAAC;YACnC,kBAAkB,OAAO,CAAC,CAAC;gBACzB,eAAe,CAAC,KAAK,OAAO,CAAC,GAAG;oBAC9B,iBAAiB;oBACjB,0BAA0B;oBAC1B,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;YACA,YAAY;QAEd,EAAE,OAAO,OAAY;YACnB,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,IAAI,CAAC,UAAU,aAAa;YAE5B,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAClE,MAAM,aAAa,UAAU,QAAQ,YAAY,EAAE;YAEnD,iCAAiC;YACjC,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAC,SAC3C,OAAO,KAAK,KAAK,iBAAiB,OAAO,aAAa,GAAG;YAG3D,UAAU;QAEZ,EAAE,OAAO,OAAY;YACnB,SAAS;QACX;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,kBAAkB;QAElB,mBAAmB;QACnB,gBAAgB,EAAE;QAClB,oBAAoB;QAEpB,4CAA4C;QAC5C,YAAY,CAAA;YACV,MAAM,cAAc;gBAAE,GAAG,IAAI;YAAC;YAC9B,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;gBAC/B,WAAW,CAAC,OAAO,GAAG;oBACpB,GAAG,WAAW,CAAC,OAAO;oBACtB,WAAW;gBACb;YACF;YACA,OAAO;QACT;QAEA,iCAAiC;QACjC,IAAI,YAAY,aAAa,gBAAgB;YAC3C,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;YAChD,IAAI,QAAQ;gBACV,wBAAwB,OAAO,aAAa;YAC9C;QACF,OAAO;YACL,wBAAwB;QAC1B;IACF;IAEA,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,QAAQ,WAAW,UAAU;QAEnC,YAAY,CAAA;YACV,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAAO;oBACf,iBAAiB;gBACnB;YACF;YAEA,0BAA0B;YAC1B,IAAI,kBAAkB,mBAAmB,gBAAgB;gBACvD,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;gBAChD,IAAI,QAAQ;oBACV,iDAAiD;oBACjD,IAAI,uBAAuB;oBAC3B,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;wBAC/B,MAAM,QAAQ,WAAW,CAAC,GAAG,EAAE,mBAAmB;wBAClD,IAAI,QAAQ,GAAG;4BACb,wBAAwB;wBAC1B;oBACF;oBAEA,kCAAkC;oBAClC,MAAM,oBAAoB,OAAO,aAAa,GAAG;oBACjD,wBAAwB;oBAExB,0DAA0D;oBAC1D,IAAI,oBAAoB,KAAK,CAAC,kBAAkB;wBAC9C,oBAAoB;wBAEpB,0DAA0D;wBAC1D,MAAM,iBAA2B,EAAE;wBACnC,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;4BAC/B,MAAM,QAAQ,WAAW,CAAC,GAAG,EAAE,mBAAmB;4BAClD,IAAI,UAAU,KAAK,OAAO,QAAQ;gCAChC,eAAe,IAAI,CAAC;4BACtB;wBACF;wBACA,gBAAgB;oBAElB,OAAO,IAAI,qBAAqB,KAAK,qBAAqB,QAAQ;wBAChE,oBAAoB;wBACpB,gBAAgB,EAAE,EAAE,uBAAuB;;oBAC7C;gBACF;YACF;YAEA,OAAO;QACT;QAEA,cAAc;QACd,cAAc,QAAQ;IACxB;IAEA,MAAM,gBAAgB,CAAC,QAAgB;QACrC,MAAM,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QAC1C,IAAI,CAAC,MAAM;QAEX,oBAAoB,CAAA;YAClB,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,SAAS,CAAC,OAAO,CAAC,4BAA4B;;YAErD,IAAI,QAAQ,GAAG;gBACb,mDAAmD;gBACnD,MAAM,SAAS,KAAK,iBAAiB,GAAG;gBACxC,IAAI,QAAQ,QAAQ;gBAClB,6CAA6C;gBAC/C;YACF;YAEA,OAAO;QACT;IACF;IAEA,MAAM,sBAAsB,CAAC,QAAgB;QAC3C,MAAM,UAAU,SAAS,UAAU;QACnC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAAO;oBACf,0BAA0B;gBAC5B;YACF,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC,QAAgB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAAO;oBACf,cAAc;gBAChB;YACF,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC,QAAgB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAAO;oBACf,cAAc;gBAChB;YACF,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,YAAY;YACZ,SAAS;YAET,qBAAqB;YACrB,MAAM,YAAY,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG;YACzD,IAAI,WAAW;gBACb,SAAS;gBACT;YACF;YAEA,sEAAsE;YACtE,MAAM,mBAA6B,CAAC;YACpC,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAA;gBAC5B,MAAM,WAAW,QAAQ,CAAC,OAAO;gBACjC,MAAM,QAAQ,UAAU,mBAAmB;gBAE3C,IAAI,QAAQ,GAAG;oBACb,0EAA0E;oBAC1E,MAAM,iBAAiB,qBAAqB,UAAU,uBAAuB;oBAE7E,gBAAgB,CAAC,OAAO,GAAG;wBACzB,GAAG,QAAQ;wBACX,WAAW,kBAAkB;wBAC7B,YAAY;oBACd;gBACF;YACF;YAEA,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK,GAAG;gBAC9C,SAAS;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC;gBACV;gBACA,eAAe,OAAO,IAAI,CAAC,kBAAkB,MAAM;gBACnD;gBACA;gBACA;gBACA;YACF;YAEA,iEAAiE;YACjE,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,cAAc,iBAAiB,CAAC,EAAE;gBAC7E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;gBAC5D;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,aAAa,CAAC,uCAAuC,EAAE,OAAO,IAAI,CAAC,kBAAkB,MAAM,CAAC,KAAK,CAAC;YAExG,YAAY;YACZ,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA;QAEF,EAAE,OAAO,OAAY;YACnB,MAAM,WAAW,OAAO,UAAU,MAAM,UAAU;YAClD,SAAS;YACT,UAAU;QACZ,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAA0B;gCACV;;;;;;;sCAEpC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAKtC,uBACC,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC,oIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;gBAItB,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAA8B;;;;;;yCAInD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAgB,eAAe;;sEAC5C,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAe;;;;;;gEAChC,OAAO,GAAG,CAAC,CAAC,uBACX,6LAAC,qIAAA,CAAA,aAAU;wEAAwB,OAAO,OAAO,SAAS;;4EAAE;4EACvD,OAAO,SAAS;4EAAC;4EAAI,OAAO,SAAS;4EAAC;4EAAE,OAAO,OAAO;4EAAC;4EAAG,OAAO,aAAa;4EAAC;;uEADnE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;wCAQxC,kBAAkB,mBAAmB,gCACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAc;;;;;;sEAC9B,6LAAC;4DAAK,WAAW,uBAAuB,IAAI,2BAA2B;;gEACpE,qBAAqB,OAAO,CAAC;gEAAG;;;;;;;;;;;;;gDAGpC,uBAAuB,mBACtB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAU3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAc;wCAAqB,KAAK,MAAM;wCAAC;;;;;;;gCAE5D,KAAK,GAAG,CAAC,CAAC;oCACT,MAAM,WAAW,QAAQ,CAAC,KAAK,OAAO,CAAC;oCACvC,MAAM,WAAW,gBAAgB,CAAC,KAAK,OAAO,CAAC;oCAC/C,MAAM,aAAa,aAAa,QAAQ,CAAC,KAAK,OAAO;oCACrD,MAAM,YAAY,qBAAqB,KAAK,OAAO;oCACnD,MAAM,cAAc,uBAAuB,KAAK,mBAAmB;oCAEnE,qBACE,6LAAC;wCAAuB,WAAW,CAAC,gCAAgC,EAAE,aAAa,6BAA6B,YAAY,mCAAmC,IAAI;;0DACjK,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEACX,KAAK,OAAO;oEACZ,4BACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAc,WAAU;kFAAU;;;;;;oEAIlD,2BACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAA4C;;;;;;;;;;;;0EAKnF,6LAAC;gEAAE,WAAU;;oEACV,KAAK,SAAS;oEAAC;oEAAI,KAAK,UAAU;oEAAC;oEAAI,KAAK,iBAAiB;oEAAC;;;;;;;;;;;;;kEAGnE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,mBAAmB,KAAK,eAAe,YAAY;0EACrE,KAAK,mBAAmB;;;;;;4DAE1B,6BACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAc,WAAU;0EAAU;;;;;;;;;;;;;;;;;;0DAOvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,CAAC,MAAM,EAAE,KAAK,OAAO,EAAE;0EAAE;;;;;;0EACzC,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAI,CAAC,MAAM,EAAE,KAAK,OAAO,EAAE;gEAC3B,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,OAAO,UAAU,mBAAmB;gEACpC,UAAU,CAAC,IAAM,kBAAkB,KAAK,OAAO,EAAE,EAAE,MAAM,CAAC,KAAK;gEAC/D,WAAW,WAAW,mBAAmB,aAAa,6BAA6B;gEACnF,aAAa,aAAa,oBAAoB;gEAC9C,UAAU;;;;;;4DAEX,0BACC,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;4DAE3C,4BACC,6LAAC;gEAAE,WAAU;0EAA4B;;;;;;4DAI1C,aAAa,CAAC,4BACb,6LAAC;gEAAE,WAAU;0EAA+B;;;;;;;;;;;;kEAMhD,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE;0EAAE;;;;;;0EAC3C,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAI,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE;gEAC7B,MAAK;gEACL,KAAI;gEACJ,OAAO,UAAU,4BAA4B;gEAC7C,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAIrE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAI,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;gEAClC,SAAS,UAAU,gBAAgB;gEACnC,iBAAiB,CAAC,UAAY,yBAAyB,KAAK,OAAO,EAAE,CAAC,CAAC;;;;;;0EAEzE,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;0EAAE;;;;;;;;;;;;kEAGlD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAI,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;gEAClC,SAAS,UAAU,gBAAgB;gEACnC,iBAAiB,CAAC,UAAY,yBAAyB,KAAK,OAAO,EAAE,CAAC,CAAC;;;;;;0EAEzE,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,CAAC,aAAa,EAAE,KAAK,OAAO,EAAE;0EAAE;;;;;;;;;;;;;;;;;;0DAKpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;wDAAK,WAAW,mBAAmB,iBAAiB,oBAAoB;kEACtE,kBAAkB;;;;;;;;;;;;;uCA/Ff,KAAK,OAAO;;;;;gCAoG1B;;;;;;;;;;;;;8BAKN,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAS;;;;;;sCAG5C,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,YAAY,aAAa,KAAK,MAAM,KAAK;sCAElD,yBACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GAnhBwB;;QAmBJ,+HAAA,CAAA,WAAQ;QACL,kIAAA,CAAA,UAAO;;;KApBN", "debugId": null}}, {"offset": {"line": 5221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/app/comande/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { getComandaColorClasses } from '@/utils/softColors'\nimport { Progress } from '@/components/ui/progress'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { comandeApi, responsabiliApi } from '@/lib/api'\nimport { Comanda, Responsabile } from '@/types'\nimport CreaComandaDialog from '@/components/comande/CreaComandaDialog'\nimport GestisciResponsabiliDialog from '@/components/comande/GestisciResponsabiliDialog'\nimport DettagliComandaDialog from '@/components/comande/DettagliComandaDialog'\nimport InserisciMetriDialog from '@/components/comande/InserisciMetriDialog'\nimport InserisciMetriPosatiDialog from '@/components/comande/InserisciMetriPosatiDialog'\nimport { useToast } from '@/hooks/use-toast'\nimport {\n  ClipboardList,\n  Plus,\n  Users,\n  Clock,\n  CheckCircle,\n  AlertTriangle,\n  Play,\n  Pause,\n  Square,\n  Eye,\n  Edit,\n  Trash2,\n  Loader2,\n  Search,\n  Filter,\n  Settings\n} from 'lucide-react'\n\nexport default function ComandePage() {\n  const [selectedTab, setSelectedTab] = useState('active')\n  const [comande, setComande] = useState<Comanda[]>([])\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedResponsabile, setSelectedResponsabile] = useState('all')\n  const [selectedTipo, setSelectedTipo] = useState('all')\n  const [showCreaComandaDialog, setShowCreaComandaDialog] = useState(false)\n  const [showResponsabiliDialog, setShowResponsabiliDialog] = useState(false)\n  const [showDettagliDialog, setShowDettagliDialog] = useState(false)\n  const [showInserisciMetriDialog, setShowInserisciMetriDialog] = useState(false)\n  const [showInserisciMetriPosatiDialog, setShowInserisciMetriPosatiDialog] = useState(false)\n  const [selectedComandaCode, setSelectedComandaCode] = useState<string | null>(null)\n  const [selectedComandaTipo, setSelectedComandaTipo] = useState<'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | null>(null)\n\n  const { user, cantiere } = useAuth()\n  const { toast } = useToast()\n\n  // Get cantiere ID con fallback al localStorage come nelle altre pagine\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n\n    }\n  }, [cantiere])\n\n  // Carica comande e responsabili dal backend\n  useEffect(() => {\n    if (cantiereId && cantiereId > 0) {\n      loadData()\n    }\n  }, [cantiereId])\n\n  const loadData = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      if (!cantiereId || cantiereId <= 0) {\n        setError('Cantiere non selezionato')\n        return\n      }\n\n      const [comandeResponse, responsabiliData] = await Promise.all([\n        comandeApi.getComande(cantiereId),\n        responsabiliApi.getResponsabili(cantiereId)\n      ])\n\n      // Extract comande array from the response object\n      const comandeData = comandeResponse?.data?.comande || comandeResponse?.comande || comandeResponse?.data || comandeResponse || []\n\n      // Extract responsabili array from the response object\n      const responsabiliArray = responsabiliData?.data || responsabiliData || []\n\n      // Ensure we always set arrays\n      setComande(Array.isArray(comandeData) ? comandeData : [])\n      setResponsabili(Array.isArray(responsabiliArray) ? responsabiliArray : [])\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Funzioni per gestire i dialog\n  const handleSuccess = (message: string) => {\n    toast({\n      title: \"Successo\",\n      description: message,\n    })\n    loadData() // Ricarica i dati\n  }\n\n  const handleError = (message: string) => {\n    toast({\n      title: \"Errore\",\n      description: message,\n      variant: \"destructive\",\n    })\n  }\n\n  const handleDeleteComanda = async (codiceComanda: string) => {\n    if (!confirm(`Sei sicuro di voler eliminare la comanda ${codiceComanda}?`)) {\n      return\n    }\n\n    try {\n      setIsLoading(true)\n      await comandeApi.deleteComanda(cantiereId, codiceComanda)\n      handleSuccess(`Comanda ${codiceComanda} eliminata con successo`)\n    } catch (error: any) {\n      handleError('Errore durante l\\'eliminazione della comanda')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleCambiaStato = async (codiceComanda: string, nuovoStato: string) => {\n    try {\n      setIsLoading(true)\n      await comandeApi.cambiaStato(cantiereId, codiceComanda, nuovoStato)\n      handleSuccess(`Stato della comanda ${codiceComanda} cambiato in ${nuovoStato}`)\n    } catch (error: any) {\n      handleError('Errore durante il cambio di stato')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getStatusBadge = (stato: string) => {\n    switch (stato) {\n      case 'COMPLETATA':\n        return <Badge className=\"bg-green-100 text-green-800\">Completata</Badge>\n      case 'IN_CORSO':\n        return <Badge className=\"bg-blue-100 text-blue-800\">In Corso</Badge>\n      case 'ASSEGNATA':\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">Assegnata</Badge>\n      case 'CREATA':\n        return <Badge className=\"bg-gray-100 text-gray-800\">Creata</Badge>\n      case 'ANNULLATA':\n        return <Badge className=\"bg-red-100 text-red-800\">Annullata</Badge>\n      default:\n        return <Badge variant=\"secondary\">{stato}</Badge>\n    }\n  }\n\n  const getTipoBadge = (tipo: string) => {\n    const colorClasses = getComandaColorClasses(tipo)\n\n    const tipoLabels: { [key: string]: string } = {\n      'POSA': '🔧 Posa',\n      'COLLEGAMENTO_PARTENZA': '🔌 Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': '⚡ Coll. Arrivo',\n      'CERTIFICAZIONE': '📋 Certificazione'\n    }\n\n    return (\n      <Badge className={colorClasses.badge}>\n        {tipoLabels[tipo] || tipo.replace(/_/g, ' ')}\n      </Badge>\n    )\n  }\n\n  const filteredComande = Array.isArray(comande) ? comande.filter(comanda => {\n    // Filtro per tab\n    let passesTabFilter = true\n    switch (selectedTab) {\n      case 'active':\n        passesTabFilter = comanda.stato === 'IN_CORSO' || comanda.stato === 'ASSEGNATA' || comanda.stato === 'CREATA'\n        break\n      case 'completed':\n        passesTabFilter = comanda.stato === 'COMPLETATA'\n        break\n      case 'all':\n        passesTabFilter = true\n        break\n      default:\n        passesTabFilter = true\n    }\n\n    // Filtro per ricerca\n    const passesSearchFilter = searchTerm === '' ||\n      comanda.codice_comanda.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (comanda.descrizione && comanda.descrizione.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (comanda.responsabile && comanda.responsabile.toLowerCase().includes(searchTerm.toLowerCase()))\n\n    // Filtro per responsabile\n    const passesResponsabileFilter = selectedResponsabile === 'all' || comanda.responsabile === selectedResponsabile\n\n    // Filtro per tipo\n    const passesTipoFilter = selectedTipo === 'all' || comanda.tipo_comanda === selectedTipo\n\n    return passesTabFilter && passesSearchFilter && passesResponsabileFilter && passesTipoFilter\n  }) : []\n\n  const stats = {\n    totali: Array.isArray(comande) ? comande.length : 0,\n    in_corso: Array.isArray(comande) ? comande.filter(c => c.stato === 'IN_CORSO').length : 0,\n    completate: Array.isArray(comande) ? comande.filter(c => c.stato === 'COMPLETATA').length : 0,\n    pianificate: Array.isArray(comande) ? comande.filter(c => c.stato === 'CREATA' || c.stato === 'ASSEGNATA').length : 0,\n    filtrate: filteredComande.length\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n\n        {/* Header semplice */}\n        <div className=\"mb-6\">\n          <h1 className=\"text-2xl font-bold text-slate-900 mb-2\">Gestione Comande</h1>\n          <p className=\"text-slate-600\">\n            {cantiereId > 0 ? `Cantiere ${typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') || cantiereId : cantiereId}` : 'Nessun cantiere selezionato'}\n          </p>\n        </div>\n\n        {/* Campo di ricerca come nella webapp originale */}\n        <div className=\"mb-6\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <Input\n              placeholder=\"Cerca per codice, responsabile, tipo, stato o descrizione...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 bg-gray-50 hover:bg-blue-50 focus:bg-white transition-colors\"\n            />\n          </div>\n        </div>\n\n        {/* Toolbar con pulsanti come nella webapp originale */}\n        <div className=\"flex flex-wrap gap-2 mb-6\">\n          <Button\n            onClick={() => setShowCreaComandaDialog(true)}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n          >\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Nuova Comanda\n          </Button>\n          <Button\n            variant=\"outline\"\n            onClick={() => {\n              // TODO: Implementare assegnazione cavi\n              toast({\n                title: \"Funzione in sviluppo\",\n                description: \"L'assegnazione cavi sarà disponibile presto\",\n              })\n            }}\n            disabled={filteredComande.length === 0}\n          >\n            <ClipboardList className=\"h-4 w-4 mr-2\" />\n            Assegna Cavi\n          </Button>\n          <Button\n            variant=\"outline\"\n            onClick={() => setShowResponsabiliDialog(true)}\n          >\n            <Users className=\"h-4 w-4 mr-2\" />\n            Gestisci Responsabili\n          </Button>\n        </div>\n\n        {/* Header con conteggio comande */}\n        <div className=\"mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            Elenco Comande ({filteredComande.length} di {stats.totali})\n          </h3>\n        </div>\n\n        {/* Tabella comande come nella webapp originale */}\n        <Card className=\"border border-gray-200 rounded-lg\">\n          <CardContent className=\"p-0\">\n            <Table>\n              <TableHeader>\n                <TableRow className=\"bg-gray-50\">\n                  <TableHead className=\"font-semibold\">Codice</TableHead>\n                  <TableHead className=\"font-semibold\">Tipo</TableHead>\n                  <TableHead className=\"font-semibold\">Responsabile</TableHead>\n                  <TableHead className=\"font-semibold\">Contatti</TableHead>\n                  <TableHead className=\"font-semibold\">Stato</TableHead>\n                  <TableHead className=\"font-semibold\">Data Creazione</TableHead>\n                  <TableHead className=\"font-semibold text-center\">Cavi</TableHead>\n                  <TableHead className=\"font-semibold text-center\">Completamento</TableHead>\n                  <TableHead className=\"font-semibold text-center\">Azioni</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {isLoading ? (\n                  <TableRow>\n                    <TableCell colSpan={9} className=\"text-center py-8\">\n                      <div className=\"flex items-center justify-center gap-2\">\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        Caricamento comande...\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ) : error ? (\n                  <TableRow>\n                    <TableCell colSpan={9} className=\"text-center py-8\">\n                      <div className=\"flex items-center justify-center gap-2 text-red-600\">\n                        <AlertTriangle className=\"h-4 w-4\" />\n                        {error}\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ) : filteredComande.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={9} className=\"text-center py-8 text-slate-500\">\n                      Nessuna comanda trovata\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  filteredComande.map((comanda) => (\n                    <TableRow key={comanda.codice_comanda} className=\"hover:bg-gray-50\">\n                      <TableCell>\n                        <div className=\"font-semibold text-blue-600\">\n                          {comanda.codice_comanda}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {getTipoBadge(comanda.tipo_comanda)}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"font-medium\">\n                          {comanda.responsabile || 'Non assegnato'}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm text-gray-600\">\n                          {comanda.responsabile_telefono && (\n                            <div>📞 {comanda.responsabile_telefono}</div>\n                          )}\n                          {comanda.responsabile_email && (\n                            <div>✉️ {comanda.responsabile_email}</div>\n                          )}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {getStatusBadge(comanda.stato)}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center\">\n                        <div className=\"font-semibold text-blue-600\">\n                          {comanda.numero_cavi_assegnati || 0}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center\">\n                        <div className=\"font-semibold\">\n                          {(comanda.percentuale_completamento || 0).toFixed(1)}%\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center\">\n                        <div className=\"flex gap-1 justify-center\">\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => {\n                              setSelectedComandaCode(comanda.codice_comanda)\n                              setShowDettagliDialog(true)\n                            }}\n                            title=\"Visualizza\"\n                          >\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => {\n                              // TODO: Implementare modifica comanda\n                              toast({\n                                title: \"Funzione in sviluppo\",\n                                description: \"La modifica comande sarà disponibile presto\",\n                              })\n                            }}\n                            title=\"Modifica\"\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          {['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda) && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedComandaCode(comanda.codice_comanda)\n                                setSelectedComandaTipo(comanda.tipo_comanda as 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO')\n\n                                // Apri il dialog corretto in base al tipo di comanda\n                                if (comanda.tipo_comanda === 'POSA') {\n                                  setShowInserisciMetriPosatiDialog(true)\n                                } else {\n                                  setShowInserisciMetriDialog(true)\n                                }\n                              }}\n                              title={comanda.tipo_comanda === 'POSA' ? 'Inserisci Metri Posati' : 'Inserisci Metri Collegati'}\n                            >\n                              <ClipboardList className=\"h-4 w-4\" />\n                            </Button>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleDeleteComanda(comanda.codice_comanda)}\n                            disabled={isLoading}\n                            className=\"text-red-600 hover:text-red-700\"\n                            title=\"Elimina\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </CardContent>\n        </Card>\n\n      </div>\n\n      {/* Dialog per creare comanda */}\n      <CreaComandaDialog\n        open={showCreaComandaDialog}\n        onClose={() => setShowCreaComandaDialog(false)}\n        onSuccess={handleSuccess}\n        onError={handleError}\n        onComandaCreated={() => loadData()}\n      />\n\n      {/* Dialog per gestire responsabili */}\n      <GestisciResponsabiliDialog\n        open={showResponsabiliDialog}\n        onClose={() => setShowResponsabiliDialog(false)}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n\n      {/* Dialog per dettagli comanda */}\n      <DettagliComandaDialog\n        open={showDettagliDialog}\n        onClose={() => {\n          setShowDettagliDialog(false)\n          setSelectedComandaCode(null)\n        }}\n        codiceComanda={selectedComandaCode}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n\n      <InserisciMetriDialog\n        open={showInserisciMetriDialog}\n        onClose={() => {\n          setShowInserisciMetriDialog(false)\n          setSelectedComandaCode(null)\n          setSelectedComandaTipo(null)\n        }}\n        codiceComanda={selectedComandaCode || ''}\n        tipoComanda={selectedComandaTipo || 'POSA'}\n        onSuccess={(message) => {\n          handleSuccess(message)\n          loadComande() // Ricarica le comande per aggiornare i dati\n        }}\n        onError={handleError}\n      />\n\n      <InserisciMetriPosatiDialog\n        open={showInserisciMetriPosatiDialog}\n        onClose={() => {\n          setShowInserisciMetriPosatiDialog(false)\n          setSelectedComandaCode(null)\n          setSelectedComandaTipo(null)\n        }}\n        codiceComanda={selectedComandaCode || ''}\n        onSuccess={(message) => {\n          handleSuccess(message)\n          loadComande() // Ricarica le comande per aggiornare i dati\n        }}\n        onError={handleError}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAnBA;;;;;;;;;;;;;;;;;AAsCe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,gCAAgC,kCAAkC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmE;IAEhI,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,uEAAuE;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,UAAU,eAAe,SAAS,aAAa,OAAO,CAAC,yBAAyB;gBACjG,cAAc;YAEhB;QACF;gCAAG;QAAC;KAAS;IAEb,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,cAAc,aAAa,GAAG;gBAChC;YACF;QACF;gCAAG;QAAC;KAAW;IAEf,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,SAAS;gBACT;YACF;YAEA,MAAM,CAAC,iBAAiB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC5D,oHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;gBACtB,oHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;aACjC;YAED,iDAAiD;YACjD,MAAM,cAAc,iBAAiB,MAAM,WAAW,iBAAiB,WAAW,iBAAiB,QAAQ,mBAAmB,EAAE;YAEhI,sDAAsD;YACtD,MAAM,oBAAoB,kBAAkB,QAAQ,oBAAoB,EAAE;YAE1E,8BAA8B;YAC9B,WAAW,MAAM,OAAO,CAAC,eAAe,cAAc,EAAE;YACxD,gBAAgB,MAAM,OAAO,CAAC,qBAAqB,oBAAoB,EAAE;QAC3E,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,gCAAgC;IAChC,MAAM,gBAAgB,CAAC;QACrB,MAAM;YACJ,OAAO;YACP,aAAa;QACf;QACA,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,CAAC,yCAAyC,EAAE,cAAc,CAAC,CAAC,GAAG;YAC1E;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,oHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,YAAY;YAC3C,cAAc,CAAC,QAAQ,EAAE,cAAc,uBAAuB,CAAC;QACjE,EAAE,OAAO,OAAY;YACnB,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,OAAO,eAAuB;QACtD,IAAI;YACF,aAAa;YACb,MAAM,oHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,YAAY,eAAe;YACxD,cAAc,CAAC,oBAAoB,EAAE,cAAc,aAAa,EAAE,YAAY;QAChF,EAAE,OAAO,OAAY;YACnB,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA0B;;;;;;YACpD;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,eAAe,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD,EAAE;QAE5C,MAAM,aAAwC;YAC5C,QAAQ;YACR,yBAAyB;YACzB,uBAAuB;YACvB,kBAAkB;QACpB;QAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,WAAW,aAAa,KAAK;sBACjC,UAAU,CAAC,KAAK,IAAI,KAAK,OAAO,CAAC,MAAM;;;;;;IAG9C;IAEA,MAAM,kBAAkB,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,CAAC,CAAA;QAC9D,iBAAiB;QACjB,IAAI,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,kBAAkB,QAAQ,KAAK,KAAK,cAAc,QAAQ,KAAK,KAAK,eAAe,QAAQ,KAAK,KAAK;gBACrG;YACF,KAAK;gBACH,kBAAkB,QAAQ,KAAK,KAAK;gBACpC;YACF,KAAK;gBACH,kBAAkB;gBAClB;YACF;gBACE,kBAAkB;QACtB;QAEA,qBAAqB;QACrB,MAAM,qBAAqB,eAAe,MACxC,QAAQ,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxF,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE7F,0BAA0B;QAC1B,MAAM,2BAA2B,yBAAyB,SAAS,QAAQ,YAAY,KAAK;QAE5F,kBAAkB;QAClB,MAAM,mBAAmB,iBAAiB,SAAS,QAAQ,YAAY,KAAK;QAE5E,OAAO,mBAAmB,sBAAsB,4BAA4B;IAC9E,KAAK,EAAE;IAEP,MAAM,QAAQ;QACZ,QAAQ,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,GAAG;QAClD,UAAU,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,YAAY,MAAM,GAAG;QACxF,YAAY,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,cAAc,MAAM,GAAG;QAC5F,aAAa,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,KAAK,aAAa,MAAM,GAAG;QACpH,UAAU,gBAAgB,MAAM;IAClC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,aAAa,IAAI,CAAC,SAAS,EAAE,uCAAgC,aAAa,OAAO,CAAC,2BAA2B,mDAAyB,GAAG;;;;;;;;;;;;kCAK9I,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;kCAMhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,yBAAyB;gCACxC,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;oCACP,uCAAuC;oCACvC,MAAM;wCACJ,OAAO;wCACP,aAAa;oCACf;gCACF;gCACA,UAAU,gBAAgB,MAAM,KAAK;;kDAErC,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG5C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,0BAA0B;;kDAEzC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsC;gCACjC,gBAAgB,MAAM;gCAAC;gCAAK,MAAM,MAAM;gCAAC;;;;;;;;;;;;kCAK9D,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAgB;;;;;;8DACrC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAgB;;;;;;8DACrC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAgB;;;;;;8DACrC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAgB;;;;;;8DACrC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAgB;;;;;;8DACrC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAgB;;;;;;8DACrC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA4B;;;;;;8DACjD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA4B;;;;;;8DACjD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA4B;;;;;;;;;;;;;;;;;kDAGrD,6LAAC,oIAAA,CAAA,YAAS;kDACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAC/B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;;;;;;;;;;mDAKhD,sBACF,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAC/B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDACxB;;;;;;;;;;;;;;;;mDAIL,gBAAgB,MAAM,KAAK,kBAC7B,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAAkC;;;;;;;;;;mDAKrE,gBAAgB,GAAG,CAAC,CAAC,wBACnB,6LAAC,oIAAA,CAAA,WAAQ;gDAA8B,WAAU;;kEAC/C,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,cAAc;;;;;;;;;;;kEAG3B,6LAAC,oIAAA,CAAA,YAAS;kEACP,aAAa,QAAQ,YAAY;;;;;;kEAEpC,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,YAAY,IAAI;;;;;;;;;;;kEAG7B,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,qBAAqB,kBAC5B,6LAAC;;wEAAI;wEAAI,QAAQ,qBAAqB;;;;;;;gEAEvC,QAAQ,kBAAkB,kBACzB,6LAAC;;wEAAI;wEAAI,QAAQ,kBAAkB;;;;;;;;;;;;;;;;;;kEAIzC,6LAAC,oIAAA,CAAA,YAAS;kEACP,eAAe,QAAQ,KAAK;;;;;;kEAE/B,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;sEACZ,IAAI,KAAK,QAAQ,cAAc,EAAE,kBAAkB,CAAC;;;;;;;;;;;kEAGzD,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,qBAAqB,IAAI;;;;;;;;;;;kEAGtC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,CAAC,QAAQ,yBAAyB,IAAI,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;kEAGzD,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;wEACP,uBAAuB,QAAQ,cAAc;wEAC7C,sBAAsB;oEACxB;oEACA,OAAM;8EAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;wEACP,sCAAsC;wEACtC,MAAM;4EACJ,OAAO;4EACP,aAAa;wEACf;oEACF;oEACA,OAAM;8EAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;gEAEjB;oEAAC;oEAAQ;oEAAyB;iEAAsB,CAAC,QAAQ,CAAC,QAAQ,YAAY,mBACrF,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;wEACP,uBAAuB,QAAQ,cAAc;wEAC7C,uBAAuB,QAAQ,YAAY;wEAE3C,qDAAqD;wEACrD,IAAI,QAAQ,YAAY,KAAK,QAAQ;4EACnC,kCAAkC;wEACpC,OAAO;4EACL,4BAA4B;wEAC9B;oEACF;oEACA,OAAO,QAAQ,YAAY,KAAK,SAAS,2BAA2B;8EAEpE,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;;;;;;8EAG7B,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,oBAAoB,QAAQ,cAAc;oEACzD,UAAU;oEACV,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAjGX,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgHnD,6LAAC,qJAAA,CAAA,UAAiB;gBAChB,MAAM;gBACN,SAAS,IAAM,yBAAyB;gBACxC,WAAW;gBACX,SAAS;gBACT,kBAAkB,IAAM;;;;;;0BAI1B,6LAAC,8JAAA,CAAA,UAA0B;gBACzB,MAAM;gBACN,SAAS,IAAM,0BAA0B;gBACzC,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,yJAAA,CAAA,UAAqB;gBACpB,MAAM;gBACN,SAAS;oBACP,sBAAsB;oBACtB,uBAAuB;gBACzB;gBACA,eAAe;gBACf,WAAW;gBACX,SAAS;;;;;;0BAGX,6LAAC,wJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS;oBACP,4BAA4B;oBAC5B,uBAAuB;oBACvB,uBAAuB;gBACzB;gBACA,eAAe,uBAAuB;gBACtC,aAAa,uBAAuB;gBACpC,WAAW,CAAC;oBACV,cAAc;oBACd,cAAc,4CAA4C;;gBAC5D;gBACA,SAAS;;;;;;0BAGX,6LAAC,8JAAA,CAAA,UAA0B;gBACzB,MAAM;gBACN,SAAS;oBACP,kCAAkC;oBAClC,uBAAuB;oBACvB,uBAAuB;gBACzB;gBACA,eAAe,uBAAuB;gBACtC,WAAW,CAAC;oBACV,cAAc;oBACd,cAAc,4CAA4C;;gBAC5D;gBACA,SAAS;;;;;;;;;;;;AAIjB;GApdwB;;QAiBK,kIAAA,CAAA,UAAO;QAChB,+HAAA,CAAA,WAAQ;;;KAlBJ", "debugId": null}}]}