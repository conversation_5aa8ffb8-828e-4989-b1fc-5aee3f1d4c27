/**
 * Componenti di Accessibilità per CABLYS Admin
 * Garantisce conformità WCAG 2.1 AA
 */

import React, { useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'

// Skip Link per navigazione da tastiera
interface SkipLinkProps {
  href: string
  children: React.ReactNode
  className?: string
}

export const SkipLink: React.FC<SkipLinkProps> = ({ href, children, className }) => {
  return (
    <a
      href={href}
      className={cn(
        'skip-link',
        'absolute -top-10 left-4 z-50 bg-blue-600 text-white px-4 py-2 rounded',
        'focus:top-4 transition-all duration-200',
        'font-medium text-sm',
        className
      )}
    >
      {children}
    </a>
  )
}

// Screen Reader Only Text
interface ScreenReaderOnlyProps {
  children: React.ReactNode
}

export const ScreenReaderOnly: React.FC<ScreenReaderOnlyProps> = ({ children }) => {
  return (
    <span className="sr-only">
      {children}
    </span>
  )
}

// Focus Trap per modali e dropdown
interface FocusTrapProps {
  children: React.ReactNode
  active?: boolean
  className?: string
}

export const FocusTrap: React.FC<FocusTrapProps> = ({ children, active = true, className }) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const firstFocusableRef = useRef<HTMLElement>(null)
  const lastFocusableRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!active || !containerRef.current) return

    const container = containerRef.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )

    if (focusableElements.length === 0) return

    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    firstFocusableRef.current = firstElement
    lastFocusableRef.current = lastElement

    // Focus sul primo elemento
    firstElement.focus()

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            e.preventDefault()
            lastElement.focus()
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            e.preventDefault()
            firstElement.focus()
          }
        }
      }
    }

    container.addEventListener('keydown', handleKeyDown)

    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }, [active])

  return (
    <div ref={containerRef} className={cn('focus-trap', className)}>
      {children}
    </div>
  )
}

// Indicatore di Progresso Accessibile
interface ProgressIndicatorProps {
  value: number
  max?: number
  label?: string
  className?: string
  showPercentage?: boolean
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  value,
  max = 100,
  label,
  className,
  showPercentage = true
}) => {
  const percentage = Math.round((value / max) * 100)

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className="flex justify-between items-center text-sm">
          <span className="font-medium text-slate-700">{label}</span>
          {showPercentage && (
            <span className="text-slate-600">{percentage}%</span>
          )}
        </div>
      )}
      <div
        className="progress-indicator h-2"
        role="progressbar"
        aria-valuenow={percentage}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label={label || 'Progresso'}
      >
        <div
          className="progress-bar"
          style={{ width: `${percentage}%` }}
        />
      </div>
      <ScreenReaderOnly>
        {label ? `${label}: ` : ''}Progresso {percentage}% completato
      </ScreenReaderOnly>
    </div>
  )
}

// Messaggio di Stato Accessibile
interface StatusMessageProps {
  type: 'success' | 'error' | 'warning' | 'info'
  children: React.ReactNode
  className?: string
  live?: 'polite' | 'assertive'
}

export const StatusMessage: React.FC<StatusMessageProps> = ({
  type,
  children,
  className,
  live = 'polite'
}) => {
  const baseClasses = 'p-4 rounded-lg flex items-center gap-3'
  const typeClasses = {
    success: 'bg-green-50 border border-green-200 text-green-800',
    error: 'bg-red-50 border border-red-200 text-red-800',
    warning: 'bg-yellow-50 border border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border border-blue-200 text-blue-800'
  }

  return (
    <div
      className={cn(baseClasses, typeClasses[type], className)}
      role="alert"
      aria-live={live}
    >
      {children}
    </div>
  )
}

// Tooltip Accessibile
interface TooltipProps {
  content: string
  children: React.ReactNode
  className?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
}

export const AccessibleTooltip: React.FC<TooltipProps> = ({
  content,
  children,
  className,
  position = 'top'
}) => {
  const tooltipId = React.useId()

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  }

  return (
    <div className="relative group">
      <div
        aria-describedby={tooltipId}
        className="cursor-help"
      >
        {children}
      </div>
      <div
        id={tooltipId}
        role="tooltip"
        className={cn(
          'absolute z-50 px-2 py-1 text-xs text-white bg-slate-900 rounded',
          'opacity-0 group-hover:opacity-100 group-focus-within:opacity-100',
          'transition-opacity duration-200 pointer-events-none whitespace-nowrap',
          positionClasses[position],
          className
        )}
      >
        {content}
      </div>
    </div>
  )
}

// Heading con livello semantico corretto
interface HeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6
  children: React.ReactNode
  className?: string
}

export const SemanticHeading: React.FC<HeadingProps> = ({ level, children, className }) => {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements

  const defaultClasses = {
    1: 'text-3xl font-bold text-slate-900',
    2: 'text-2xl font-semibold text-slate-900',
    3: 'text-xl font-semibold text-slate-900',
    4: 'text-lg font-medium text-slate-900',
    5: 'text-base font-medium text-slate-900',
    6: 'text-sm font-medium text-slate-900'
  }

  return (
    <Tag className={cn(defaultClasses[level], className)}>
      {children}
    </Tag>
  )
}

// Landmark per navigazione
interface LandmarkProps {
  role: 'main' | 'navigation' | 'banner' | 'contentinfo' | 'complementary' | 'region'
  label?: string
  children: React.ReactNode
  className?: string
}

export const Landmark: React.FC<LandmarkProps> = ({ role, label, children, className }) => {
  return (
    <div
      role={role}
      aria-label={label}
      className={className}
    >
      {children}
    </div>
  )
}

// Esportazione di tutte le utilità
export const AccessibilityUtils = {
  SkipLink,
  ScreenReaderOnly,
  FocusTrap,
  ProgressIndicator,
  StatusMessage,
  AccessibleTooltip,
  SemanticHeading,
  Landmark
}

export default AccessibilityUtils
