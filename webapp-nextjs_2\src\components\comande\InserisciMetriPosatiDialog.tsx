'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, AlertTriangle, Cable, AlertCircle, Wrench } from 'lucide-react'
import { comandeApi, parcoCaviApi } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/AuthContext'

interface Cavo {
  id_cavo: string
  tipologia: string
  formazione: string
  metratura_teorica: number
  metratura_reale?: number
  stato_installazione: string
  id_bobina?: string
}

interface <PERSON><PERSON> {
  id_bobina: string
  tipologia: string
  sezione: string
  metri_residui: number
  stato: string
}

interface DatiPosaCavo {
  metratura_reale: number
  numero_persone_impiegate: number
  sistemazione: boolean
  fascettatura: boolean
  id_bobina: string
  force_over?: boolean
}

interface DatiPosa {
  [cavoId: string]: DatiPosaCavo
}

interface InserisciMetriPosatiDialogProps {
  open: boolean
  onClose: () => void
  codiceComanda: string
  onSuccess?: (message: string) => void
  onError?: (error: string) => void
}

export default function InserisciMetriPosatiDialog({
  open,
  onClose,
  codiceComanda,
  onSuccess,
  onError
}: InserisciMetriPosatiDialogProps) {
  const [cavi, setCavi] = useState<Cavo[]>([])
  const [bobine, setBobine] = useState<Bobina[]>([])
  const [datiPosa, setDatiPosa] = useState<DatiPosa>({})
  const [validationErrors, setValidationErrors] = useState<{[cavoId: string]: string}>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedBobina, setSelectedBobina] = useState<string>('')
  const [metriResiduiSimulati, setMetriResiduiSimulati] = useState<number>(0)
  const [caviBloccati, setCaviBloccati] = useState<string[]>([])
  const [cavoCheCausaOver, setCavoCheCausaOver] = useState<string | null>(null)
  
  const { toast } = useToast()
  const { cantiere } = useAuth()

  // Carica cavi della comanda
  useEffect(() => {
    if (open && codiceComanda) {
      loadCaviComanda()
    }
  }, [open, codiceComanda])

  // Carica bobine quando si apre il dialog
  useEffect(() => {
    if (open && cantiere?.id_cantiere) {
      loadBobine()
    }
  }, [open, cantiere?.id_cantiere])

  // Reset quando si chiude il dialog
  useEffect(() => {
    if (!open) {
      resetDialog()
    }
  }, [open])

  const resetDialog = () => {
    setCavi([])
    setBobine([])
    setDatiPosa({})
    setValidationErrors({})
    setError(null)
    setSelectedBobina('')
    setMetriResiduiSimulati(0)
    setCaviBloccati([])
    setCavoCheCausaOver(null)
  }

  const loadCaviComanda = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await comandeApi.getCaviComanda(codiceComanda)
      const caviData = response?.data || response || []
      
      // Filtra solo i cavi non installati per POSA
      const caviNonInstallati = caviData.filter((cavo: Cavo) => 
        cavo.stato_installazione !== 'Installato'
      )
      
      setCavi(caviNonInstallati)
      
      // Inizializza dati posa per ogni cavo
      const initialDatiPosa: DatiPosa = {}
      caviNonInstallati.forEach((cavo: Cavo) => {
        initialDatiPosa[cavo.id_cavo] = {
          metratura_reale: 0,
          numero_persone_impiegate: 1,
          sistemazione: false,
          fascettatura: false,
          id_bobina: 'BOBINA_VUOTA'
        }
      })
      setDatiPosa(initialDatiPosa)
      
    } catch (error: any) {
      setError('Errore durante il caricamento dei cavi della comanda')
    } finally {
      setIsLoading(false)
    }
  }

  const loadBobine = async () => {
    try {
      if (!cantiere?.id_cantiere) return
      
      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)
      const bobineData = response?.data || response || []
      
      // Filtra solo bobine disponibili
      const bobineDisponibili = bobineData.filter((bobina: Bobina) => 
        bobina.stato === 'disponibile' && bobina.metri_residui > 0
      )
      
      setBobine(bobineDisponibili)
      
    } catch (error: any) {
      setError('Errore durante il caricamento delle bobine')
    }
  }

  const handleBobinaSelectionChange = (bobinaId: string) => {
    setSelectedBobina(bobinaId)
    
    // Reset stati OVER
    setCaviBloccati([])
    setCavoCheCausaOver(null)
    
    // Aggiorna tutti i cavi con la nuova bobina
    setDatiPosa(prev => {
      const newDatiPosa = { ...prev }
      Object.keys(newDatiPosa).forEach(cavoId => {
        newDatiPosa[cavoId] = {
          ...newDatiPosa[cavoId],
          id_bobina: bobinaId
        }
      })
      return newDatiPosa
    })
    
    // Calcola metri residui iniziali
    if (bobinaId && bobinaId !== 'BOBINA_VUOTA') {
      const bobina = bobine.find(b => b.id_bobina === bobinaId)
      if (bobina) {
        setMetriResiduiSimulati(bobina.metri_residui)
      }
    } else {
      setMetriResiduiSimulati(0)
    }
  }

  const handleMetriChange = (cavoId: string, value: string) => {
    const metri = parseFloat(value) || 0
    
    setDatiPosa(prev => {
      const newDatiPosa = {
        ...prev,
        [cavoId]: {
          ...prev[cavoId],
          metratura_reale: metri
        }
      }
      
      // LOGICA OVER PROGRESSIVA
      if (selectedBobina && selectedBobina !== 'BOBINA_VUOTA') {
        const bobina = bobine.find(b => b.id_bobina === selectedBobina)
        if (bobina) {
          // Calcola metri totali richiesti da tutti i cavi
          let metriTotaliRichiesti = 0
          Object.keys(newDatiPosa).forEach(id => {
            const metri = newDatiPosa[id]?.metratura_reale || 0
            if (metri > 0) {
              metriTotaliRichiesti += metri
            }
          })
          
          // Aggiorna metri residui simulati
          const nuoviMetriResidui = bobina.metri_residui - metriTotaliRichiesti
          setMetriResiduiSimulati(nuoviMetriResidui)
          
          // Identifica il cavo che causa OVER e blocca i successivi
          if (nuoviMetriResidui < 0 && !cavoCheCausaOver) {
            setCavoCheCausaOver(cavoId)
            
            // Blocca tutti i cavi che non hanno ancora metri inseriti
            const caviDaBloccare: string[] = []
            Object.keys(newDatiPosa).forEach(id => {
              const metri = newDatiPosa[id]?.metratura_reale || 0
              if (metri === 0 && id !== cavoId) {
                caviDaBloccare.push(id)
              }
            })
            setCaviBloccati(caviDaBloccare)
            
          } else if (nuoviMetriResidui >= 0 && cavoCheCausaOver === cavoId) {
            setCavoCheCausaOver(null)
            setCaviBloccati([]) // Sblocca tutti i cavi
          }
        }
      }
      
      return newDatiPosa
    })
    
    // Validazione
    validateMetri(cavoId, metri)
  }

  const validateMetri = (cavoId: string, metri: number) => {
    const cavo = cavi.find(c => c.id_cavo === cavoId)
    if (!cavo) return
    
    setValidationErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[cavoId] // Rimuovi errore precedente
      
      if (metri > 0) {
        // Warning se metri > teorici + 10% (non bloccante)
        const soglia = cavo.metratura_teorica * 1.1
        if (metri > soglia) {
          // Non è un errore bloccante, solo un warning
        }
      }
      
      return newErrors
    })
  }

  const handlePersoneChange = (cavoId: string, value: string) => {
    const persone = parseInt(value) || 1
    setDatiPosa(prev => ({
      ...prev,
      [cavoId]: {
        ...prev[cavoId],
        numero_persone_impiegate: persone
      }
    }))
  }

  const handleSistemazioneChange = (cavoId: string, checked: boolean) => {
    setDatiPosa(prev => ({
      ...prev,
      [cavoId]: {
        ...prev[cavoId],
        sistemazione: checked
      }
    }))
  }

  const handleFascettaturaChange = (cavoId: string, checked: boolean) => {
    setDatiPosa(prev => ({
      ...prev,
      [cavoId]: {
        ...prev[cavoId],
        fascettatura: checked
      }
    }))
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)
      setError(null)

      // Validazione finale
      const hasErrors = Object.keys(validationErrors).length > 0
      if (hasErrors) {
        setError('Correggere gli errori di validazione prima di salvare')
        return
      }

      // Filtra solo i cavi con metri > 0 e imposta force_over se necessario
      const datiPosaFiltrati: DatiPosa = {}
      Object.keys(datiPosa).forEach(cavoId => {
        const datiCavo = datiPosa[cavoId]
        const metri = datiCavo?.metratura_reale || 0

        if (metri > 0) {
          // Imposta force_over se il cavo causa OVER o se la bobina è in stato OVER
          const needsForceOver = cavoCheCausaOver === cavoId || metriResiduiSimulati < 0

          datiPosaFiltrati[cavoId] = {
            ...datiCavo,
            id_bobina: selectedBobina || 'BOBINA_VUOTA',
            force_over: needsForceOver
          }
        }
      })

      if (Object.keys(datiPosaFiltrati).length === 0) {
        setError('Inserire almeno un metro per almeno un cavo')
        return
      }

      console.log({
        codiceComanda,
        caviDaSalvare: Object.keys(datiPosaFiltrati).length,
        datiPosaFiltrati,
        selectedBobina,
        metriResiduiSimulati,
        cavoCheCausaOver
      })

      // Chiama API per aggiornare dati posa con bobine (POST endpoint)
      const response = await fetch(`/api/comande/${codiceComanda}/dati-posa-bobine`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(datiPosaFiltrati)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Errore durante il salvataggio')
      }

      const successMsg = `Metri posati inseriti con successo per ${Object.keys(datiPosaFiltrati).length} cavi`

      onSuccess?.(successMsg)
      toast({
        title: "Successo",
        description: successMsg,
      })
      onClose()

    } catch (error: any) {
      const errorMsg = error?.response?.data?.detail || 'Errore durante il salvataggio dei metri posati'
      setError(errorMsg)
      onError?.(errorMsg)
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5 text-blue-600" />
            Inserisci Metri Posati - Comanda {codiceComanda}
          </DialogTitle>
          <p className="text-sm text-gray-600">
            Inserisci i metri posati per ogni cavo della comanda POSA
          </p>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Caricamento cavi...
          </div>
        ) : (
          <div className="space-y-6">
            {/* Selezione Bobina Principale */}
            <div className="border rounded-lg p-4 bg-blue-50">
              <h3 className="font-medium text-blue-900 mb-3">Selezione Bobina Principale</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="bobina-principale">Bobina da Utilizzare</Label>
                  <Select value={selectedBobina} onValueChange={handleBobinaSelectionChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona bobina principale..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="BOBINA_VUOTA">🔄 BOBINA_VUOTA (Assegna dopo)</SelectItem>
                      {bobine.map((bobina) => (
                        <SelectItem key={bobina.id_bobina} value={bobina.id_bobina}>
                          ✅ {bobina.id_bobina} - {bobina.tipologia} {bobina.sezione} ({bobina.metri_residui}m)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {selectedBobina && selectedBobina !== 'BOBINA_VUOTA' && (
                  <div className="flex items-center gap-4">
                    <div className="text-sm">
                      <span className="font-medium">Metri Residui: </span>
                      <span className={metriResiduiSimulati < 0 ? 'text-red-600 font-bold' : 'text-green-600'}>
                        {metriResiduiSimulati.toFixed(1)}m
                      </span>
                    </div>
                    {metriResiduiSimulati < 0 && (
                      <Badge variant="destructive" className="text-xs">
                        OVER
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Lista Cavi */}
            <div className="space-y-4">
              <h3 className="font-medium">Cavi da Installare ({cavi.length})</h3>
              
              {cavi.map((cavo) => {
                const datiCavo = datiPosa[cavo.id_cavo]
                const hasError = validationErrors[cavo.id_cavo]
                const isBloccato = caviBloccati.includes(cavo.id_cavo)
                const causaOver = cavoCheCausaOver === cavo.id_cavo
                const isOverState = metriResiduiSimulati < 0 && selectedBobina !== 'BOBINA_VUOTA'
                
                return (
                  <div key={cavo.id_cavo} className={`border rounded-lg p-4 space-y-4 ${isBloccato ? 'bg-red-50 border-red-200' : causaOver ? 'bg-orange-50 border-orange-200' : ''}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium flex items-center gap-2">
                          {cavo.id_cavo}
                          {isBloccato && (
                            <Badge variant="destructive" className="text-xs">
                              BLOCCATO
                            </Badge>
                          )}
                          {causaOver && (
                            <Badge variant="outline" className="text-xs border-orange-500 text-orange-700">
                              CAUSA OVER
                            </Badge>
                          )}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {cavo.tipologia} - {cavo.formazione} - {cavo.metratura_teorica}m teorici
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={cavo.stato_installazione === 'Installato' ? 'default' : 'secondary'}>
                          {cavo.stato_installazione}
                        </Badge>
                        {isOverState && (
                          <Badge variant="destructive" className="text-xs">
                            OVER
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <Label htmlFor={`metri-${cavo.id_cavo}`}>Metri Posati *</Label>
                        <Input
                          id={`metri-${cavo.id_cavo}`}
                          type="number"
                          min="0"
                          step="0.1"
                          value={datiCavo?.metratura_reale || ''}
                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                          className={hasError ? 'border-red-500' : isBloccato ? 'border-red-300 bg-red-50' : ''}
                          placeholder={isBloccato ? 'Bloccato (OVER)' : '0.0'}
                          disabled={isBloccato}
                        />
                        {hasError && (
                          <p className="text-xs text-red-500 mt-1">{hasError}</p>
                        )}
                        {isBloccato && (
                          <p className="text-xs text-red-600 mt-1">
                            ⚠️ Cavo bloccato: bobina in stato OVER
                          </p>
                        )}
                        {causaOver && !isBloccato && (
                          <p className="text-xs text-orange-600 mt-1">
                            ⚠️ Questo cavo causa lo stato OVER della bobina
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <Label htmlFor={`persone-${cavo.id_cavo}`}>Persone Impiegate</Label>
                        <Input
                          id={`persone-${cavo.id_cavo}`}
                          type="number"
                          min="1"
                          value={datiCavo?.numero_persone_impiegate || 1}
                          onChange={(e) => handlePersoneChange(cavo.id_cavo, e.target.value)}
                        />
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`sistemazione-${cavo.id_cavo}`}
                          checked={datiCavo?.sistemazione || false}
                          onCheckedChange={(checked) => handleSistemazioneChange(cavo.id_cavo, !!checked)}
                        />
                        <Label htmlFor={`sistemazione-${cavo.id_cavo}`}>Sistemazione</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`fascettatura-${cavo.id_cavo}`}
                          checked={datiCavo?.fascettatura || false}
                          onCheckedChange={(checked) => handleFascettaturaChange(cavo.id_cavo, !!checked)}
                        />
                        <Label htmlFor={`fascettatura-${cavo.id_cavo}`}>Fascettatura</Label>
                      </div>
                    </div>
                    
                    {/* Mostra bobina assegnata */}
                    <div className="text-sm text-gray-600">
                      <span className="font-medium">Bobina assegnata: </span>
                      <span className={selectedBobina === 'BOBINA_VUOTA' ? 'text-orange-600' : 'text-blue-600'}>
                        {selectedBobina || 'Nessuna'}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || isLoading || cavi.length === 0}
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Salvando...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Salva Metri Posati
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
