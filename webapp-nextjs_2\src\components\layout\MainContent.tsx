'use client'

import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useSessionPersistence } from '@/hooks/useSessionPersistence'
import ExpirationWarning from '@/components/auth/ExpirationWarning'

interface MainContentProps {
  children: React.ReactNode
}

export default function MainContent({ children }: MainContentProps) {
  const { isAuthenticated } = useAuth()

  // SEMPRE chiamare gli hooks - la logica condizionale è DENTRO l'hook
  useSessionPersistence()

  return (
    <main className="pt-16">
      {isAuthenticated && (
        <div className="container mx-auto px-4 py-2">
          <ExpirationWarning />
        </div>
      )}
      {children}
    </main>
  )
}
