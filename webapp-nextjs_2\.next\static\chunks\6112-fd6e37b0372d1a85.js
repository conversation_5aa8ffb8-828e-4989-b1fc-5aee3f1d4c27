"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6112],{15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),d=r(19178),c=r(25519),u=r(34378),p=r(28905),f=r(63655),h=r(92293),y=r(93795),g=r(38168),m=r(99708),v=r(95155),x="Dialog",[k,b]=(0,l.A)(x),[j,C]=k(x),D=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:x});return(0,v.jsx)(j,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};D.displayName=x;var w="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=C(w,r),i=(0,a.s)(t,l.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":K(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=w;var E="DialogPortal",[I,N]=k(E,{forceMount:void 0}),_=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=C(E,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(p.C,{present:r||l.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};_.displayName=E;var A="DialogOverlay",O=n.forwardRef((e,t)=>{let r=N(A,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(A,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:n||a.open,children:(0,v.jsx)(M,{...o,ref:t})}):null});O.displayName=A;var F=(0,m.TL)("DialogOverlay.RemoveScroll"),M=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(A,r);return(0,v.jsx)(y.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",G=n.forwardRef((e,t)=>{let r=N(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(P,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||a.open,children:a.modal?(0,v.jsx)(T,{...o,ref:t}):(0,v.jsx)(q,{...o,ref:t})})});G.displayName=P;var T=n.forwardRef((e,t)=>{let r=C(P,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,v.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=n.forwardRef((e,t)=>{let r=C(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=C(P,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,h.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":K(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:u.titleId}),(0,v.jsx)(Y,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(B,r);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=B;var H="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(H,r);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=H;var Z="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(Z,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}V.displayName=Z;var U="DialogTitleWarning",[X,z]=(0,l.q)(U,{contentName:P,titleName:B,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=z(U),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,o=z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},Q=D,$=R,ee=_,et=O,er=G,en=S,eo=W,ea=V},17580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},25273:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},76981:(e,t,r)=>{r.d(t,{C1:()=>C,bL:()=>b});var n=r(12115),o=r(6101),a=r(46081),l=r(85185),i=r(5845),s=r(45503),d=r(11275),c=r(28905),u=r(63655),p=r(95155),f="Checkbox",[h,y]=(0,a.A)(f),[g,m]=h(f);function v(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:l,form:s,name:d,onCheckedChange:c,required:u,value:h="on",internal_do_not_use_render:y}=e,[m,v]=(0,i.i)({prop:r,defaultProp:null!=a&&a,onChange:c,caller:f}),[x,k]=n.useState(null),[b,j]=n.useState(null),C=n.useRef(!1),D=!x||!!s||!!x.closest("form"),w={checked:m,disabled:l,setChecked:v,control:x,setControl:k,name:d,form:s,value:h,hasConsumerStoppedPropagationRef:C,required:u,defaultChecked:!R(a)&&a,isFormControl:D,bubbleInput:b,setBubbleInput:j};return(0,p.jsx)(g,{scope:t,...w,children:"function"==typeof y?y(w):o})}var x="CheckboxTrigger",k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:a,onClick:i,...s}=e,{control:d,value:c,disabled:f,checked:h,required:y,setControl:g,setChecked:v,hasConsumerStoppedPropagationRef:k,isFormControl:b,bubbleInput:j}=m(x,r),C=(0,o.s)(t,g),D=n.useRef(h);return n.useEffect(()=>{let e=null==d?void 0:d.form;if(e){let t=()=>v(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,v]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":R(h)?"mixed":h,"aria-required":y,"data-state":E(h),"data-disabled":f?"":void 0,disabled:f,value:c,...s,ref:C,onKeyDown:(0,l.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(i,e=>{v(e=>!!R(e)||!e),j&&b&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})})});k.displayName=x;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:l,disabled:i,value:s,onCheckedChange:d,form:c,...u}=e;return(0,p.jsx)(v,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:i,required:l,onCheckedChange:d,name:n,form:c,value:s,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(k,{...u,ref:t,__scopeCheckbox:r}),n&&(0,p.jsx)(w,{__scopeCheckbox:r})]})}})});b.displayName=f;var j="CheckboxIndicator",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=m(j,r);return(0,p.jsx)(c.C,{present:n||R(a.checked)||!0===a.checked,children:(0,p.jsx)(u.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=j;var D="CheckboxBubbleInput",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...a}=e,{control:l,hasConsumerStoppedPropagationRef:i,checked:c,defaultChecked:f,required:h,disabled:y,name:g,value:v,form:x,bubbleInput:k,setBubbleInput:b}=m(D,r),j=(0,o.s)(t,b),C=(0,s.Z)(c),w=(0,d.X)(l);n.useEffect(()=>{if(!k)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(C!==c&&e){let r=new Event("click",{bubbles:t});k.indeterminate=R(c),e.call(k,!R(c)&&c),k.dispatchEvent(r)}},[k,C,c,i]);let E=n.useRef(!R(c)&&c);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:E.current,required:h,disabled:y,name:g,value:v,form:x,...a,tabIndex:-1,ref:j,style:{...a.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function E(e){return R(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=D},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}}]);