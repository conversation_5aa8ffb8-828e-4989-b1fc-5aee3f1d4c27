if(!self.define){let e,a={};const s=(s,n)=>(s=new URL(s+".js",n).href,a[s]||new Promise((a=>{if("document"in self){const e=document.createElement("script");e.src=s,e.onload=a,document.head.appendChild(e)}else e=s,importScripts(s),a()})).then((()=>{let e=a[s];if(!e)throw new Error(`Module ${s} didn’t register its module`);return e})));self.define=(n,c)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(a[i])return;let t={};const r=e=>s(e,i),o={module:{uri:i},exports:t,require:r};a[i]=Promise.all(n.map((e=>o[e]||r(e)))).then((e=>(c(...e),t)))}}define(["./workbox-4d767a27"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"9fca685116b9198b939120e46f76505f"},{url:"/_next/static/SLGTdN0bTaa4cExS8e38E/_buildManifest.js",revision:"a6b7c3a1bc42176faf038ba476ba57e3"},{url:"/_next/static/SLGTdN0bTaa4cExS8e38E/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/188-dda145e351294a07.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/341.a4c9e4b8dad9832e.js",revision:"a4c9e4b8dad9832e"},{url:"/_next/static/chunks/464-22b74a9a67a94075.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/497-fa5a714f82d77fc1.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/4bd1b696-4ac4a5e3a6ee71b7.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/684-bf0e08330f044dff.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/761-21d493009173d035.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/937-fa7f6270b9db0b71.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/_not-found/page-8c76c9b20ee4bf3d.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/admin/page-87c2f2d6a0170983.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/cavi/page-7badda9750eeb9e6.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/certificazioni/page-39415953afde5977.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/comande/page-9543052c2aee90a4.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/layout-b9ce69fad64b590f.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/login/page-3fd86914f5c021c7.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/page-d76ac89b7e371813.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/parco-cavi/page-727673e66285a91d.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/productivity/page-f08ebcb194f2a7e9.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/app/reports/page-3cef85e84a0420d2.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/framework-52167c1502110d79.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/main-16529e7c18d6d424.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/main-app-e1686622a05fec29.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/pages/_app-6635a3a4af8739be.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/pages/_error-da855ac378341690.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-4cbbeb72a7361947.js",revision:"SLGTdN0bTaa4cExS8e38E"},{url:"/_next/static/css/9a20fcfa5587aa79.css",revision:"9a20fcfa5587aa79"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/747892c23ea88013-s.woff2",revision:"a0761690ccf4441ace5cec893b82d4ab"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/93f479601ee12b01-s.p.woff2",revision:"da83d5f06d825c5ae65b7cca706cb312"},{url:"/_next/static/media/9610d9e46709d722-s.woff2",revision:"7b7c0ef93df188a852344fc272fc096b"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/file.svg",revision:"d09f95206c3fa0bb9bd9fefabfd0ea71"},{url:"/globe.svg",revision:"2aaafa6a49b6563925fe440891e32717"},{url:"/manifest.json",revision:"be445b8da8468a0581f75f1def8757bc"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/vercel.svg",revision:"c0af2f507b369b085b35ef4bbe3bcf1e"},{url:"/window.svg",revision:"a2760511c65806022ad20adf74370ff3"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:a,event:s,state:n})=>a&&"opaqueredirect"===a.type?new Response(a.body,{status:200,statusText:"OK",headers:a.headers}):a}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const a=e.pathname;return!a.startsWith("/api/auth/")&&!!a.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
