'use client'

import { useState, useEffect, useRef } from 'react'
import * as VisuallyHidden from '@radix-ui/react-visually-hidden'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Zap, CheckCircle, AlertTriangle, X } from 'lucide-react'
import { Cavo } from '@/types'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

interface CollegamentiDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSuccess?: () => void
  onError?: (error: string) => void
}

interface ConfirmDialogState {
  open: boolean
  type: 'partenza' | 'arrivo' | 'entrambi' | null
  title: string
  description: string
}

interface ConfirmDisconnectDialogProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description: string
  isLoading: boolean
  isDangerous?: boolean
}

function ConfirmDisconnectDialog({
  open,
  onClose,
  onConfirm,
  title,
  description,
  isLoading,
  isDangerous = false
}: ConfirmDisconnectDialogProps) {
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)

  useEffect(() => {
    if (!open) {
      setShowFinalConfirmation(false)
    }
  }, [open])

  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open && !isLoading) {
        onClose()
      }
    }

    if (open) {
      document.addEventListener('keydown', handleEsc)
      return () => document.removeEventListener('keydown', handleEsc)
    }
  }, [open, onClose, isLoading])

  const handleInitialConfirm = () => {
    if (isDangerous) {
      setShowFinalConfirmation(true)
    } else {
      onConfirm()
    }
  }

  const handleFinalConfirm = () => {
    onConfirm()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-[400px]"
        aria-describedby="confirm-disconnect-description"
      >
        {!showFinalConfirmation ? (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-orange-600">
                <AlertTriangle className="h-5 w-5" />
                {title}
              </DialogTitle>
              <DialogDescription id="confirm-disconnect-description">
                {description}
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <Alert className="border-orange-200 bg-orange-50">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <strong>Attenzione:</strong> Questa azione modificherà lo stato del collegamento del cavo.
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="flex-1 hover:bg-gray-50"
              >
                Annulla
              </Button>
              <Button
                variant="outline"
                onClick={handleInitialConfirm}
                disabled={isLoading}
                className="flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
              >
                <AlertTriangle className="mr-2 h-4 w-4" />
                {isDangerous ? 'Procedi' : 'Conferma'}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="text-center text-orange-600">
                Conferma Finale
              </DialogTitle>
            </DialogHeader>

            <div className="py-4 text-center">
              <div className="mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Sei veramente sicuro?
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Questa azione scollegherà <strong>entrambi i lati</strong> del cavo.
              </p>
              <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
                <p className="text-sm text-orange-800 font-medium">
                  ⚠️ Operazione irreversibile
                </p>
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFinalConfirmation(false)}
                disabled={isLoading}
                className="flex-1"
              >
                No, Annulla
              </Button>
              <Button
                variant="outline"
                onClick={handleFinalConfirm}
                disabled={isLoading}
                className="flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Scollegando...
                  </>
                ) : (
                  'Sì, Scollega'
                )}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}

function CollegamentiDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: CollegamentiDialogProps) {
  const { cantiere } = useAuth()
  const { toast } = useToast()
  const [selectedResponsabile, setSelectedResponsabile] = useState('cantiere')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    open: false,
    type: null,
    title: '',
    description: ''
  })

  const dialogRef = useRef<HTMLDivElement>(null)
  const firstFocusableRef = useRef<HTMLButtonElement>(null)
  const lastFocusableRef = useRef<HTMLButtonElement>(null)

  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')

  useEffect(() => {
    if (open && cavo) {
      setSelectedResponsabile('cantiere')
      setError('')
    }
  }, [open, cavo])

  useEffect(() => {
    if (open && dialogRef.current) {
      const focusableElements = dialogRef.current.querySelectorAll(
        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      )

      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus()
      }
    }
  }, [open])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open || loading || confirmDialog.open) return

      switch (e.key) {
        case 'Escape':
          onClose()
          break

        case 'Tab':
          const focusableElements = dialogRef.current?.querySelectorAll(
            'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
          )

          const firstElement = focusableElements?.[0] as HTMLElement
          const lastElement = focusableElements?.[focusableElements.length - 1] as HTMLElement

          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              e.preventDefault()
              lastElement?.focus()
            }
          } else {
            if (document.activeElement === lastElement) {
              e.preventDefault()
              firstElement?.focus()
            }
          }
          break
      }
    }

    if (open) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [open, onClose, loading, confirmDialog.open])



  const getStatoCollegamento = () => {
    if (!cavo) return {
      stato: 'non_collegato',
      descrizione: 'Non collegato',
      dettaglio: 'Nessun lato collegato',
      progressoPercentuale: 0,
      colore: 'gray'
    }

    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    switch (collegamento) {
      case 1:
        return {
          stato: 'partenza',
          descrizione: '🟢⚪ Collegato lato partenza',
          dettaglio: 'Collegamento parziale - manca lato arrivo',
          progressoPercentuale: 50,
          colore: 'amber'
        }
      case 2:
        return {
          stato: 'arrivo',
          descrizione: '⚪🟢 Collegato lato arrivo',
          dettaglio: 'Collegamento parziale - manca lato partenza',
          progressoPercentuale: 50,
          colore: 'amber'
        }
      case 3:
        return {
          stato: 'completo',
          descrizione: '🟢🟢 Completamente collegato',
          dettaglio: 'Entrambi i lati collegati correttamente',
          progressoPercentuale: 100,
          colore: 'green'
        }
      default:
        return {
          stato: 'non_collegato',
          descrizione: '⚪⚪ Non collegato',
          dettaglio: 'Nessun lato collegato',
          progressoPercentuale: 0,
          colore: 'gray'
        }
    }
  }

  const handleCollegaPartenza = async () => {
    if (!cavo || !cantiere) {
      console.error('🔌 CollegamentiDialog: Mancano dati necessari', { cavo, cantiere })
      return
    }

    // Verifica autenticazione
    const token = localStorage.getItem('token')
    if (!token) {
      setError('Utente non autenticato. Effettua il login.')
      toast({
        title: "Errore autenticazione",
        description: "Utente non autenticato. Effettua il login.",
        variant: "destructive"
      })
      return
    }

    try {
      setLoading(true)
      setError('')

      // Debug completo dell'autenticazione
      const accessToken = localStorage.getItem('access_token')
      const userData = localStorage.getItem('user_data')

      console.log('🔌 CollegamentiDialog: Debug autenticazione', {
        token: token ? `presente (${token.substring(0, 20)}...)` : 'assente',
        accessToken: accessToken ? `presente (${accessToken.substring(0, 20)}...)` : 'assente',
        userData: userData ? 'presente' : 'assente'
      })

      console.log('🔌 CollegamentiDialog: Tentativo collegamento partenza', {
        cantiereId: cantiere.id_cantiere,
        cavoId: cavo.id_cavo,
        responsabile: selectedResponsabile
      })

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'partenza',
        selectedResponsabile
      )

      const successMessage = `Collegamento lato partenza completato per il cavo ${cavo.id_cavo}`

      toast({
        title: "Collegamento completato",
        description: successMessage,
        variant: "default"
      })

      setScreenReaderAnnouncement(successMessage)
      window.location.reload()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'

      toast({
        title: "Errore collegamento",
        description: errorMessage,
        variant: "destructive"
      })

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleCollegaArrivo = async () => {
    if (!cavo || !cantiere) {
      console.error('🔌 CollegamentiDialog: Mancano dati necessari', { cavo, cantiere })
      return
    }

    try {
      setLoading(true)
      setError('')

      console.log('🔌 CollegamentiDialog: Tentativo collegamento arrivo', {
        cantiereId: cantiere.id_cantiere,
        cavoId: cavo.id_cavo,
        responsabile: selectedResponsabile
      })

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'arrivo',
        selectedResponsabile
      )

      const successMessage = `Collegamento lato arrivo completato per il cavo ${cavo.id_cavo}`

      toast({
        title: "Collegamento completato",
        description: successMessage,
        variant: "default"
      })

      setScreenReaderAnnouncement(successMessage)
      window.location.reload()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'

      toast({
        title: "Errore collegamento",
        description: errorMessage,
        variant: "destructive"
      })

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleOpenConfirmPartenza = () => {
    setConfirmDialog({
      open: true,
      type: 'partenza',
      title: 'Scollega Lato Partenza',
      description: `Sei sicuro di voler scollegare il lato partenza del cavo ${cavo?.id_cavo}?`
    })
  }

  const handleOpenConfirmArrivo = () => {
    setConfirmDialog({
      open: true,
      type: 'arrivo',
      title: 'Scollega Lato Arrivo',
      description: `Sei sicuro di voler scollegare il lato arrivo del cavo ${cavo?.id_cavo}?`
    })
  }

  const handleOpenConfirmEntrambi = () => {
    setConfirmDialog({
      open: true,
      type: 'entrambi',
      title: 'Scollega Entrambi i Lati',
      description: `Sei sicuro di voler scollegare completamente il cavo ${cavo?.id_cavo}? Questa azione rimuoverà tutti i collegamenti.`
    })
  }

  const handleCloseConfirm = () => {
    setConfirmDialog({
      open: false,
      type: null,
      title: '',
      description: ''
    })
  }

  const handleConfirmScollega = async () => {
    if (!cavo || !cantiere || !confirmDialog.type) return

    try {
      setLoading(true)
      setError('')

      // FIX: Passa il lato corretto o undefined solo per "entrambi"
      if (confirmDialog.type === 'entrambi') {
        // Per scollegare entrambi, non passare il lato
        await caviApi.scollegaCavo(cantiere.id_cantiere, cavo.id_cavo)
      } else {
        // Per scollegare un lato specifico, passa il lato
        await caviApi.scollegaCavo(cantiere.id_cantiere, cavo.id_cavo, confirmDialog.type)
      }

      const latoText = confirmDialog.type === 'entrambi'
        ? ' completo'
        : ` lato ${confirmDialog.type}`

      toast({
        title: "Scollegamento completato",
        description: `Scollegamento${latoText} completato per il cavo ${cavo.id_cavo}`,
        variant: "default"
      })

      handleCloseConfirm()
      onClose()
      window.location.reload()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante lo scollegamento'

      toast({
        title: "Errore scollegamento",
        description: errorMessage,
        variant: "destructive"
      })

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (!cavo) return null

  const statoCollegamento = getStatoCollegamento()
  // Verifica se il cavo è installato (più permissiva)
  const isInstalled = cavo.stato_installazione?.toLowerCase() === 'installato' ||
                     (cavo.metri_posati || cavo.metratura_reale || 0) > 0

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        ref={dialogRef}
        className="sm:max-w-[500px]"
        aria-describedby="collegamenti-dialog-description"
        role="dialog"
        aria-labelledby="collegamenti-dialog-title"
        aria-modal="true"
      >
        <VisuallyHidden.Root>
          <DialogTitle id="collegamenti-dialog-title">
            Gestione Collegamenti - Cavo {cavo.id_cavo}
          </DialogTitle>
        </VisuallyHidden.Root>

        <DialogHeader>
          <div className="flex items-center gap-2 text-lg font-semibold">
            <Zap className="h-5 w-5 text-blue-600" />
            Gestione Collegamenti - Cavo {cavo.id_cavo}
          </div>
          <DialogDescription id="collegamenti-dialog-description">
            Gestisci i collegamenti elettrici del cavo selezionato
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6" role="main">
          <div
            aria-live="polite"
            aria-atomic="true"
            className="sr-only"
          >
            {screenReaderAnnouncement}
          </div>

          <section className="p-4 bg-gray-50 rounded-lg space-y-3"
            aria-labelledby="stato-collegamenti" role="region">
            <h3 id="stato-collegamenti" className="text-sm font-medium">
              Stato Attuale Collegamenti
            </h3>

            <div
              className="mt-1 text-lg font-semibold"
              aria-live="polite"
              aria-describedby="stato-collegamenti"
            >
              {statoCollegamento.descrizione}
            </div>

            <div className="mt-2 space-y-2">
              <div className="text-sm text-gray-600">
                {statoCollegamento.dettaglio}
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    statoCollegamento.colore === 'green' ? 'bg-green-500' :
                    statoCollegamento.colore === 'amber' ? 'bg-amber-500' :
                    'bg-gray-400'
                  }`}
                  style={{ width: `${statoCollegamento.progressoPercentuale}%` }}
                  role="progressbar"
                  aria-valuenow={statoCollegamento.progressoPercentuale}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  aria-label={`Progresso collegamento: ${statoCollegamento.progressoPercentuale}%`}
                />
              </div>

              <div className="text-xs text-gray-500 text-right">
                Progresso: {statoCollegamento.progressoPercentuale}%
              </div>
            </div>

            <div
              className="grid grid-cols-2 gap-4 text-sm"
              role="group"
              aria-label="Dettagli collegamenti per lato"
            >
              <div>
                <Label className="text-xs text-gray-600" id="lato-partenza-label">
                  Lato Partenza
                </Label>
                <div className="mt-1">
                  <div className="space-y-1">
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                        (cavo.collegamenti || 0) & 1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                      }`}
                      role="status"
                      aria-labelledby="lato-partenza-label"
                      aria-describedby={cavo.responsabile_partenza ? "resp-partenza" : undefined}
                    >
                      {(cavo.collegamenti || 0) & 1 ? '🟢 Collegato' : '⚪ Non collegato'}
                    </span>

                    {statoCollegamento.stato === 'arrivo' && (
                      <div className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                        ⚠️ Prossimo: collegare questo lato
                      </div>
                    )}
                  </div>

                  {cavo.responsabile_partenza && (
                    <div
                      id="resp-partenza"
                      className="text-xs text-gray-600 mt-1"
                      aria-label={`Responsabile lato partenza: ${cavo.responsabile_partenza}`}
                    >
                      Resp: {cavo.responsabile_partenza}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label className="text-xs text-gray-600" id="lato-arrivo-label">
                  Lato Arrivo
                </Label>
                <div className="mt-1">
                  <div className="space-y-1">
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                        (cavo.collegamenti || 0) & 2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                      }`}
                      role="status"
                      aria-labelledby="lato-arrivo-label"
                      aria-describedby={cavo.responsabile_arrivo ? "resp-arrivo" : undefined}
                    >
                      {(cavo.collegamenti || 0) & 2 ? '🟢 Collegato' : '⚪ Non collegato'}
                    </span>

                    {statoCollegamento.stato === 'partenza' && (
                      <div className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                        ⚠️ Prossimo: collegare questo lato
                      </div>
                    )}
                  </div>

                  {cavo.responsabile_arrivo && (
                    <div
                      id="resp-arrivo"
                      className="text-xs text-gray-600 mt-1"
                      aria-label={`Responsabile lato arrivo: ${cavo.responsabile_arrivo}`}
                    >
                      Resp: {cavo.responsabile_arrivo}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </section>

          {!isInstalled && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>Il cavo deve essere installato prima di poter essere collegato.</p>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p><strong>Stato attuale:</strong> {cavo.stato_installazione || 'Non definito'}</p>
                    <p><strong>Metri posati:</strong> {cavo.metri_posati || 0}</p>
                    <p><strong>Metratura reale:</strong> {cavo.metratura_reale || 0}</p>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isInstalled && (statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'arrivo') && (
            <Alert className="border-amber-200 bg-amber-50">
              <AlertCircle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                <strong>Collegamento parziale rilevato!</strong><br />
                {statoCollegamento.stato === 'partenza'
                  ? 'Il lato partenza è collegato. Completa il collegamento collegando anche il lato arrivo.'
                  : 'Il lato arrivo è collegato. Completa il collegamento collegando anche il lato partenza.'
                }
              </AlertDescription>
            </Alert>
          )}

          {(isInstalled || process.env.NODE_ENV === 'development') && (
            <>
              {!isInstalled && (
                <Alert className="border-yellow-200 bg-yellow-50">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    <strong>Modalità Debug:</strong> Il cavo non è installato ma puoi testare il collegamento.
                  </AlertDescription>
                </Alert>
              )}

              <section className="space-y-2" aria-labelledby="responsabile-section">
                <h3 id="responsabile-section" className="sr-only">
                  Selezione Responsabile Collegamento
                </h3>
                <Label
                  htmlFor="responsabile-input"
                  className="text-sm font-medium"
                >
                  Responsabile Collegamento
                </Label>
                <Input
                  id="responsabile-input"
                  type="text"
                  value={selectedResponsabile}
                  onChange={(e) => setSelectedResponsabile(e.target.value)}
                  disabled={loading}
                  className="w-full"
                  placeholder="Inserisci nome responsabile..."
                  aria-describedby="responsabile-help"
                />
                <p id="responsabile-help" className="text-xs text-gray-600">
                  Inserisci il nome del responsabile che effettuerà il collegamento (predefinito: 'cantiere')
                </p>
              </section>

              <section className="space-y-3" aria-labelledby="azioni-collegamento">
                <h3 id="azioni-collegamento" className="text-sm font-medium">
                  Azioni Collegamento
                </h3>

                <div className="grid grid-cols-2 gap-3">
                  <Button
                    ref={firstFocusableRef}
                    onClick={handleCollegaPartenza}
                    disabled={loading || !selectedResponsabile || ((cavo.collegamenti || 0) & 1) > 0}
                    className="w-full bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300"
                    variant="outline"
                    aria-describedby="collega-partenza-help"
                  >
                    {loading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="mr-2 h-4 w-4" />
                    )}
                    Collega Partenza
                  </Button>

                  <Button
                    onClick={handleCollegaArrivo}
                    disabled={loading || !selectedResponsabile || ((cavo.collegamenti || 0) & 2) > 0}
                    className="w-full bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300"
                    variant="outline"
                    aria-describedby="collega-arrivo-help"
                  >
                    {loading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="mr-2 h-4 w-4" />
                    )}
                    Collega Arrivo
                  </Button>
                </div>

                <div className="text-xs text-gray-600 space-y-1">
                  <p id="collega-partenza-help">
                    Collega il lato partenza del cavo
                  </p>
                  <p id="collega-arrivo-help">
                    Collega il lato arrivo del cavo
                  </p>
                </div>

                {((cavo.collegamenti || 0) > 0) && (
                  <div className="border-t pt-3 space-y-3">
                    <h4 className="text-sm font-medium text-orange-600">
                      Azioni Scollegamento
                    </h4>

                    <div className="grid grid-cols-1 gap-2">
                      {((cavo.collegamenti || 0) & 1) > 0 && (
                        <Button
                          variant="outline"
                          onClick={handleOpenConfirmPartenza}
                          disabled={loading}
                          className="w-full bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
                          aria-describedby="scollega-partenza-help"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Scollega Partenza
                        </Button>
                      )}

                      {((cavo.collegamenti || 0) & 2) > 0 && (
                        <Button
                          variant="outline"
                          onClick={handleOpenConfirmArrivo}
                          disabled={loading}
                          className="w-full bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
                          aria-describedby="scollega-arrivo-help"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Scollega Arrivo
                        </Button>
                      )}

                      {(cavo.collegamenti || 0) === 3 && (
                        <Button
                          variant="outline"
                          onClick={handleOpenConfirmEntrambi}
                          disabled={loading}
                          className="w-full bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:border-red-300"
                          aria-describedby="scollega-entrambi-help"
                        >
                          <AlertTriangle className="mr-2 h-4 w-4" />
                          Scollega Entrambi i Lati
                        </Button>
                      )}
                    </div>

                    <div className="text-xs text-gray-600 space-y-1">
                      <p id="scollega-partenza-help">
                        Rimuove il collegamento dal lato partenza
                      </p>
                      <p id="scollega-arrivo-help">
                        Rimuove il collegamento dal lato arrivo
                      </p>
                      <p id="scollega-entrambi-help">
                        Rimuove completamente tutti i collegamenti del cavo
                      </p>
                    </div>
                  </div>
                )}
              </section>
            </>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            ref={lastFocusableRef}
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="flex-1"
          >
            Chiudi
          </Button>
        </DialogFooter>
      </DialogContent>

      <ConfirmDisconnectDialog
        open={confirmDialog.open}
        onClose={handleCloseConfirm}
        onConfirm={handleConfirmScollega}
        title={confirmDialog.title}
        description={confirmDialog.description}
        isLoading={loading}
        isDangerous={confirmDialog.type === 'entrambi'}
      />
    </Dialog>
  )
}

export default CollegamentiDialog
