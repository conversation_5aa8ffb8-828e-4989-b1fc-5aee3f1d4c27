import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    console.log('🔄 Cantieri API: Proxying request to backend:', `${backendUrl}/api/cantieri`)
    
    const response = await fetch(`${backendUrl}/api/cantieri`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      }
    })

    console.log('📡 Cantieri API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Cantieri API: Backend error:', errorData)
      return NextResponse.json(errorData, { 
        status: response.status 
      })
    }

    const data = await response.json()
    console.log('📡 Cantieri API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Cantieri API: Error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    console.log('🔄 Cantieri API: Proxying POST request to backend:', `${backendUrl}/api/cantieri`)
    
    const response = await fetch(`${backendUrl}/api/cantieri`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      },
      body: JSON.stringify(body)
    })

    console.log('📡 Cantieri API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Cantieri API: Backend error:', errorData)
      return NextResponse.json(errorData, { 
        status: response.status 
      })
    }

    const data = await response.json()
    console.log('📡 Cantieri API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Cantieri API: POST Error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
