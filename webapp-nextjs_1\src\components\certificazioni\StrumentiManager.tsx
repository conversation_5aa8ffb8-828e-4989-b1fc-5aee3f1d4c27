'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Settings, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react'
import { StrumentoCertificato } from '@/types/certificazioni'
import { strumentiApi } from '@/lib/api'
import StrumentoForm from './StrumentoForm'

interface StrumentiManagerProps {
  cantiereId: number
  strumenti: StrumentoCertificato[]
  onUpdate: () => void
}

export default function StrumentiManager({ cantiereId, strumenti, onUpdate }: StrumentiManagerProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [selectedStrumento, setSelectedStrumento] = useState<StrumentoCertificato | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleCreateStrumento = () => {
    setSelectedStrumento(null)
    setShowForm(true)
  }

  const handleEditStrumento = (strumento: StrumentoCertificato) => {
    setSelectedStrumento(strumento)
    setShowForm(true)
  }

  const handleDeleteStrumento = async (id: number) => {
    if (!confirm('Sei sicuro di voler eliminare questo strumento?')) return
    
    try {
      setIsLoading(true)
      await strumentiApi.deleteStrumento(cantiereId, id)
      onUpdate()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante l\'eliminazione')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatoBadge = (strumento: StrumentoCertificato) => {
    const oggi = new Date()
    const scadenza = new Date(strumento.data_scadenza_calibrazione)
    const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))

    if (strumento.stato_strumento === 'FUORI_SERVIZIO') {
      return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Fuori Servizio</Badge>
    }

    if (giorniRimanenti < 0) {
      return <Badge className="bg-red-100 text-red-800 border-red-200">Scaduto</Badge>
    }

    if (giorniRimanenti <= 30) {
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">In Scadenza</Badge>
    }

    return <Badge className="bg-green-100 text-green-800 border-green-200">Attivo</Badge>
  }

  const getStatoIcon = (strumento: StrumentoCertificato) => {
    const oggi = new Date()
    const scadenza = new Date(strumento.data_scadenza_calibrazione)
    const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))

    if (strumento.stato_strumento === 'FUORI_SERVIZIO' || giorniRimanenti < 0) {
      return <AlertCircle className="h-4 w-4 text-red-500" />
    }

    if (giorniRimanenti <= 30) {
      return <Clock className="h-4 w-4 text-yellow-500" />
    }

    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  const filteredStrumenti = strumenti.filter(strumento => {
    const searchLower = searchTerm.toLowerCase()
    return (
      strumento.nome?.toLowerCase().includes(searchLower) ||
      strumento.marca?.toLowerCase().includes(searchLower) ||
      strumento.modello?.toLowerCase().includes(searchLower) ||
      strumento.numero_serie?.toLowerCase().includes(searchLower)
    )
  })

  const stats = {
    totali: strumenti.length,
    attivi: strumenti.filter(s => {
      const oggi = new Date()
      const scadenza = new Date(s.data_scadenza_calibrazione)
      return s.stato_strumento === 'ATTIVO' && scadenza > oggi
    }).length,
    scaduti: strumenti.filter(s => {
      const oggi = new Date()
      const scadenza = new Date(s.data_scadenza_calibrazione)
      return scadenza <= oggi
    }).length,
    in_scadenza: strumenti.filter(s => {
      const oggi = new Date()
      const scadenza = new Date(s.data_scadenza_calibrazione)
      const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))
      return giorniRimanenti > 0 && giorniRimanenti <= 30
    }).length
  }

  if (showForm) {
    return (
      <StrumentoForm
        cantiereId={cantiereId}
        strumento={selectedStrumento}
        onSuccess={() => {
          setShowForm(false)
          onUpdate()
        }}
        onCancel={() => setShowForm(false)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 flex items-center gap-3">
            <Settings className="h-6 w-6 text-blue-600" />
            Gestione Strumenti
          </h2>
          <p className="text-slate-600 mt-1">Strumenti di misura e calibrazione</p>
        </div>
        
        <Button onClick={handleCreateStrumento}>
          <Plus className="h-4 w-4 mr-2" />
          Nuovo Strumento
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Totali</p>
                <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
              </div>
              <Settings className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Attivi</p>
                <p className="text-2xl font-bold text-green-600">{stats.attivi}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">In Scadenza</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.in_scadenza}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Scaduti</p>
                <p className="text-2xl font-bold text-red-600">{stats.scaduti}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Ricerca Strumenti
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="Cerca per nome, marca, modello o numero serie..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </CardContent>
      </Card>

      {/* Strumenti Table */}
      <Card>
        <CardHeader>
          <CardTitle>Elenco Strumenti ({filteredStrumenti.length})</CardTitle>
          <CardDescription>
            Gestione strumenti di misura e certificazione
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Marca/Modello</TableHead>
                  <TableHead>Numero Serie</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Calibrazione</TableHead>
                  <TableHead>Scadenza</TableHead>
                  <TableHead>Stato</TableHead>
                  <TableHead>Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Caricamento...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredStrumenti.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-slate-500">
                      Nessuno strumento trovato
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredStrumenti.map((strumento) => {
                    const oggi = new Date()
                    const scadenza = new Date(strumento.data_scadenza_calibrazione)
                    const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))

                    return (
                      <TableRow key={strumento.id_strumento}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {getStatoIcon(strumento)}
                            {strumento.nome}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{strumento.marca}</div>
                            <div className="text-sm text-slate-500">{strumento.modello}</div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm">{strumento.numero_serie}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {strumento.tipo_strumento || 'N/A'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(strumento.data_calibrazione).toLocaleDateString('it-IT')}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div>{new Date(strumento.data_scadenza_calibrazione).toLocaleDateString('it-IT')}</div>
                            {giorniRimanenti > 0 && giorniRimanenti <= 30 && (
                              <div className="text-xs text-yellow-600">
                                {giorniRimanenti} giorni rimanenti
                              </div>
                            )}
                            {giorniRimanenti < 0 && (
                              <div className="text-xs text-red-600">
                                Scaduto da {Math.abs(giorniRimanenti)} giorni
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{getStatoBadge(strumento)}</TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleEditStrumento(strumento)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleDeleteStrumento(strumento.id_strumento)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
