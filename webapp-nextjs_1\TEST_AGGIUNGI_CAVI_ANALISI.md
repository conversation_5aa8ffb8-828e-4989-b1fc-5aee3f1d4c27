# Test Analisi Funzione Aggiungi Cavi a Bobina

## 🧪 Test Scenario: Inserimento Sequenziale con Stato OVER

### **Setup Test**
- **<PERSON><PERSON>**: C1_B001 con 100m residui
- **<PERSON><PERSON>**: 
  1. C1_001 (teorici: 50m)
  2. C1_002 (teorici: 40m) 
  3. C1_003 (teorici: 30m)

### **Test Case 1: Inserimento Normale**
**Azione**: Utente inserisce metri in sequenza
1. C1_001: 45m → ✅ OK (residui: 55m)
2. C1_002: 35m → ✅ OK (residui: 20m)
3. C1_003: 15m → ✅ OK (residui: 5m)

**Risultato Atteso**: Tutti i cavi salvati, bobina stato "In uso"

### **Test Case 2: Stato OVER durante Inserimento**
**Azione**: Utente inserisce metri che causano OVER
1. C1_001: 60m → ✅ OK (residui: 40m)
2. C1_002: 50m → ❌ OVER! (residui: -10m)
3. C1_003: 0m → 🚫 Bloccato

**Comportamento Attuale**:
- ❌ C1_002 viene bloccato completamente
- ❌ C1_003 viene bloccato automaticamente
- ❌ Utente non può modificare C1_002 per ridurre metri

**Comportamento Richiesto**:
- ✅ C1_002 mostra warning ma permette modifica
- ✅ C1_003 rimane disponibile per inserimento
- ✅ Sistema ricalcola dinamicamente ad ogni modifica

### **Test Case 3: Modifica Real-time**
**Azione**: Utente modifica metri già inseriti
1. C1_001: 60m → 40m (libera 20m)
2. C1_002: 50m → 30m (ora possibile)
3. C1_003: 25m → ✅ OK

**Comportamento Attuale**:
- ❌ setTimeout causa ritardi e race conditions
- ❌ Calcolo progressivo non considera ordine utente
- ❌ Blocchi non si aggiornano immediatamente

**Comportamento Richiesto**:
- ✅ Ricalcolo immediato ad ogni modifica
- ✅ Sblocco automatico quando metri disponibili
- ✅ Feedback visivo istantaneo

## 🚨 **Problemi Identificati**

### **1. Metri Default Errati**
```typescript
// PROBLEMA: Linea 272
[cavo.id_cavo]: cavo.metri_teorici?.toString() || '0'  // ❌ Dovrebbe essere '0'
```

### **2. Ordinamento Non Deterministico**
```typescript
// PROBLEMA: Linee 367-375
const caviOrdinati = [...caviSelezionati].sort((a, b) => {
  // Non mantiene ordine di selezione utente
  return 0  // ❌ Ordinamento casuale
})
```

### **3. Blocco Troppo Aggressivo**
```typescript
// PROBLEMA: Linee 382-391
if (metriResiduiSimulati - metri < 0) {
  caviBloccati.push(cavo.id_cavo)
  // Blocca TUTTI i cavi successivi
  remainingCavi.forEach(c => caviBloccati.push(c.id_cavo))  // ❌
  break  // ❌ Interrompe calcolo
}
```

### **4. Race Conditions**
```typescript
// PROBLEMA: Linea 349
setTimeout(() => validateAllMetri(), 100)  // ❌ Race condition
```

### **5. Force Over Mancante**
```typescript
// PROBLEMA: Linee 506-511
await caviApi.updateMetriPosati(
  cantiere.id_cantiere,
  cavo.id_cavo,
  metriPosati,
  bobina.id_bobina  // ❌ Manca force_over parameter
)
```

## 🎯 **Requisiti Funzionali**

### **Flusso Corretto Richiesto**:
1. **Selezione**: Utente seleziona cavi (metri default = 0)
2. **Inserimento Sequenziale**: Utente inserisce metri nell'ordine desiderato
3. **Calcolo Progressivo**: Sistema sottrae metri in tempo reale
4. **Warning OVER**: Sistema avvisa quando bobina sta per andare OVER
5. **Blocco Selettivo**: Solo il cavo che causa OVER viene bloccato
6. **Modifica Real-time**: Utente può modificare metri e sistema ricalcola
7. **Salvataggio**: Solo cavi validi vengono salvati con force_over se necessario

### **Comportamenti Attesi**:
- ✅ Metri default a 0 (non teorici)
- ✅ Ordine di inserimento mantenuto
- ✅ Calcolo progressivo in tempo reale
- ✅ Blocco solo del cavo che causa OVER
- ✅ Possibilità di modifica e ricalcolo
- ✅ Force over automatico quando necessario
- ✅ Feedback visivo chiaro e immediato

## 🔧 **Soluzioni Proposte**

### **1. Fix Metri Default**
```typescript
// SOLUZIONE
[cavo.id_cavo]: '0'  // ✅ Sempre 0
```

### **2. Fix Ordinamento**
```typescript
// SOLUZIONE: Mantieni ordine di selezione
const caviOrdinati = [...caviSelezionati]  // ✅ Ordine originale
```

### **3. Fix Calcolo Progressivo**
```typescript
// SOLUZIONE: Calcolo non-bloccante
for (const cavo of caviOrdinati) {
  if (metriResiduiSimulati - metri < 0) {
    caviBloccati.push(cavo.id_cavo)  // ✅ Solo questo cavo
    // NON interrompere il ciclo
  } else {
    metriResiduiSimulati -= metri
    caviValidi.push(cavo.id_cavo)
  }
}
```

### **4. Fix Validazione Real-time**
```typescript
// SOLUZIONE: Rimozione setTimeout
const handleMetriChange = (cavoId: string, value: string) => {
  setCaviMetri(prev => ({ ...prev, [cavoId]: value }))
  validateMetri(cavoId, value)
  validateAllMetri()  // ✅ Immediato
}
```

### **5. Fix Force Over**
```typescript
// SOLUZIONE: Aggiungere force_over
await caviApi.updateMetriPosati(
  cantiere.id_cantiere,
  cavo.id_cavo,
  metriPosati,
  bobina.id_bobina,
  needsForceOver  // ✅ Calcolo automatico
)
```
