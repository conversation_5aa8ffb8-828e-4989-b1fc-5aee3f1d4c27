'use client'

import { useState, useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'

interface ContextMenuProps {
  children: React.ReactNode
  items: ContextMenuItem[]
  onAction: (action: string, data?: any) => void
  disabled?: boolean
}

interface ContextMenuItem {
  id: string
  label: string
  icon?: React.ReactNode
  action: string
  disabled?: boolean
  separator?: boolean
  submenu?: ContextMenuItem[]
  color?: 'default' | 'warning' | 'danger'
}

interface ContextMenuPosition {
  x: number
  y: number
}

export function ContextMenuCustom({ children, items, onAction, disabled = false }: ContextMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [position, setPosition] = useState<ContextMenuPosition>({ x: 0, y: 0 })
  const [contextData, setContextData] = useState<any>(null)
  const menuRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen])

  const handleContextMenu = (event: React.MouseEvent) => {
    if (disabled) return
    
    event.preventDefault()
    event.stopPropagation()

    const rect = triggerRef.current?.getBoundingClientRect()
    const x = event.clientX
    const y = event.clientY

    // Adjust position if menu would go off screen
    const menuWidth = 200 // Estimated menu width
    const menuHeight = items.length * 40 // Estimated menu height
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    const adjustedX = x + menuWidth > viewportWidth ? x - menuWidth : x
    const adjustedY = y + menuHeight > viewportHeight ? y - menuHeight : y

    setPosition({ x: adjustedX, y: adjustedY })
    setContextData(event.currentTarget.dataset)
    setIsOpen(true)
  }

  const handleItemClick = (item: ContextMenuItem) => {
    if (item.disabled) return
    
    setIsOpen(false)
    onAction(item.action, contextData)
  }

  const getItemColorClass = (color?: string) => {
    switch (color) {
      case 'warning':
        return 'text-amber-600 hover:bg-amber-50'
      case 'danger':
        return 'text-red-600 hover:bg-red-50'
      default:
        return 'text-gray-700 hover:bg-gray-100'
    }
  }

  const renderMenu = () => {
    if (!isOpen) return null

    return createPortal(
      <div
        ref={menuRef}
        className="fixed z-50 min-w-[200px] bg-white border border-gray-200 rounded-md shadow-lg py-1"
        style={{
          left: position.x,
          top: position.y,
        }}
      >
        {items.map((item, index) => {
          if (item.separator) {
            return <div key={`separator-${index}`} className="border-t border-gray-200 my-1" />
          }

          return (
            <button
              key={item.id}
              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${
                item.disabled 
                  ? 'text-gray-400 cursor-not-allowed' 
                  : getItemColorClass(item.color)
              }`}
              onClick={() => handleItemClick(item)}
              disabled={item.disabled}
            >
              {item.icon && <span className="w-4 h-4">{item.icon}</span>}
              <span>{item.label}</span>
            </button>
          )
        })}
      </div>,
      document.body
    )
  }

  return (
    <>
      <div
        ref={triggerRef}
        onContextMenu={handleContextMenu}
        className="w-full h-full"
      >
        {children}
      </div>
      {renderMenu()}
    </>
  )
}
