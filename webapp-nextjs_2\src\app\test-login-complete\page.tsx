'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { User, Building2, Shield, CheckCircle, XCircle, Loader2 } from 'lucide-react'

export default function TestLoginCompletePage() {
  const [testResults, setTestResults] = useState<Record<string, any>>({})
  const [isLoading, setIsLoading] = useState(false)
  const { login, loginCantiere, logout, user, cantiere, isAuthenticated } = useAuth()

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setIsLoading(true)
    try {
      const result = await testFn()
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, result }
      }))
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, error: error.message || error }
      }))
    } finally {
      setIsLoading(false)
    }
  }

  const testAdminLogin = async () => {
    await logout()
    const result = await login('admin', 'admin')
    return result
  }

  const testUserLogin = async () => {
    await logout()
    const result = await login('testuser2', 'test123')
    return result
  }

  const testCantiereLogin = async () => {
    await logout()
    const result = await loginCantiere('MU258UC', 'test123')
    return result
  }

  const testInvalidLogin = async () => {
    await logout()
    const result = await login('invalid', 'invalid')
    return result
  }

  const renderTestResult = (testName: string, result: any) => {
    if (!result) return null

    return (
      <div className={`p-3 rounded-lg border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
        <div className="flex items-center gap-2 mb-2">
          {result.success ? (
            <CheckCircle className="w-4 h-4 text-green-600" />
          ) : (
            <XCircle className="w-4 h-4 text-red-600" />
          )}
          <span className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
            {testName}
          </span>
        </div>
        <pre className={`text-xs ${result.success ? 'text-green-700' : 'text-red-700'}`}>
          {JSON.stringify(result.success ? result.result : result.error, null, 2)}
        </pre>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <Shield className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-slate-900">Test Sistema Login Completo</h1>
          <p className="text-slate-600">webapp-nextjs_2 - Verifica tutti i tipi di autenticazione</p>
        </div>

        {/* Stato Corrente */}
        <Card>
          <CardHeader>
            <CardTitle>Stato Autenticazione Corrente</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant={isAuthenticated ? 'default' : 'secondary'}>
                  {isAuthenticated ? 'Autenticato' : 'Non Autenticato'}
                </Badge>
              </div>
              {user && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="font-medium text-blue-900">Utente: {user.username}</p>
                  <p className="text-blue-700">Ruolo: {user.ruolo}</p>
                  <p className="text-blue-700">ID: {user.id_utente}</p>
                </div>
              )}
              {cantiere && (
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="font-medium text-green-900">Cantiere: {cantiere.commessa}</p>
                  <p className="text-green-700">Codice: {cantiere.codice_univoco}</p>
                  <p className="text-green-700">ID: {cantiere.id_cantiere}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Test Buttons */}
        <Card>
          <CardHeader>
            <CardTitle>Test di Login</CardTitle>
            <CardDescription>
              Testa tutti i tipi di login supportati dal sistema
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={() => runTest('Admin Login', testAdminLogin)}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Shield className="w-4 h-4" />
                Test Admin Login
                {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
              </Button>

              <Button
                onClick={() => runTest('User Login', testUserLogin)}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <User className="w-4 h-4" />
                Test User Login
                {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
              </Button>

              <Button
                onClick={() => runTest('Cantiere Login', testCantiereLogin)}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Building2 className="w-4 h-4" />
                Test Cantiere Login
                {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
              </Button>

              <Button
                onClick={() => runTest('Invalid Login', testInvalidLogin)}
                disabled={isLoading}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <XCircle className="w-4 h-4" />
                Test Invalid Login
                {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
              </Button>
            </div>

            <div className="mt-4">
              <Button
                onClick={logout}
                disabled={isLoading}
                variant="secondary"
                className="w-full"
              >
                Logout
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Risultati Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(testResults).map(([testName, result]) => (
                  <div key={testName}>
                    {renderTestResult(testName, result)}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Credenziali di Test */}
        <Card>
          <CardHeader>
            <CardTitle>Credenziali di Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Admin/Owner</h4>
                <p className="text-blue-700">Username: admin</p>
                <p className="text-blue-700">Password: admin</p>
                <p className="text-blue-700">Ruolo: owner</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">User Standard</h4>
                <p className="text-green-700">Username: testuser2</p>
                <p className="text-green-700">Password: test123</p>
                <p className="text-green-700">Ruolo: user</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2">Cantiere</h4>
                <p className="text-orange-700">Codice: MU258UC</p>
                <p className="text-orange-700">Password: test123</p>
                <p className="text-orange-700">Ruolo: cantieri_user</p>
                <p className="text-orange-600 text-xs mt-1">⚠️ Login cantiere ha errori backend</p>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
