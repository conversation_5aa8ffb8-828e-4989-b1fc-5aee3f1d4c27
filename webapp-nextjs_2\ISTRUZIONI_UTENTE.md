# 🚀 CABLYS Next.js - Istruzioni per l'Utente

## ✅ Sistema Implementato e Testato

Il run system per webapp-nextjs è stato **implementato con successo** e testato completamente. Tutti i servizi funzionano correttamente.

## 🎯 Come Avviare il Sistema

### **Metodo 1: <PERSON> (Raccomandato)**

1. **<PERSON><PERSON><PERSON> click** su `CABLYS_MANAGER.bat`
2. Scegli l'opzione desiderata dal menu:
   - `1` = Run System Semplice
   - `2` = Run System Avanzato  
   - `5` = Controllo Stato Servizi

### **Metodo 2: Avvio Rapido**

**Per uso quotidiano:**
- <PERSON><PERSON><PERSON> click su `START_CABLYS.bat`

**Per funzionalità avanzate:**
- <PERSON><PERSON><PERSON> click su `START_CABLYS_ADVANCED.bat`

### **Metodo 3: Linea di Comando**

```bash
# Sistema semplice (raccomandato per sviluppo)
python run_system.py

# Sistema avanzato (con configurazioni JSON)
python run_system_advanced.py

# Ambiente produzione
python run_production.py
```

## 🌐 Servizi Disponibili

Dopo l'avvio, saranno disponibili:

- **🔧 Backend API**: http://localhost:8001
- **🎨 Frontend Next.js**: http://localhost:3000

## 🔍 Monitoraggio Sistema

Per verificare lo stato dei servizi:

```bash
# Check rapido
python monitor_system.py

# Monitoraggio continuo
python monitor_system.py --continuous
```

## ⚙️ Configurazioni

### **File di Configurazione Disponibili:**
- `run_config.json` - Configurazione sviluppo
- `run_config_production.json` - Configurazione produzione
- `.env.local` - Variabili ambiente Next.js

### **Personalizzazione:**
Puoi modificare i file JSON per cambiare:
- Porte utilizzate
- Modalità di avvio
- Timeout
- Logging
- Auto-apertura browser

## 🛠️ Risoluzione Problemi

### **Errore "Porta già in uso"**
- Il sistema rileva automaticamente se i servizi sono già attivi
- Continua con il frontend se il backend è già in esecuzione

### **Errore "node_modules non trovato"**
```bash
npm install
```

### **Errore "Dipendenze mancanti"**
- Installa Node.js: https://nodejs.org/
- Installa Python: https://python.org/
- Installa uvicorn: `pip install uvicorn`

### **Problemi Database**
- Verifica che PostgreSQL sia in esecuzione
- Controlla credenziali: user=postgres, password=Taranto, db=cantieri

## 🎉 Funzionalità Implementate

### ✅ **Sistema di Avvio**
- Verifica automatica dipendenze
- Avvio simultaneo backend + frontend
- Gestione porte occupate
- Terminazione pulita (Ctrl+C)

### ✅ **Monitoraggio**
- Status check in tempo reale
- Verifica connessione database
- Monitoraggio continuo

### ✅ **Configurazione**
- File JSON personalizzabili
- Modalità sviluppo/produzione
- Parametri configurabili

### ✅ **User Experience**
- Manager interattivo
- Launcher grafici Windows
- Output colorato e chiaro
- Documentazione completa

## 📁 File Principali

### **Per l'Utente Finale:**
- `CABLYS_MANAGER.bat` - Manager completo
- `START_CABLYS.bat` - Avvio rapido
- `START_CABLYS_ADVANCED.bat` - Avvio avanzato

### **Per Sviluppatori:**
- `run_system.py` - Run system base
- `run_system_advanced.py` - Run system configurabile
- `monitor_system.py` - Sistema monitoraggio
- `cablys_manager.py` - Manager Python

### **Configurazioni:**
- `run_config.json` - Config sviluppo
- `run_config_production.json` - Config produzione

## 🏆 Raccomandazioni

### **Per Sviluppo Quotidiano:**
```cmd
START_CABLYS.bat
```

### **Per Testing e Debug:**
```cmd
CABLYS_MANAGER.bat
# Scegli opzione 2 (Sistema Avanzato)
```

### **Per Monitoraggio:**
```cmd
CABLYS_MANAGER.bat
# Scegli opzione 5 (Status Check)
```

## 🎯 Prossimi Passi

1. **Testa il sistema** con `START_CABLYS.bat`
2. **Verifica i servizi** su http://localhost:3000 e http://localhost:8001
3. **Personalizza le configurazioni** se necessario
4. **Usa il manager** per funzionalità avanzate

---

**🚀 Il run system per webapp-nextjs è pronto per l'uso!**

Per qualsiasi problema, consulta i file di documentazione o usa il sistema di monitoraggio integrato.
