'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import {
  Building2,
  Plus,
  Settings,
  Trash2,
  Eye,
  EyeOff,
  Search,
  Copy,
  Calendar,
  MapPin,
  User,
  Loader2,
  AlertCircle,
  Lock,
  Mail,
  Shield,
  CheckCircle,
  BarChart3,
  Edit,
  ArrowRight
} from 'lucide-react'

export default function CantieriPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [cantieriStats, setCantieriStats] = useState<Record<number, { percentuale_avanzamento: number }>>({})
  const [statsLoading, setStatsLoading] = useState(false)


  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [showViewPasswordDialog, setShowViewPasswordDialog] = useState(false)
  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null)
  const [formData, setFormData] = useState({
    commessa: '',
    descrizione: '',
    nome_cliente: '',
    indirizzo_cantiere: '',
    citta_cantiere: '',
    nazione_cantiere: '',
    password_cantiere: '',
    codice_univoco: ''
  })
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [passwordMode, setPasswordMode] = useState<'change'>('change')
  const [revealedPassword, setRevealedPassword] = useState('')
  const [showRevealedPassword, setShowRevealedPassword] = useState(false)
  const [viewPasswordLoading, setViewPasswordLoading] = useState(false)
  const [passwordVisibility, setPasswordVisibility] = useState<Record<number, boolean>>({})
  const [revealedPasswords, setRevealedPasswords] = useState<Record<number, string>>({})

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      loadCantieri()
    }
  }, [isAuthenticated])

  const loadCantieri = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantieri()
      setCantieri(data)

      // Carica le statistiche per ogni cantiere
      await loadCantieriStatistics(data)
    } catch (error) {
      setError('Errore nel caricamento dei cantieri')
    } finally {
      setLoading(false)
    }
  }

  const loadCantieriStatistics = async (cantieriData: Cantiere[]) => {
    try {
      setStatsLoading(true)
      const statsPromises = cantieriData.map(async (cantiere) => {
        try {
          const stats = await cantieriApi.getCantiereStatistics(cantiere.id_cantiere)
          return { id: cantiere.id_cantiere, stats }
        } catch (error) {
          console.error(`Errore nel caricamento statistiche cantiere ${cantiere.id_cantiere}:`, error)
          return { id: cantiere.id_cantiere, stats: { percentuale_avanzamento: 0 } }
        }
      })

      const results = await Promise.all(statsPromises)
      const statsMap = results.reduce((acc, { id, stats }) => {
        acc[id] = stats
        return acc
      }, {} as Record<number, { percentuale_avanzamento: number }>)

      setCantieriStats(statsMap)
    } catch (error) {
      console.error('Errore nel caricamento delle statistiche:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  const handleCreateCantiere = async () => {
    try {
      await cantieriApi.createCantiere(formData)
      setShowCreateDialog(false)
      setFormData({
        commessa: '',
        descrizione: '',
        nome_cliente: '',
        indirizzo_cantiere: '',
        citta_cantiere: '',
        nazione_cantiere: '',
        password_cantiere: '',
        codice_univoco: ''
      })
      loadCantieri()
    } catch (error) {
      setError('Errore nella creazione del cantiere')
    }
  }

  const handleEditCantiere = async () => {
    if (!selectedCantiere) return
    
    try {
      await cantieriApi.updateCantiere(selectedCantiere.id_cantiere, formData)
      setShowEditDialog(false)
      setSelectedCantiere(null)
      loadCantieri()
    } catch (error) {
      setError('Errore nella modifica del cantiere')
    }
  }

  const handleSelectCantiere = (cantiere: Cantiere) => {
    // Salva il cantiere selezionato nel localStorage
    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())
    localStorage.setItem('selectedCantiereName', cantiere.commessa)
    
    // Naviga alla pagina del cantiere specifico
    router.push(`/cantieri/${cantiere.id_cantiere}`)
  }

  const openEditDialog = (cantiere: Cantiere) => {
    setSelectedCantiere(cantiere)
    setFormData({
      commessa: cantiere.commessa || '',
      descrizione: cantiere.descrizione || '',
      nome_cliente: cantiere.nome_cliente || '',
      indirizzo_cantiere: cantiere.indirizzo_cantiere || '',
      citta_cantiere: cantiere.citta_cantiere || '',
      nazione_cantiere: cantiere.nazione_cantiere || '',
      password_cantiere: cantiere.password_cantiere || '',
      codice_univoco: cantiere.codice_univoco || ''
    })
    setShowEditDialog(true)
  }

  const handleRecoverPasswordDirect = async () => {
    if (!selectedCantiere) return

    try {
      setLoading(true)
      setError('')

      // Chiamata API reale per recupero diretto password
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/view-password`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Errore nel recupero password')
      }

      const data = await response.json()
      setRevealedPassword(data.password_cantiere)
      setShowRevealedPassword(true)
      setError('')

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Errore nel recupero password')
      setShowRevealedPassword(false)
    } finally {
      setLoading(false)
    }
  }

  const handleViewPasswordDirect = async (cantiere: Cantiere) => {
    try {
      setViewPasswordLoading(true)
      setError('')

      // Chiamata API per visualizzare la password
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
      const token = localStorage.getItem('token') || localStorage.getItem('access_token')
      const response = await fetch(`${backendUrl}/api/cantieri/${cantiere.id_cantiere}/view-password`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Errore nel recupero password')
      }

      const data = await response.json()
      setSelectedCantiere(cantiere)
      setRevealedPassword(data.password_cantiere)
      setShowViewPasswordDialog(true)
      setError('')

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Errore nel recupero password')
    } finally {
      setViewPasswordLoading(false)
    }
  }



  const handleChangePassword = async () => {
    if (!selectedCantiere) return

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('Le password non coincidono')
      return
    }

    if (!passwordData.currentPassword) {
      setError('Inserisci la password attuale per confermare il cambio')
      return
    }

    if (!passwordData.newPassword || passwordData.newPassword.length < 6) {
      setError('La nuova password deve essere di almeno 6 caratteri')
      return
    }

    try {
      setLoading(true)
      setError('')

      // Chiamata API reale per cambio password
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          password_attuale: passwordData.currentPassword,
          password_nuova: passwordData.newPassword,
          conferma_password: passwordData.confirmPassword
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Errore nel cambio password')
      }

      const data = await response.json()

      if (data.success) {
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        })
        setShowPasswordDialog(false)
        setError('')
        alert(data.message || 'Password cambiata con successo')
      } else {
        throw new Error(data.message || 'Errore nel cambio password')
      }

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Errore nel cambio password')
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // Visual feedback could be added here (toast notification)
    } catch (err) {
    }
  }

  const togglePasswordVisibility = async (cantiere: Cantiere) => {
    const cantiereId = cantiere.id_cantiere
    const isCurrentlyVisible = passwordVisibility[cantiereId]

    if (isCurrentlyVisible) {
      // Nascondi la password
      setPasswordVisibility(prev => ({ ...prev, [cantiereId]: false }))
      setRevealedPasswords(prev => ({ ...prev, [cantiereId]: '' }))
    } else {
      // Mostra la password - carica se non già caricata
      if (!revealedPasswords[cantiereId]) {
        try {
          setViewPasswordLoading(true)
          const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
          const token = localStorage.getItem('token') || localStorage.getItem('access_token')
          const response = await fetch(`${backendUrl}/api/cantieri/${cantiereId}/view-password`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.detail || 'Errore nel recupero password')
          }

          const data = await response.json()
          setRevealedPasswords(prev => ({ ...prev, [cantiereId]: data.password_cantiere }))
          setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Errore nel recupero password')
        } finally {
          setViewPasswordLoading(false)
        }
      } else {
        // Password già caricata, mostra semplicemente
        setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))
      }
    }
  }

  const filteredCantieri = cantieri.filter(cantiere =>
    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="max-w-[90%] mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca per commessa, descrizione o cliente..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full"
            />
          </div>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">
              <Plus className="mr-2 h-4 w-4" />
              Nuovo Cantiere
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Crea Nuovo Cantiere</DialogTitle>
              <DialogDescription>
                Inserisci i dettagli del nuovo cantiere
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="commessa" className="text-right">
                  Commessa
                </Label>
                <Input
                  id="commessa"
                  value={formData.commessa}
                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="descrizione" className="text-right">
                  Descrizione
                </Label>
                <Input
                  id="descrizione"
                  value={formData.descrizione}
                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="nome_cliente" className="text-right">
                  Cliente
                </Label>
                <Input
                  id="nome_cliente"
                  value={formData.nome_cliente}
                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password_cantiere" className="text-right">
                  Password
                </Label>
                <Input
                  id="password_cantiere"
                  type="password"
                  value={formData.password_cantiere}
                  onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleCreateCantiere} className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">Crea Cantiere</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <div className="mb-4 p-4 border border-red-200 rounded-lg bg-red-50">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : filteredCantieri.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nessun cantiere trovato</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm ? 'Nessun cantiere corrisponde ai criteri di ricerca' : 'Crea il tuo primo cantiere per iniziare'}
            </p>
            {!searchTerm && (
              <Button
                onClick={() => setShowCreateDialog(true)}
                className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
              >
                <Plus className="mr-2 h-4 w-4" />
                Crea Primo Cantiere
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <Table>
            <TableHeader>
              <TableRow className="border-b border-gray-200">
                <TableHead className="font-semibold text-gray-700">Commessa</TableHead>
                <TableHead className="font-semibold text-gray-700">Descrizione</TableHead>
                <TableHead className="font-semibold text-gray-700">Cliente</TableHead>
                <TableHead className="font-semibold text-gray-700">Data Creazione</TableHead>
                <TableHead className="font-semibold text-gray-700">Codice Accesso</TableHead>
                <TableHead className="font-semibold text-gray-700">Password Cantiere</TableHead>
                <TableHead className="font-semibold text-gray-700 w-32">Avanzamento</TableHead>
                <TableHead className="font-semibold text-gray-700 text-center">Progresso %</TableHead>
                <TableHead className="text-center font-semibold text-gray-700 w-48">Azioni</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCantieri.map((cantiere) => (
                <TableRow key={cantiere.id_cantiere} className="hover:bg-gray-50/50 transition-colors">
                  <TableCell className="font-semibold text-gray-900 py-4">{cantiere.commessa}</TableCell>
                  <TableCell className="text-gray-700 py-4">{cantiere.descrizione}</TableCell>
                  <TableCell className="text-gray-700 py-4">{cantiere.nome_cliente}</TableCell>
                  <TableCell className="text-gray-600 py-4">{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      <code className="text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200">
                        {cantiere.codice_univoco}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors"
                        title="Copia codice"
                        onClick={() => copyToClipboard(cantiere.codice_univoco)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <div className="flex items-center gap-3">
                      {/* Status e Password - Assumiamo che tutti i cantieri abbiano una password configurata */}
                      <div className="flex items-center gap-2">
                        {passwordVisibility[cantiere.id_cantiere] && revealedPasswords[cantiere.id_cantiere] ? (
                          // Password visibile
                          <div className="flex items-center gap-2">
                            <code className="text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono">
                              {revealedPasswords[cantiere.id_cantiere]}
                            </code>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors"
                              title="Nascondi password"
                              onClick={() => togglePasswordVisibility(cantiere)}
                            >
                              <EyeOff className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          // Password nascosta
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm text-gray-600 font-medium">Configurata</span>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                              title="Mostra password"
                              onClick={() => togglePasswordVisibility(cantiere)}
                              disabled={viewPasswordLoading}
                            >
                              {viewPasswordLoading ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      {statsLoading ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                          <span className="text-sm text-gray-500">Caricamento...</span>
                        </div>
                      ) : (
                        <>
                          {/* Barra di Progresso Migliorata */}
                          <div className="flex-1 min-w-[120px]">
                            <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                              <div
                                className={`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${
                                  (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 90
                                    ? 'bg-gradient-to-r from-green-500 to-green-600'
                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 75
                                    ? 'bg-gradient-to-r from-blue-500 to-blue-600'
                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 50
                                    ? 'bg-gradient-to-r from-yellow-500 to-yellow-600'
                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 25
                                    ? 'bg-gradient-to-r from-orange-500 to-orange-600'
                                    : 'bg-gradient-to-r from-red-500 to-red-600'
                                }`}
                                style={{
                                  width: `${Math.min(cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0, 100)}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="py-4 text-center">
                    {statsLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400 mx-auto" />
                    ) : (
                      <div className="flex items-center justify-center gap-1">
                        <BarChart3 className="h-4 w-4 text-gray-500" />
                        <span className={`text-sm font-semibold ${
                          (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 90
                            ? 'text-green-700'
                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 75
                            ? 'text-blue-700'
                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 50
                            ? 'text-yellow-700'
                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 25
                            ? 'text-orange-700'
                            : 'text-red-700'
                        }`}>
                          {(cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0).toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="text-center py-4">
                    <div className="flex items-center justify-center gap-2">
                      {/* Pulsante Modifica Cantiere */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openEditDialog(cantiere)}
                        className="h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out"
                        title="Modifica dati cantiere"
                      >
                        <Edit className="h-4 w-4 mr-1.5" />
                        Modifica
                      </Button>

                      {/* Pulsante Principale - Accedi al Cantiere (stile topbar senza scaling) */}
                      <Button
                        size="sm"
                        onClick={() => handleSelectCantiere(cantiere)}
                        className="h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out"
                        title="Accedi al cantiere"
                      >
                        <ArrowRight className="h-4 w-4 mr-1.5" />
                        Accedi
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Dialog di modifica */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Modifica Cantiere</DialogTitle>
            <DialogDescription>
              Modifica i dettagli del cantiere selezionato
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-commessa" className="text-right">
                Commessa
              </Label>
              <Input
                id="edit-commessa"
                value={formData.commessa}
                onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-descrizione" className="text-right">
                Descrizione
              </Label>
              <Input
                id="edit-descrizione"
                value={formData.descrizione}
                onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-nome_cliente" className="text-right">
                Cliente
              </Label>
              <Input
                id="edit-nome_cliente"
                value={formData.nome_cliente}
                onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-indirizzo_cantiere" className="text-right">
                Indirizzo
              </Label>
              <Input
                id="edit-indirizzo_cantiere"
                value={formData.indirizzo_cantiere}
                onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-citta_cantiere" className="text-right">
                Città
              </Label>
              <Input
                id="edit-citta_cantiere"
                value={formData.citta_cantiere}
                onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-nazione_cantiere" className="text-right">
                Nazione
              </Label>
              <Input
                id="edit-nazione_cantiere"
                value={formData.nazione_cantiere}
                onChange={(e) => setFormData({ ...formData, nazione_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Password
              </Label>
              <div className="col-span-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowPasswordDialog(true)
                  }}
                  className="w-full h-10 justify-start text-left bg-gray-50 hover:bg-gray-100 border-gray-200 hover:border-gray-300 transition-colors"
                >
                  <Lock className="h-4 w-4 mr-2 text-gray-500" />
                  <span className="text-gray-700">Modifica Password</span>
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowEditDialog(false)} className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">
              Annulla
            </Button>
            <Button onClick={handleEditCantiere} className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">Salva Modifiche</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog gestione password ottimale */}
      <Dialog open={showPasswordDialog} onOpenChange={(open) => {
        setShowPasswordDialog(open)
        if (!open) {
          setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' })
          setError('')
        }
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Gestione Password - {selectedCantiere?.commessa}
            </DialogTitle>
            <DialogDescription>
              Modifica la password di accesso al cantiere
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Cambia Password
              </h3>
              <p className="text-sm text-gray-600">
                Inserisci la password attuale e la nuova password
              </p>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="current-password-change">Password Attuale</Label>
                    <Input
                      id="current-password-change"
                      type="password"
                      placeholder="Password attuale per conferma"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-password">Nuova Password</Label>
                    <Input
                      id="new-password"
                      type="password"
                      placeholder="Inserisci la nuova password"
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="confirm-password">Conferma Nuova Password</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      placeholder="Conferma la nuova password"
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                    />
                  </div>
                  <Button
                    onClick={handleChangePassword}
                    disabled={loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                    className="w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Settings className="mr-2 h-4 w-4" />}
                    Cambia Password
                  </Button>
                </div>
              </div>

            {/* Messaggio di errore */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <span className="font-medium text-red-800">Errore</span>
                </div>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPasswordDialog(false)}
            >
              Chiudi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog visualizzazione password semplice */}
      <Dialog open={showViewPasswordDialog} onOpenChange={(open) => {
        setShowViewPasswordDialog(open)
        if (!open) {
          setRevealedPassword('')
          setSelectedCantiere(null)
          setError('')
        }
      }}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-green-600" />
              Password Cantiere - {selectedCantiere?.commessa}
            </DialogTitle>
            <DialogDescription>
              Password per l'accesso al cantiere con codice: <code className="bg-muted px-2 py-1 rounded">{selectedCantiere?.codice_univoco}</code>
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {revealedPassword && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-800">Password del Cantiere</span>
                </div>
                <div className="flex items-center gap-2">
                  <code className="flex-1 text-lg font-mono bg-white p-3 rounded border border-green-300 text-center">
                    {revealedPassword}
                  </code>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(revealedPassword)}
                    className="text-green-600 hover:bg-green-50 border-green-300"
                    title="Copia password"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-green-700 mt-2">
                  Utilizza questa password insieme al codice univoco <strong>{selectedCantiere?.codice_univoco}</strong> per accedere al cantiere.
                </p>
              </div>
            )}

            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <span className="font-medium text-red-800">Errore</span>
                </div>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowViewPasswordDialog(false)}
            >
              Chiudi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
