(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6222],{30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>l});var s=t(95155);t(12115);var r=t(99708),n=t(74466),i=t(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:t,size:n,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:n,className:a})),...c})}},42712:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>_});var s=t(95155),r=t(12115),n=t(35695),i=t(66695),o=t(30285),l=t(62523),c=t(85127),d=t(54165),m=t(85057),u=t(40283),x=t(25731),g=t(51154),h=t(47924),p=t(53904),v=t(84616),f=t(24357),b=t(78749),j=t(92657),N=t(72713),w=t(13717),y=t(92138);function _(){let{user:e,isAuthenticated:a,isLoading:t}=(0,u.A)(),_=(0,n.useRouter)(),[z,C]=(0,r.useState)([]),[A,k]=(0,r.useState)(!0),[S,E]=(0,r.useState)(""),[I,F]=(0,r.useState)(""),[L,$]=(0,r.useState)({}),[P,J]=(0,r.useState)(!1),[D,M]=(0,r.useState)({}),[B,Z]=(0,r.useState)({}),[T,G]=(0,r.useState)(!1),[H,O]=(0,r.useState)(!1),[X,R]=(0,r.useState)(!1),[V,Q]=(0,r.useState)(null),[U,W]=(0,r.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[Y,q]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[K,ee]=(0,r.useState)(!1);(0,r.useEffect)(()=>{t||a||_.push("/login")},[a,t,_]),(0,r.useEffect)(()=>{a&&ea()},[a]);let ea=async()=>{try{k(!0);let e=await x._I.getCantieri();C(e),await et(e)}catch(e){E("Errore nel caricamento dei cantieri")}finally{k(!1)}},et=async e=>{try{J(!0);let a=e.map(async e=>{try{let a=await x._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:a}}catch(a){return console.error("Errore nel caricamento statistiche cantiere ".concat(e.id_cantiere,":"),a),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),t=(await Promise.all(a)).reduce((e,a)=>{let{id:t,stats:s}=a;return e[t]=s,e},{});$(t)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{J(!1)}},es=async e=>{let a=e.id_cantiere;if(D[a])M(e=>({...e,[a]:!1})),Z(e=>({...e,[a]:""}));else if(B[a])M(e=>({...e,[a]:!0}));else try{ee(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),t=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(a,"/view-password"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json();throw Error(e.detail||"Errore nel recupero password")}let s=await t.json();Z(e=>({...e,[a]:s.password_cantiere})),M(e=>({...e,[a]:!0}))}catch(e){E(e instanceof Error?e.message:"Errore nel recupero password")}finally{ee(!1)}},er=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),_.push("/cantieri/".concat(e.id_cantiere))},en=e=>{Q(e),W({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),O(!0)},ei=async()=>{try{await x._I.createCantiere(U),G(!1),W({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"Italia",password_cantiere:"",codice_univoco:""}),ea()}catch(e){E("Errore nella creazione del cantiere")}},eo=async()=>{z.length>0&&await et(z)},el=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Failed to copy to clipboard:",e)}},ec=z.filter(e=>{var a,t;return e.commessa.toLowerCase().includes(I.toLowerCase())||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(I.toLowerCase()))||(null==(t=e.nome_cliente)?void 0:t.toLowerCase().includes(I.toLowerCase()))});return t?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(g.A,{className:"h-8 w-8 animate-spin"})}):(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)("div",{className:"relative w-80",children:[(0,s.jsx)(h.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(l.p,{placeholder:"Cerca cantieri...",value:I,onChange:e=>F(e.target.value),className:"pl-8 w-full"})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(o.$,{variant:"outline",onClick:eo,disabled:P,className:"relative overflow-hidden border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 ease-in-out",title:"Aggiorna statistiche avanzamento",children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 ".concat(P?"animate-spin":"")}),P?"Aggiornando...":"Aggiorna Avanzamento"]}),(0,s.jsxs)(d.lG,{open:T,onOpenChange:G,children:[(0,s.jsx)(d.zM,{asChild:!0,children:(0,s.jsxs)(o.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25",children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,s.jsxs)(d.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(d.c7,{children:[(0,s.jsx)(d.L3,{children:"Crea Nuovo Cantiere"}),(0,s.jsx)(d.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(m.J,{htmlFor:"commessa",className:"text-right",children:"Commessa *"}),(0,s.jsx)(l.p,{id:"commessa",value:U.commessa,onChange:e=>W({...U,commessa:e.target.value}),className:"col-span-3",placeholder:"Nome commessa"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(m.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,s.jsx)(l.p,{id:"descrizione",value:U.descrizione,onChange:e=>W({...U,descrizione:e.target.value}),className:"col-span-3",placeholder:"Descrizione cantiere"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(m.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,s.jsx)(l.p,{id:"nome_cliente",value:U.nome_cliente,onChange:e=>W({...U,nome_cliente:e.target.value}),className:"col-span-3",placeholder:"Nome cliente"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(m.J,{htmlFor:"indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,s.jsx)(l.p,{id:"indirizzo_cantiere",value:U.indirizzo_cantiere,onChange:e=>W({...U,indirizzo_cantiere:e.target.value}),className:"col-span-3",placeholder:"Indirizzo cantiere"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(m.J,{htmlFor:"citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,s.jsx)(l.p,{id:"citta_cantiere",value:U.citta_cantiere,onChange:e=>W({...U,citta_cantiere:e.target.value}),className:"col-span-3",placeholder:"Citt\xe0"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(m.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,s.jsx)(l.p,{id:"password_cantiere",type:"password",value:U.password_cantiere,onChange:e=>W({...U,password_cantiere:e.target.value}),className:"col-span-3",placeholder:"Password cantiere"})]})]}),(0,s.jsxs)(d.Es,{children:[(0,s.jsx)(o.$,{variant:"outline",onClick:()=>G(!1),children:"Annulla"}),(0,s.jsx)(o.$,{onClick:ei,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25",children:"Crea Cantiere"})]})]})]})]})]}),S&&(0,s.jsx)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-700",children:S})}),(0,s.jsx)(i.Zp,{children:(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(c.XI,{children:[(0,s.jsx)(c.A0,{children:(0,s.jsxs)(c.Hj,{className:"border-b border-gray-200",children:[(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700 w-32",children:"Avanzamento"}),(0,s.jsx)(c.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,s.jsx)(c.nd,{className:"text-center font-semibold text-gray-700 w-48",children:"Azioni"})]})}),(0,s.jsx)(c.BF,{children:ec.map(e=>{var a,t,r,n,i,l,d,m,u,x;return(0,s.jsxs)(c.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,s.jsx)(c.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,s.jsx)(c.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,s.jsx)(c.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,s.jsx)(c.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,s.jsx)(c.nA,{className:"py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>el(e.codice_univoco),children:(0,s.jsx)(f.A,{className:"h-3 w-3"})})]})}),(0,s.jsx)(c.nA,{className:"py-4",children:(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:D[e.id_cantiere]&&B[e.id_cantiere]?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:B[e.id_cantiere]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>es(e),children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]}):(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>es(e),disabled:K,children:K?(0,s.jsx)(g.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(j.A,{className:"h-4 w-4"})})]})})})}),(0,s.jsx)(c.nA,{className:"py-4",children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:P?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,s.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out shadow-sm ".concat(((null==(a=L[e.id_cantiere])?void 0:a.percentuale_avanzamento)||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":((null==(t=L[e.id_cantiere])?void 0:t.percentuale_avanzamento)||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":((null==(r=L[e.id_cantiere])?void 0:r.percentuale_avanzamento)||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":((null==(n=L[e.id_cantiere])?void 0:n.percentuale_avanzamento)||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"),style:{width:"".concat(Math.min((null==(i=L[e.id_cantiere])?void 0:i.percentuale_avanzamento)||0,100),"%")}})})})})})}),(0,s.jsx)(c.nA,{className:"py-4 text-center",children:P?(0,s.jsx)(g.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("span",{className:"text-sm font-semibold ".concat(((null==(l=L[e.id_cantiere])?void 0:l.percentuale_avanzamento)||0)>=90?"text-green-700":((null==(d=L[e.id_cantiere])?void 0:d.percentuale_avanzamento)||0)>=75?"text-blue-700":((null==(m=L[e.id_cantiere])?void 0:m.percentuale_avanzamento)||0)>=50?"text-yellow-700":((null==(u=L[e.id_cantiere])?void 0:u.percentuale_avanzamento)||0)>=25?"text-orange-700":"text-red-700"),children:[((null==(x=L[e.id_cantiere])?void 0:x.percentuale_avanzamento)||0).toFixed(1),"%"]})]})}),(0,s.jsx)(c.nA,{className:"text-center py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>en(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,s.jsxs)(o.$,{size:"sm",onClick:()=>er(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out",title:"Accedi al cantiere",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere)})})]})})})]})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>x,L3:()=>g,c7:()=>u,lG:()=>o,rr:()=>h,zM:()=>l});var s=t(95155);t(12115);var r=t(15452),n=t(54416),i=t(59434);function o(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...a})}function l(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function m(e){let{className:a,children:t,showCloseButton:o=!0,...l}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...l,children:[t,o&&(0,s.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function x(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...t})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var s=t(52596),r=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var s=t(95155);t(12115);var r=t(59434);function n(e){let{className:a,type:t,...n}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...n})}},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=t(95155);t(12115);var r=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>i});var s=t(95155);t(12115);var r=t(40968),n=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},85127:(e,a,t)=>{"use strict";t.d(a,{A0:()=>i,BF:()=>o,Hj:()=>l,XI:()=>n,nA:()=>d,nd:()=>c});var s=t(95155);t(12115);var r=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",a),...t})})}function i(e){let{className:a,...t}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}},92524:(e,a,t)=>{Promise.resolve().then(t.bind(t,42712))}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3455,5585,9872,283,8441,1684,7358],()=>a(92524)),_N_E=e.O()}]);