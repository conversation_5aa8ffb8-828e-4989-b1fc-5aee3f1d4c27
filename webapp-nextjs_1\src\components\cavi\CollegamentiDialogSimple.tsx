'use client'

import { useState, useEffect } from 'react'
import * as VisuallyHidden from '@radix-ui/react-visually-hidden'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertTriangle, CheckCircle, Zap, Package, User } from 'lucide-react'
import { Cavo } from '@/types'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

// Costanti per i flag di collegamento (come nella webapp originale)
const COLLEGAMENTO_PARTENZA = 1  // 0001 in binario
const COLLEGAMENTO_ARRIVO = 2    // 0010 in binario

// Funzioni di utilità per la logica bitwise (specifiche interfaccia unificata)
const formatStatoCollegamenti = (collegamenti: number): { icon: string, text: string, color: string } => {
  collegamenti = collegamenti || 0
  if (collegamenti === 0) return { icon: "⚪", text: "Non collegato", color: "text-gray-600" }
  if (collegamenti === 1) return { icon: "🟡", text: "Parzialmente collegato", color: "text-yellow-600" }
  if (collegamenti === 2) return { icon: "🟡", text: "Parzialmente collegato", color: "text-yellow-600" }
  if (collegamenti === 3) return { icon: "🟢", text: "Completamente collegato", color: "text-green-600" }
  return { icon: "❓", text: `Sconosciuto (${collegamenti})`, color: "text-gray-600" }
}

const formatStatoLato = (collegamenti: number, lato: 'partenza' | 'arrivo'): { icon: string, text: string } => {
  collegamenti = collegamenti || 0
  if (lato === 'partenza') {
    return (collegamenti & 1) ? { icon: "🟢", text: "Collegato" } : { icon: "⚪", text: "Non collegato" }
  } else {
    return (collegamenti & 2) ? { icon: "🟢", text: "Collegato" } : { icon: "⚪", text: "Non collegato" }
  }
}

// Lista responsabili (come nelle altre modali del sistema)
const responsabiliOptions = [
  { value: 'cantiere', label: 'Cantiere' },
  { value: 'elettricista', label: 'Elettricista' },
  { value: 'tecnico', label: 'Tecnico' },
  { value: 'supervisore', label: 'Supervisore' }
]

interface CollegamentiDialogSimpleProps {
  open: boolean
  onClose: () => void
  cavo: Cavo
  cantiere?: any // Cantiere opzionale come prop
  onSuccess?: () => void
}



interface ConfirmDialog {
  open: boolean
  type: 'partenza' | 'arrivo' | 'entrambi' | null
  title: string
  description: string
}

export default function CollegamentiDialogSimple({
  open,
  onClose,
  cavo,
  cantiere: cantiereProp,
  onSuccess
}: CollegamentiDialogSimpleProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [selectedResponsabile, setSelectedResponsabile] = useState('')

  const { cantiere: cantiereAuth } = useAuth()
  const { toast } = useToast()

  // Usa il cantiere passato come prop o quello dal contesto di autenticazione
  const cantiere = cantiereProp || cantiereAuth

  // Reset errori quando si apre il dialog
  useEffect(() => {
    if (open) {
      setError('')
      setSelectedResponsabile('')
    }
  }, [open])

  // Determina lo stato attuale dei collegamenti
  const collegamenti = cavo?.collegamenti || 0
  const latoPartenzaCollegato = (collegamenti & COLLEGAMENTO_PARTENZA) === COLLEGAMENTO_PARTENZA
  const latoArrivoCollegato = (collegamenti & COLLEGAMENTO_ARRIVO) === COLLEGAMENTO_ARRIVO

  // Determina il tipo di sezione e azioni disponibili (approccio contestuale delle specifiche)
  const getSectionInfo = () => {
    if (collegamenti === 0) {
      return {
        title: "Azioni di Collegamento",
        type: "collegamento",
        actions: [
          { id: 'partenza', label: 'Collega Partenza', icon: '⚡', variant: 'default' as const },
          { id: 'arrivo', label: 'Collega Arrivo', icon: '⚡', variant: 'default' as const },
          { id: 'entrambi', label: 'Collega Entrambi i Lati', icon: '⚡', variant: 'default' as const }
        ]
      }
    } else if (collegamenti === 3) {
      return {
        title: "Azioni di Scollegamento",
        type: "scollegamento",
        actions: [
          { id: 'scollega-partenza', label: 'Scollega Partenza', icon: '⛔', variant: 'destructive' as const },
          { id: 'scollega-arrivo', label: 'Scollega Arrivo', icon: '⛔', variant: 'destructive' as const },
          { id: 'scollega-entrambi', label: 'Scollega Entrambi i Lati', icon: '⛔', variant: 'destructive' as const }
        ]
      }
    } else {
      // Parzialmente collegato - azioni miste
      const actions = []
      if (latoPartenzaCollegato) {
        actions.push({ id: 'scollega-partenza', label: 'Scollega Partenza', icon: '⛔', variant: 'destructive' as const })
      } else {
        actions.push({ id: 'partenza', label: 'Collega Partenza', icon: '⚡', variant: 'default' as const })
      }

      if (latoArrivoCollegato) {
        actions.push({ id: 'scollega-arrivo', label: 'Scollega Arrivo', icon: '⛔', variant: 'destructive' as const })
      } else {
        actions.push({ id: 'arrivo', label: 'Collega Arrivo', icon: '⚡', variant: 'default' as const })
      }

      return {
        title: "Azioni Disponibili",
        type: "misto",
        actions
      }
    }
  }

  const sectionInfo = getSectionInfo()

  // Gestisce l'esecuzione di un'azione specifica (approccio contestuale)
  const handleAction = async (actionId: string) => {
    if (!selectedResponsabile && !actionId.startsWith('scollega')) {
      setError('Seleziona un responsabile per abilitare le azioni di collegamento')
      return
    }

    if (!cavo || !cantiere) {
      console.error('🔌 CollegamentiDialogSimple: Mancano dati necessari', { cavo, cantiere })
      setError('Errore: dati del cavo o cantiere mancanti. Ricarica la pagina.')
      return
    }

    // Verifica autenticazione
    const token = localStorage.getItem('token')
    if (!token) {
      setError('Utente non autenticato. Effettua il login.')
      toast({
        title: "Errore autenticazione",
        description: "Utente non autenticato. Effettua il login.",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    setError('')

    try {

      // Gestione delle azioni basata sull'ID (logica semplificata e diretta)
      switch (actionId) {
        case 'partenza':
          await caviApi.collegaCavo(cantiere.id_cantiere, cavo.id_cavo, 'partenza', selectedResponsabile)
          toast({
            title: 'Successo',
            description: `Lato partenza collegato con successo!`,
          })
          break

        case 'arrivo':
          await caviApi.collegaCavo(cantiere.id_cantiere, cavo.id_cavo, 'arrivo', selectedResponsabile)
          toast({
            title: 'Successo',
            description: `Lato arrivo collegato con successo!`,
          })
          break

        case 'entrambi':
          // Collega entrambi i lati (solo quelli non ancora collegati)
          let operazioniEffettuate = []

          if (!latoPartenzaCollegato) {
            await caviApi.collegaCavo(cantiere.id_cantiere, cavo.id_cavo, 'partenza', selectedResponsabile)
            operazioniEffettuate.push('Lato partenza')
          }

          if (!latoArrivoCollegato) {
            await caviApi.collegaCavo(cantiere.id_cantiere, cavo.id_cavo, 'arrivo', selectedResponsabile)
            operazioniEffettuate.push('Lato arrivo')
          }

          if (operazioniEffettuate.length > 0) {
            toast({
              title: 'Successo',
              description: `${operazioniEffettuate.join(' e ')} collegati con successo!`,
            })
          }
          break

        case 'scollega-partenza':
          await caviApi.scollegaCavo(cantiere.id_cantiere, cavo.id_cavo, 'partenza')
          toast({
            title: 'Successo',
            description: `Lato partenza scollegato con successo!`,
          })
          break

        case 'scollega-arrivo':
          await caviApi.scollegaCavo(cantiere.id_cantiere, cavo.id_cavo, 'arrivo')
          toast({
            title: 'Successo',
            description: `Lato arrivo scollegato con successo!`,
          })
          break

        case 'scollega-entrambi':
          await caviApi.scollegaCavo(cantiere.id_cantiere, cavo.id_cavo)
          toast({
            title: 'Successo',
            description: `Entrambi i lati scollegati con successo!`,
          })
          break

        default:
          throw new Error(`Azione non riconosciuta: ${actionId}`)
      }

      onSuccess?.()
      onClose()
    } catch (error: any) {
      console.error('Errore collegamenti:', error)

      const errorMessage = error?.response?.data?.detail || error?.message || 'Errore durante l\'operazione'
      setError(errorMessage)

      toast({
        title: "Errore",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }





  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-600" />
              Gestione Collegamenti - Cavo: {cavo.id_cavo}
            </DialogTitle>
            <DialogDescription>
              Gestisci i collegamenti del cavo {cavo.id_cavo}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">

            {/* Sezione Informazioni Cavo */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 font-medium text-sm text-gray-700">
                <Package className="h-4 w-4" />
                Informazioni Cavo
              </div>
              <div className="grid grid-cols-1 gap-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Partenza:</span>
                  <span className="font-medium text-gray-900">{cavo.ubicazione_partenza || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Arrivo:</span>
                  <span className="font-medium text-gray-900">{cavo.ubicazione_arrivo || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Sezione Stato Attuale Collegamenti */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="font-medium text-sm text-gray-700">Stato Attuale Collegamenti</div>

              {/* Stato Generale */}
              <div className="flex items-center gap-2 text-sm">
                <span className="text-2xl">{formatStatoCollegamenti(collegamenti).icon}</span>
                <span className={`font-medium ${formatStatoCollegamenti(collegamenti).color}`}>
                  {formatStatoCollegamenti(collegamenti).text}
                </span>
              </div>

              {/* Dettagli per lato */}
              <div className="space-y-2 text-sm border-t pt-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{formatStatoLato(collegamenti, 'partenza').icon}</span>
                    <span className="text-gray-600">Lato Partenza:</span>
                    <span className="font-medium">{formatStatoLato(collegamenti, 'partenza').text}</span>
                  </div>
                  {latoPartenzaCollegato && cavo.responsabile_partenza && (
                    <span className="text-xs text-gray-500">(Resp: {cavo.responsabile_partenza})</span>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{formatStatoLato(collegamenti, 'arrivo').icon}</span>
                    <span className="text-gray-600">Lato Arrivo:</span>
                    <span className="font-medium">{formatStatoLato(collegamenti, 'arrivo').text}</span>
                  </div>
                  {latoArrivoCollegato && cavo.responsabile_arrivo && (
                    <span className="text-xs text-gray-500">(Resp: {cavo.responsabile_arrivo})</span>
                  )}
                </div>
              </div>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Sezione Gestisci Collegamenti - Interfaccia Unificata */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              <div className="font-medium text-sm text-gray-700">{sectionInfo.title}</div>

              {/* Selezione Responsabile (solo per azioni di collegamento) */}
              {sectionInfo.type !== "scollegamento" && (
                <div className="space-y-2">
                  <Label htmlFor="responsabile" className="flex items-center gap-2 text-sm">
                    <User className="h-4 w-4" />
                    Seleziona responsabile...
                  </Label>
                  <Select value={selectedResponsabile} onValueChange={setSelectedResponsabile}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Seleziona responsabile..." />
                    </SelectTrigger>
                    <SelectContent>
                      {responsabiliOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {!selectedResponsabile && sectionInfo.type !== "scollegamento" && (
                    <div className="flex items-center gap-2 text-xs text-amber-600">
                      <AlertTriangle className="h-3 w-3" />
                      Seleziona un responsabile per abilitare le azioni
                    </div>
                  )}
                </div>
              )}

              {/* Azioni Disponibili (Bottoni Contestuali) */}
              <div className="space-y-2">
                <Label className="text-sm text-gray-600">Azioni Disponibili:</Label>
                <div className="grid grid-cols-1 gap-2">
                  {sectionInfo.actions.map((action) => (
                    <Button
                      key={action.id}
                      onClick={() => handleAction(action.id)}
                      disabled={loading || (!selectedResponsabile && !action.id.startsWith('scollega'))}
                      variant={action.variant}
                      className="w-full justify-start"
                    >
                      {loading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <span className="mr-2">{action.icon}</span>
                      )}
                      {action.label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annulla
            </Button>
            <Button
              onClick={onClose}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Chiudi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
