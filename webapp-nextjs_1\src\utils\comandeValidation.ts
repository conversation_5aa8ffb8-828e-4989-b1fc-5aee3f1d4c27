/**
 * Utilità per validazioni del sistema COMANDE
 */

export interface CavoValidation {
  id_cavo: string
  isValid: boolean
  errors: string[]
  warnings: string[]
  info: string[]
}

export interface ComandaValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  info: string[]
  caviValidi: any[]
  caviProblematici: Array<{
    cavo: any
    issues: string[]
  }>
}

export const COMMAND_TYPES = {
  POSA: 'POSA',
  COLLEGAMENTO_PARTENZA: 'COLLEGAMENTO_PARTENZA', 
  COLLEGAMENTO_ARRIVO: 'COLLEGAMENTO_ARRIVO',
  CERTIFICAZIONE: 'CERTIFICAZIONE'
} as const

export const CABLE_STATES = {
  NON_INSTALLATO: 'Non Installato',
  DA_INSTALLARE: 'Da installare', 
  IN_CORSO: 'In corso',
  INSTALLATO: 'Installato'
} as const

export const VALIDATION_TYPES = {
  CABLE_STATE: 'CABLE_STATE',
  EXISTING_COMMAND: 'EXISTING_COMMAND',
  PREREQUISITE: 'PREREQUISITE',
  RESPONSIBLE_CONFLICT: 'RESPONSIBLE_CONFLICT'
} as const

export const SEVERITY_LEVELS = {
  ERROR: 'ERROR',
  WARNING: 'WARNING',
  INFO: 'INFO'
} as const

/**
 * Valida un singolo cavo per l'assegnazione a una comanda
 */
export function validateSingleCavo(
  cavo: any,
  tipoComanda: string,
  responsabile: string
): CavoValidation {
  const result: CavoValidation = {
    id_cavo: cavo.id_cavo,
    isValid: true,
    errors: [],
    warnings: [],
    info: []
  }

  // 1. Controlli stato cavo
  const stateChecks = checkCableState(cavo, tipoComanda)
  result.errors.push(...stateChecks.errors)
  result.warnings.push(...stateChecks.warnings)
  result.info.push(...stateChecks.info)

  // 2. Controlli comande esistenti
  const existingCommandChecks = checkExistingCommands(cavo, tipoComanda)
  result.errors.push(...existingCommandChecks.errors)
  result.warnings.push(...existingCommandChecks.warnings)
  result.info.push(...existingCommandChecks.info)

  // 3. Controlli prerequisiti
  const prerequisiteChecks = checkPrerequisites(cavo, tipoComanda)
  result.warnings.push(...prerequisiteChecks.warnings)
  result.info.push(...prerequisiteChecks.info)

  // 4. Controlli responsabile
  const responsibleChecks = checkResponsibleConflicts(cavo, tipoComanda, responsabile)
  result.warnings.push(...responsibleChecks.warnings)
  result.info.push(...responsibleChecks.info)

  result.isValid = result.errors.length === 0
  return result
}

/**
 * Valida una lista di cavi per l'assegnazione a una comanda
 */
export function validateCaviForComanda(
  cavi: any[],
  tipoComanda: string,
  responsabile: string
): ComandaValidationResult {
  const result: ComandaValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    info: [],
    caviValidi: [],
    caviProblematici: []
  }

  if (!cavi || cavi.length === 0) {
    result.errors.push('Nessun cavo selezionato per la comanda')
    result.isValid = false
    return result
  }

  if (!responsabile || responsabile.trim() === '') {
    result.errors.push('Responsabile non specificato')
    result.isValid = false
    return result
  }

  cavi.forEach(cavo => {
    const cavoValidation = validateSingleCavo(cavo, tipoComanda, responsabile)
    
    // Aggrega i risultati
    result.errors.push(...cavoValidation.errors)
    result.warnings.push(...cavoValidation.warnings)
    result.info.push(...cavoValidation.info)
    
    if (cavoValidation.isValid) {
      result.caviValidi.push(cavo)
    } else {
      result.caviProblematici.push({
        cavo: cavo,
        issues: cavoValidation.errors
      })
    }
  })

  // La validazione generale è valida se non ci sono errori bloccanti
  result.isValid = result.errors.length === 0
  
  return result
}

/**
 * Controlla lo stato del cavo per determinare se può essere assegnato alla comanda
 */
function checkCableState(cavo: any, tipoComanda: string) {
  const result = { errors: [] as string[], warnings: [] as string[], info: [] as string[] }
  
  const isInstalled = cavo.stato_installazione === CABLE_STATES.INSTALLATO
  const hasMeters = cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0
  const isConnected = cavo.collegamenti && parseInt(cavo.collegamenti) > 0
  const isCertified = cavo.stato_certificazione === 'CERTIFICATO'

  switch (tipoComanda) {
    case COMMAND_TYPES.POSA:
      if (isInstalled) {
        result.errors.push(`Cavo ${cavo.id_cavo} è già installato e non può essere assegnato a comanda POSA`)
      }
      if (hasMeters) {
        result.warnings.push(`Cavo ${cavo.id_cavo} ha già metratura reale registrata`)
      }
      break

    case COMMAND_TYPES.COLLEGAMENTO_PARTENZA:
    case COMMAND_TYPES.COLLEGAMENTO_ARRIVO:
      if (!isInstalled && !hasMeters) {
        result.warnings.push(`Cavo ${cavo.id_cavo} non risulta installato. Verificare prerequisiti.`)
      }
      if (isConnected) {
        result.warnings.push(`Cavo ${cavo.id_cavo} risulta già collegato`)
      }
      break

    case COMMAND_TYPES.CERTIFICAZIONE:
      if (!isInstalled) {
        result.errors.push(`Cavo ${cavo.id_cavo} deve essere installato per la certificazione`)
      }
      if (!isConnected) {
        result.warnings.push(`Cavo ${cavo.id_cavo} non risulta collegato. Verificare prerequisiti.`)
      }
      if (isCertified) {
        result.warnings.push(`Cavo ${cavo.id_cavo} è già certificato`)
      }
      break
  }

  return result
}

/**
 * Controlla se il cavo ha già comande assegnate per il tipo specificato
 */
function checkExistingCommands(cavo: any, tipoComanda: string) {
  const result = { errors: [] as string[], warnings: [] as string[], info: [] as string[] }

  switch (tipoComanda) {
    case COMMAND_TYPES.POSA:
      if (cavo.comanda_posa) {
        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda POSA assegnata: ${cavo.comanda_posa}`)
      }
      break

    case COMMAND_TYPES.COLLEGAMENTO_PARTENZA:
      if (cavo.comanda_partenza) {
        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda COLLEGAMENTO_PARTENZA assegnata: ${cavo.comanda_partenza}`)
      }
      break

    case COMMAND_TYPES.COLLEGAMENTO_ARRIVO:
      if (cavo.comanda_arrivo) {
        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda COLLEGAMENTO_ARRIVO assegnata: ${cavo.comanda_arrivo}`)
      }
      break

    case COMMAND_TYPES.CERTIFICAZIONE:
      if (cavo.comanda_certificazione) {
        result.errors.push(`Cavo ${cavo.id_cavo} ha già comanda CERTIFICAZIONE assegnata: ${cavo.comanda_certificazione}`)
      }
      break
  }

  return result
}

/**
 * Controlla i prerequisiti per il tipo di comanda
 */
function checkPrerequisites(cavo: any, tipoComanda: string) {
  const result = { warnings: [] as string[], info: [] as string[] }

  switch (tipoComanda) {
    case COMMAND_TYPES.COLLEGAMENTO_PARTENZA:
    case COMMAND_TYPES.COLLEGAMENTO_ARRIVO:
      // Prerequisito: deve esistere comanda posa completata o cavo installato
      if (!cavo.comanda_posa && (!cavo.metratura_reale || parseFloat(cavo.metratura_reale) <= 0)) {
        result.warnings.push(`Cavo ${cavo.id_cavo} non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti.`)
      }
      break

    case COMMAND_TYPES.CERTIFICAZIONE:
      // Prerequisito: deve avere collegamenti completati
      if (!cavo.comanda_partenza && !cavo.comanda_arrivo) {
        result.warnings.push(`Cavo ${cavo.id_cavo} non ha comande di collegamento assegnate. Verificare prerequisiti.`)
      }
      break
  }

  return result
}

/**
 * Controlla conflitti di responsabili
 */
function checkResponsibleConflicts(cavo: any, tipoComanda: string, nuovoResponsabile: string) {
  const result = { warnings: [] as string[], info: [] as string[] }

  // Raccoglie tutti i responsabili esistenti per il cavo
  const responsabili: Record<string, string> = {
    posa: cavo.responsabile_posa || '',
    partenza: cavo.responsabile_partenza || '',
    arrivo: cavo.responsabile_arrivo || '',
    certificazione: cavo.responsabile_certificazione || ''
  }

  // Controlla se ci sono responsabili diversi per lo stesso cavo
  const responsabiliAttivi = Object.values(responsabili).filter(r => r && r.trim() !== '')
  const responsabiliUnici = [...new Set(responsabiliAttivi)]

  if (responsabiliUnici.length > 1 && !responsabiliUnici.includes(nuovoResponsabile)) {
    result.warnings.push(`Cavo ${cavo.id_cavo} ha già responsabili diversi (${responsabiliUnici.join(', ')}). Nuovo responsabile: ${nuovoResponsabile}`)
  }

  return result
}

/**
 * Valida i dati di un responsabile
 */
export function validateResponsabile(responsabileData: any) {
  const errors: string[] = []

  if (!responsabileData.nome_responsabile || !responsabileData.nome_responsabile.trim()) {
    errors.push('Il nome del responsabile è obbligatorio')
  }

  if (!responsabileData.mail && !responsabileData.numero_telefono) {
    errors.push('Almeno uno tra email e telefono deve essere specificato')
  }

  if (responsabileData.mail && !isValidEmail(responsabileData.mail)) {
    errors.push('Formato email non valido')
  }

  if (responsabileData.numero_telefono && !isValidPhone(responsabileData.numero_telefono)) {
    errors.push('Formato telefono non valido')
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  }
}

/**
 * Valida formato email
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Valida formato telefono
 */
function isValidPhone(phone: string): boolean {
  // Accetta numeri con o senza prefisso, spazi, trattini, parentesi
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,15}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

/**
 * Formatta i risultati di validazione per la visualizzazione
 */
export function formatValidationResults(validation: ComandaValidationResult) {
  const messages: string[] = []

  if (validation.errors.length > 0) {
    messages.push(`❌ Errori (${validation.errors.length}):`)
    validation.errors.forEach(error => messages.push(`  • ${error}`))
  }

  if (validation.warnings.length > 0) {
    messages.push(`⚠️ Avvisi (${validation.warnings.length}):`)
    validation.warnings.forEach(warning => messages.push(`  • ${warning}`))
  }

  if (validation.info.length > 0) {
    messages.push(`ℹ️ Informazioni (${validation.info.length}):`)
    validation.info.forEach(info => messages.push(`  • ${info}`))
  }

  if (validation.caviValidi.length > 0) {
    messages.push(`✅ Cavi validi: ${validation.caviValidi.length}`)
  }

  if (validation.caviProblematici.length > 0) {
    messages.push(`❌ Cavi problematici: ${validation.caviProblematici.length}`)
  }

  return messages.join('\n')
}
