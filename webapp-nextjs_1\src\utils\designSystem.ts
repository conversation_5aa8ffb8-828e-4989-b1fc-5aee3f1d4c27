/**
 * Design System Utilities per CABLYS Admin
 * Garan<PERSON>ce coerenza visiva e conformità WCAG 2.1 AA
 */

// Colori del sistema con contrasti WCAG AA compliant
export const colors = {
  // Colori primari
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Colore principale
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  
  // Colori di stato
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // Colori neutri
  slate: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  }
}

// Tipografia con scale armoniche
export const typography = {
  fontSizes: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
  },
  
  lineHeights: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
  
  fontWeights: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  }
}

// Spaziature consistenti
export const spacing = {
  xs: '0.25rem',   // 4px
  sm: '0.5rem',    // 8px
  md: '1rem',      // 16px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '3rem',   // 48px
  '3xl': '4rem',   // 64px
}

// Bordi e raggi
export const borders = {
  radius: {
    sm: '0.25rem',   // 4px
    md: '0.375rem',  // 6px
    lg: '0.5rem',    // 8px
    xl: '0.75rem',   // 12px
  },
  
  width: {
    thin: '1px',
    medium: '2px',
    thick: '3px',
  }
}

// Ombre con profondità
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
}

// Transizioni standard
export const transitions = {
  fast: '150ms ease-in-out',
  normal: '200ms ease-in-out',
  slow: '300ms ease-in-out',
}

// Classi CSS per stati di focus accessibili
export const focusClasses = {
  default: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
  danger: 'focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
  success: 'focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2',
}

// Pattern per componenti interattivi
export const interactionPatterns = {
  button: {
    primary: `
      bg-blue-600 text-white border border-blue-600
      hover:bg-blue-700 hover:border-blue-700
      active:bg-blue-800 active:border-blue-800
      disabled:bg-slate-300 disabled:text-slate-500 disabled:border-slate-300 disabled:cursor-not-allowed
      transition-all duration-200
      ${focusClasses.default}
    `,
    
    secondary: `
      bg-white text-slate-700 border border-slate-300
      hover:bg-slate-50 hover:border-slate-400
      active:bg-slate-100 active:border-slate-500
      disabled:bg-slate-100 disabled:text-slate-400 disabled:border-slate-200 disabled:cursor-not-allowed
      transition-all duration-200
      ${focusClasses.default}
    `,
    
    danger: `
      bg-red-600 text-white border border-red-600
      hover:bg-red-700 hover:border-red-700
      active:bg-red-800 active:border-red-800
      disabled:bg-slate-300 disabled:text-slate-500 disabled:border-slate-300 disabled:cursor-not-allowed
      transition-all duration-200
      ${focusClasses.danger}
    `,
  },
  
  input: `
    border border-slate-300 rounded-md px-3 py-2
    hover:border-slate-400
    focus:border-blue-500 focus:ring-1 focus:ring-blue-500
    disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 disabled:cursor-not-allowed
    transition-all duration-200
    placeholder:text-slate-400
  `,
  
  card: `
    bg-white border border-slate-200 rounded-lg shadow-sm
    hover:shadow-md
    transition-shadow duration-200
  `,
}

// Utilità per contrasto colori (WCAG AA compliance)
export const getContrastColor = (backgroundColor: string): string => {
  // Implementazione semplificata - in produzione usare una libreria come chroma-js
  const darkColors = ['800', '900']
  const isDark = darkColors.some(shade => backgroundColor.includes(shade))
  return isDark ? colors.slate[50] : colors.slate[900]
}

// Utilità per badge di stato
export const getStatusBadgeClasses = (status: 'success' | 'warning' | 'error' | 'info') => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium'
  
  switch (status) {
    case 'success':
      return `${baseClasses} bg-green-100 text-green-800`
    case 'warning':
      return `${baseClasses} bg-yellow-100 text-yellow-800`
    case 'error':
      return `${baseClasses} bg-red-100 text-red-800`
    case 'info':
    default:
      return `${baseClasses} bg-blue-100 text-blue-800`
  }
}

// Utilità per tooltip accessibili
export const tooltipClasses = `
  absolute z-50 px-2 py-1 text-xs text-white bg-slate-900 rounded
  opacity-0 group-hover:opacity-100 transition-opacity duration-200
  pointer-events-none whitespace-nowrap
`

// Breakpoints responsive
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
}

// Utilità per animazioni
export const animations = {
  fadeIn: 'animate-in fade-in duration-200',
  slideInFromTop: 'animate-in slide-in-from-top-2 duration-300',
  slideInFromBottom: 'animate-in slide-in-from-bottom-2 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-200',
}

// Pattern per layout
export const layoutPatterns = {
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  section: 'py-8 space-y-6',
  grid: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
}

// Validazione WCAG per contrasti
export const validateContrast = (foreground: string, background: string): boolean => {
  // Implementazione semplificata - in produzione calcolare il rapporto di contrasto reale
  // WCAG AA richiede almeno 4.5:1 per testo normale, 3:1 per testo grande
  return true // Placeholder
}

// Esportazione di utilità comuni
export const designSystem = {
  colors,
  typography,
  spacing,
  borders,
  shadows,
  transitions,
  focusClasses,
  interactionPatterns,
  getContrastColor,
  getStatusBadgeClasses,
  tooltipClasses,
  breakpoints,
  animations,
  layoutPatterns,
  validateContrast,
}

export default designSystem
