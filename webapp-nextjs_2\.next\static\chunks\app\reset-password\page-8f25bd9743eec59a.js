(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{4366:(e,s,r)=>{Promise.resolve().then(r.bind(r,67712))},24944:(e,s,r)=>{"use strict";r.d(s,{k:()=>i});var t=r(95155);r(12115);var a=r(55863),n=r(59434);function i(e){let{className:s,value:r,...i}=e;return(0,t.jsx)(a.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...i,children:(0,t.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})}},30285:(e,s,r)=>{"use strict";r.d(s,{$:()=>l});var t=r(95155);r(12115);var a=r(99708),n=r(74466),i=r(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:r,size:n,asChild:l=!1,...d}=e,c=l?a.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:n,className:s})),...d})}},32919:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35169:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,s,r)=>{"use strict";var t=r(18999);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},40968:(e,s,r)=>{"use strict";r.d(s,{b:()=>o});var t=r(12115),a=r(63655),n=r(95155),i=t.forwardRef((e,s)=>(0,n.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var o=i},46081:(e,s,r)=>{"use strict";r.d(s,{A:()=>i,q:()=>n});var t=r(12115),a=r(95155);function n(e,s){let r=t.createContext(s),n=e=>{let{children:s,...n}=e,i=t.useMemo(()=>n,Object.values(n));return(0,a.jsx)(r.Provider,{value:i,children:s})};return n.displayName=e+"Provider",[n,function(a){let n=t.useContext(r);if(n)return n;if(void 0!==s)return s;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function i(e,s=[]){let r=[],n=()=>{let s=r.map(e=>t.createContext(e));return function(r){let a=r?.[e]||s;return t.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return n.scopeName=e,[function(s,n){let i=t.createContext(n),o=r.length;r=[...r,n];let l=s=>{let{scope:r,children:n,...l}=s,d=r?.[e]?.[o]||i,c=t.useMemo(()=>l,Object.values(l));return(0,a.jsx)(d.Provider,{value:c,children:n})};return l.displayName=s+"Provider",[l,function(r,a){let l=a?.[e]?.[o]||i,d=t.useContext(l);if(d)return d;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((s,{useScope:r,scopeName:t})=>{let a=r(e)[`__scope${t}`];return{...s,...a}},{});return t.useMemo(()=>({[`__scope${s.scopeName}`]:a}),[a])}};return r.scopeName=s.scopeName,r}(n,...s)]}},54861:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55365:(e,s,r)=>{"use strict";r.d(s,{Fc:()=>l,TN:()=>d});var t=r(95155),a=r(12115),n=r(74466),i=r(59434);let o=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=a.forwardRef((e,s)=>{let{className:r,variant:a,...n}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(o({variant:a}),r),...n})});l.displayName="Alert",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",r),...a})}).displayName="AlertTitle";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",r),...a})});d.displayName="AlertDescription"},55863:(e,s,r)=>{"use strict";r.d(s,{C1:()=>j,bL:()=>b});var t=r(12115),a=r(46081),n=r(63655),i=r(95155),o="Progress",[l,d]=(0,a.A)(o),[c,u]=l(o),m=t.forwardRef((e,s)=>{var r,t,a,o;let{__scopeProgress:l,value:d=null,max:u,getValueLabel:m=f,...x}=e;(u||0===u)&&!g(u)&&console.error((r="".concat(u),t="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(t,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=g(u)?u:100;null===d||w(d,p)||console.error((a="".concat(d),o="Progress","Invalid prop `value` of value `".concat(a,"` supplied to `").concat(o,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=w(d,p)?d:null,j=v(b)?m(b,p):void 0;return(0,i.jsx)(c,{scope:l,value:b,max:p,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":v(b)?b:void 0,"aria-valuetext":j,role:"progressbar","data-state":h(b,p),"data-value":null!=b?b:void 0,"data-max":p,...x,ref:s})})});m.displayName=o;var x="ProgressIndicator",p=t.forwardRef((e,s)=>{var r;let{__scopeProgress:t,...a}=e,o=u(x,t);return(0,i.jsx)(n.sG.div,{"data-state":h(o.value,o.max),"data-value":null!=(r=o.value)?r:void 0,"data-max":o.max,...a,ref:s})});function f(e,s){return"".concat(Math.round(e/s*100),"%")}function h(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function v(e){return"number"==typeof e}function g(e){return v(e)&&!isNaN(e)&&e>0}function w(e,s){return v(e)&&!isNaN(e)&&e<=s&&e>=0}p.displayName=x;var b=m,j=p},59434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(52596),a=r(39688);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}},62523:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(95155);r(12115);var a=r(59434);function n(e){let{className:s,type:r,...n}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},63655:(e,s,r)=>{"use strict";r.d(s,{hO:()=>l,sG:()=>o});var t=r(12115),a=r(47650),n=r(99708),i=r(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,n.TL)(`Primitive.${s}`),a=t.forwardRef((e,t)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?r:s,{...n,ref:t})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function l(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}},66695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var t=r(95155);r(12115);var a=r(59434);function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function d(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}},67712:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>P});var t=r(95155),a=r(12115),n=r(6874),i=r.n(n),o=r(3493),l=r(35169),d=r(30285),c=r(35695),u=r(62523),m=r(85057),x=r(66695),p=r(55365),f=r(24944),h=r(54861),v=r(32919),g=r(78749),w=r(92657),b=r(40646),j=r(1243),y=r(75525),N=r(59434);function k(){var e;let s=(0,c.useRouter)(),r=(0,c.useSearchParams)().get("token"),[n,i]=(0,a.useState)({newPassword:"",confirmPassword:""}),[o,l]=(0,a.useState)({new:!1,confirm:!1}),[k,P]=(0,a.useState)({score:0,feedback:[],isValid:!1}),[A,z]=(0,a.useState)(!1),[C,S]=(0,a.useState)(null),[_,R]=(0,a.useState)(null);(0,a.useEffect)(()=>{if(!r){S({type:"error",text:"Token di reset mancante o non valido"}),R(!1);return}R(!0)},[r]);let T=async e=>{if(!e)return void P({score:0,feedback:[],isValid:!1});try{let s=await fetch("/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:e})});if(s.ok){let e=await s.json();P({score:e.strength_score,feedback:e.suggestions||[],isValid:e.is_valid})}}catch(e){}},E=(e,s)=>{i(r=>({...r,[e]:s})),"newPassword"===e&&T(s)},$=async e=>{e.preventDefault(),z(!0),S(null);try{if(!n.newPassword||!n.confirmPassword)throw Error("Tutti i campi sono obbligatori");if(n.newPassword!==n.confirmPassword)throw Error("Le password non corrispondono");if(!k.isValid)throw Error("La password non rispetta i requisiti di sicurezza");if(!r)throw Error("Token di reset non valido");let e=await fetch("/api/password/confirm-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:r,new_password:n.newPassword,confirm_password:n.confirmPassword})}),t=await e.json();if(e.ok&&t.success)S({type:"success",text:t.message}),setTimeout(()=>{s.push("/login")},3e3);else throw Error(t.detail||t.message||"Errore durante il reset della password")}catch(e){S({type:"error",text:e instanceof Error?e.message:"Errore durante il reset della password"})}finally{z(!1)}},M=e=>{l(s=>({...s,[e]:!s[e]}))};return!1===_?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)(x.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(x.aR,{className:"text-center",children:[(0,t.jsxs)(x.ZB,{className:"text-2xl flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(h.A,{className:"h-6 w-6"}),"Token Non Valido"]}),(0,t.jsx)(x.BT,{children:"Il link di reset password non \xe8 valido o \xe8 scaduto"})]}),(0,t.jsxs)(x.Wu,{className:"text-center",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Il token potrebbe essere scaduto o gi\xe0 utilizzato. Richiedi un nuovo link di reset password."}),(0,t.jsx)(d.$,{onClick:()=>s.push("/forgot-password"),className:"bg-mariner-600 hover:bg-mariner-700",children:"Richiedi Nuovo Reset"})]})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)(x.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(x.aR,{className:"space-y-1",children:[(0,t.jsxs)(x.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-mariner-600"}),"Reimposta Password"]}),(0,t.jsx)(x.BT,{children:"Inserisci la tua nuova password sicura"})]}),(0,t.jsxs)(x.Wu,{children:[(0,t.jsxs)("form",{onSubmit:$,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"newPassword",children:"Nuova Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.p,{id:"newPassword",type:o.new?"text":"password",value:n.newPassword,onChange:e=>E("newPassword",e.target.value),className:"pr-10",required:!0}),(0,t.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>M("new"),children:o.new?(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(w.A,{className:"h-4 w-4 text-gray-400"})})]}),n.newPassword&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{children:"Forza password:"}),(0,t.jsx)("span",{className:(0,N.cn)("font-medium",k.score<2?"text-red-600":k.score<4?"text-yellow-600":"text-green-600"),children:(e=k.score)<2?"Debole":e<4?"Media":"Forte"})]}),(0,t.jsx)(f.k,{value:k.score/5*100,className:"h-2"}),k.feedback.length>0&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Suggerimenti:"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1",children:k.feedback.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"confirmPassword",children:"Conferma Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.p,{id:"confirmPassword",type:o.confirm?"text":"password",value:n.confirmPassword,onChange:e=>E("confirmPassword",e.target.value),className:"pr-10",required:!0}),(0,t.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>M("confirm"),children:o.confirm?(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(w.A,{className:"h-4 w-4 text-gray-400"})})]}),n.confirmPassword&&(0,t.jsx)("div",{className:"flex items-center gap-2 text-sm",children:n.newPassword===n.confirmPassword?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"text-green-600",children:"Le password corrispondono"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-600",children:"Le password non corrispondono"})]})})]}),C&&(0,t.jsxs)(p.Fc,{className:(0,N.cn)("success"===C.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===C.type?(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(j.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(p.TN,{className:(0,N.cn)("success"===C.type?"text-green-800":"text-red-800"),children:[C.text,"success"===C.type&&(0,t.jsx)("div",{className:"mt-2 text-sm",children:"Verrai reindirizzato al login tra pochi secondi..."})]})]}),(0,t.jsx)(d.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:A||!k.isValid||n.newPassword!==n.confirmPassword,children:A?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Aggiornamento..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Reimposta Password"]})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Sicurezza"}),(0,t.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,t.jsx)("li",{children:"• Usa una password unica che non hai mai utilizzato prima"}),(0,t.jsx)("li",{children:"• Combina lettere maiuscole, minuscole, numeri e simboli"}),(0,t.jsx)("li",{children:"• Evita informazioni personali facilmente indovinabili"}),(0,t.jsx)("li",{children:"• Considera l'uso di un gestore di password"})]})]})]})})]})]})})}function P(){return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"w-8 h-8 text-white"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,t.jsx)("p",{className:"text-slate-600",children:"Reimposta Password"})]}),(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."}),children:(0,t.jsx)(k,{})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(i(),{href:"/login",children:(0,t.jsxs)(d.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,t.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},75525:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78749:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,s,r)=>{"use strict";r.d(s,{J:()=>i});var t=r(95155);r(12115);var a=r(40968),n=r(59434);function i(e){let{className:s,...r}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},92657:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[3455,541,8441,1684,7358],()=>s(4366)),_N_E=e.O()}]);