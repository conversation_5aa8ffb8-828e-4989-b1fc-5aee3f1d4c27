'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DangerButton, SecondaryButton } from '@/components/ui/animated-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { usersApi } from '@/lib/api'
import { Loader2, RotateCcw, AlertTriangle, Trash2 } from 'lucide-react'

export default function ResetDatabase() {
  const [confirmText, setConfirmText] = useState('')
  const [confirmChecked, setConfirmChecked] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const handleReset = async () => {
    if (confirmText !== 'RESET DATABASE' || !confirmChecked) {
      setError('Conferma richiesta per procedere con il reset')
      return
    }

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      await usersApi.resetDatabase()
      setSuccess('Database resettato con successo! Tutti i dati sono stati eliminati.')
      setConfirmText('')
      setConfirmChecked(false)
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante il reset del database')
    } finally {
      setLoading(false)
    }
  }

  const isResetEnabled = confirmText === 'RESET DATABASE' && confirmChecked && !loading

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <RotateCcw className="h-5 w-5" />
          Reset Database
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Avviso di pericolo */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-6 w-6 text-red-600 mt-0.5" />
            <div>
              <h4 className="font-bold text-red-900 text-lg">⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE</h4>
              <div className="text-red-700 mt-2 space-y-2">
                <p className="font-medium">
                  Questa operazione eliminerà PERMANENTEMENTE tutti i dati dal database:
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Tutti gli utenti (eccetto l'amministratore principale)</li>
                  <li>Tutti i cantieri e i progetti</li>
                  <li>Tutti i cavi installati</li>
                  <li>Tutte le bobine del parco cavi</li>
                  <li>Tutti i comandi e le certificazioni</li>
                  <li>Tutti i report e i dati di produttività</li>
                </ul>
                <p className="font-bold text-red-800 mt-3">
                  NON È POSSIBILE RECUPERARE I DATI DOPO IL RESET!
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Messaggi di stato */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-600">{success}</p>
          </div>
        )}

        {/* Form di conferma */}
        <div className="space-y-4 border-t pt-6">
          <div>
            <h4 className="font-semibold text-slate-900 mb-4">
              Conferma Reset Database
            </h4>
            <p className="text-sm text-slate-600 mb-4">
              Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:
            </p>
          </div>

          <div className="space-y-4">
            {/* Step 1: Digitare testo di conferma */}
            <div className="space-y-2">
              <Label htmlFor="confirm-text" className="text-sm font-medium">
                1. Digita esattamente: <code className="bg-slate-100 px-2 py-1 rounded text-red-600 font-bold">RESET DATABASE</code>
              </Label>
              <Input
                id="confirm-text"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder="Digita: RESET DATABASE"
                disabled={loading}
                className={confirmText === 'RESET DATABASE' ? 'border-green-500' : ''}
              />
            </div>

            {/* Step 2: Checkbox di conferma */}
            <div className="flex items-start space-x-3">
              <Checkbox
                id="confirm-checkbox"
                checked={confirmChecked}
                onCheckedChange={setConfirmChecked}
                disabled={loading}
              />
              <Label htmlFor="confirm-checkbox" className="text-sm leading-relaxed">
                2. Confermo di aver compreso che questa operazione eliminerà TUTTI i dati dal database 
                in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario.
              </Label>
            </div>
          </div>

          {/* Stato di conferma */}
          <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
            <h5 className="font-medium text-slate-900 mb-2">Stato Conferma:</h5>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  confirmText === 'RESET DATABASE' ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>Testo di conferma: {confirmText === 'RESET DATABASE' ? '✓ Corretto' : '✗ Richiesto'}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  confirmChecked ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>Checkbox confermata: {confirmChecked ? '✓ Sì' : '✗ Richiesta'}</span>
              </div>
            </div>
          </div>

          {/* Pulsante di reset */}
          <DangerButton
            onClick={handleReset}
            disabled={!isResetEnabled}
            className="w-full"
            size="lg"
            loading={loading}
            icon={<Trash2 className="h-5 w-5" />}
            glow
          >
            {loading ? 'Reset in corso...' : 'RESET DATABASE - ELIMINA TUTTI I DATI'}
          </DangerButton>

          {!isResetEnabled && (
            <p className="text-center text-sm text-slate-500">
              Completa tutti i passaggi di conferma per abilitare il reset
            </p>
          )}
        </div>

        {/* Informazioni aggiuntive */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
          <h5 className="font-medium text-blue-900 mb-2">Informazioni Tecniche:</h5>
          <ul className="text-blue-700 space-y-1">
            <li>• Il reset manterrà la struttura delle tabelle</li>
            <li>• L'utente amministratore principale verrà ricreato</li>
            <li>• Le configurazioni di sistema verranno ripristinate ai valori di default</li>
            <li>• L'operazione può richiedere alcuni minuti per completarsi</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
