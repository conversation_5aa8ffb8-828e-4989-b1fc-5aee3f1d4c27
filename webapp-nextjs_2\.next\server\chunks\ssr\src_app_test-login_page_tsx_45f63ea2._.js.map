{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/app/test-login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function TestLoginPage() {\n  const { login, loginCantiere, user, cantiere, isLoading } = useAuth()\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const testUserLogin = async () => {\n    setLoading(true)\n    setResult('')\n    \n    try {\n      console.log('Testing user login...')\n      \n      const result = await login('admin', 'admin')\n      \n      console.log('Login result:', result)\n      \n      if (result.success && result.user) {\n        setResult(`SUCCESS: Login riuscito per ${result.user.username} (${result.user.ruolo})`)\n      } else {\n        setResult(`ERROR: ${result.error || 'Login fallito'}`)\n      }\n    } catch (error: any) {\n      console.error('Login test error:', error)\n      setResult(`EXCEPTION: ${error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const testCantiereLogin = async () => {\n    setLoading(true)\n    setResult('')\n    \n    try {\n      console.log('Testing cantiere login...')\n      \n      const result = await loginCantiere('TEST123', 'test123')\n      \n      console.log('Cantiere login result:', result)\n      \n      if (result.success && result.cantiere) {\n        setResult(`SUCCESS: Login cantiere riuscito per ${result.cantiere.commessa}`)\n      } else {\n        setResult(`ERROR: ${result.error || 'Login cantiere fallito'}`)\n      }\n    } catch (error: any) {\n      console.error('Cantiere login test error:', error)\n      setResult(`EXCEPTION: ${error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const testDirectAPI = async () => {\n    setLoading(true)\n    setResult('')\n    \n    try {\n      console.log('Testing direct API call...')\n      \n      const formData = new FormData()\n      formData.append('username', 'admin')\n      formData.append('password', 'admin')\n      \n      const response = await fetch('http://localhost:8001/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: formData\n      })\n\n      console.log('Response status:', response.status)\n      const data = await response.json()\n      console.log('Response data:', data)\n\n      if (response.ok) {\n        setResult(`SUCCESS: API diretta funzionante - Token: ${data.access_token.substring(0, 50)}...`)\n      } else {\n        setResult(`ERROR: ${response.status} - ${JSON.stringify(data, null, 2)}`)\n      }\n    } catch (error: any) {\n      console.error('Direct API test error:', error)\n      setResult(`EXCEPTION: ${error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\">\n        <h1 className=\"text-2xl font-bold text-center mb-6\">Test Login Sistema</h1>\n        \n        <div className=\"space-y-4\">\n          <div className=\"text-sm text-gray-600\">\n            <p><strong>Stato Auth:</strong></p>\n            <p>Loading: {isLoading ? 'Sì' : 'No'}</p>\n            <p>User: {user ? `${user.username} (${user.ruolo})` : 'Nessuno'}</p>\n            <p>Cantiere: {cantiere ? cantiere.commessa : 'Nessuno'}</p>\n          </div>\n          \n          <hr />\n          \n          <button\n            onClick={testUserLogin}\n            disabled={loading}\n            className=\"w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50\"\n          >\n            {loading ? 'Testing...' : 'Test Login Utente (admin/admin)'}\n          </button>\n          \n          <button\n            onClick={testCantiereLogin}\n            disabled={loading}\n            className=\"w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50\"\n          >\n            {loading ? 'Testing...' : 'Test Login Cantiere (TEST123/test123)'}\n          </button>\n          \n          <button\n            onClick={testDirectAPI}\n            disabled={loading}\n            className=\"w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50\"\n          >\n            {loading ? 'Testing...' : 'Test API Diretta'}\n          </button>\n          \n          {result && (\n            <div className={`p-4 rounded ${\n              result.startsWith('SUCCESS') \n                ? 'bg-green-100 text-green-800' \n                : 'bg-red-100 text-red-800'\n            }`}>\n              <pre className=\"whitespace-pre-wrap text-sm\">{result}</pre>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB;QACpB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,SAAS,MAAM,MAAM,SAAS;YAEpC,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,UAAU,CAAC,4BAA4B,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACxF,OAAO;gBACL,UAAU,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,iBAAiB;YACvD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qBAAqB;YACnC,UAAU,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,SAAS,MAAM,cAAc,WAAW;YAE9C,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,IAAI,OAAO,OAAO,IAAI,OAAO,QAAQ,EAAE;gBACrC,UAAU,CAAC,qCAAqC,EAAE,OAAO,QAAQ,CAAC,QAAQ,EAAE;YAC9E,OAAO;gBACL,UAAU,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,0BAA0B;YAChE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,UAAU,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,YAAY;YAC5B,SAAS,MAAM,CAAC,YAAY;YAE5B,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,CAAC,0CAA0C,EAAE,KAAK,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;YAChG,OAAO;gBACL,UAAU,CAAC,OAAO,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;YAC1E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,UAAU,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAsC;;;;;;8BAEpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE,cAAA,8OAAC;kDAAO;;;;;;;;;;;8CACX,8OAAC;;wCAAE;wCAAU,YAAY,OAAO;;;;;;;8CAChC,8OAAC;;wCAAE;wCAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG;;;;;;;8CACtD,8OAAC;;wCAAE;wCAAW,WAAW,SAAS,QAAQ,GAAG;;;;;;;;;;;;;sCAG/C,8OAAC;;;;;sCAED,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,eAAe;;;;;;sCAG5B,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,eAAe;;;;;;sCAG5B,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,eAAe;;;;;;wBAG3B,wBACC,8OAAC;4BAAI,WAAW,CAAC,YAAY,EAC3B,OAAO,UAAU,CAAC,aACd,gCACA,2BACJ;sCACA,cAAA,8OAAC;gCAAI,WAAU;0CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D", "debugId": null}}]}