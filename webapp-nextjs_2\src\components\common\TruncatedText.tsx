import React, { useState } from 'react'

interface TruncatedTextProps {
  text: string
  maxLength?: number
  className?: string
}

export default function TruncatedText({
  text,
  maxLength = 20,
  className = ""
}: TruncatedTextProps) {
  const [showTooltip, setShowTooltip] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  if (!text) return <span className="text-gray-400">-</span>

  const shouldTruncate = text.length > maxLength
  const displayText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text

  if (!shouldTruncate) {
    return <span className={className}>{text}</span>
  }

  const handleMouseEnter = (e: React.MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY })
    setShowTooltip(true)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY })
  }

  return (
    <div className="relative inline-block">
      <span
        className={`cursor-help ${className}`}
        style={{
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          maxWidth: '100%',
          display: 'inline-block'
        }}
        onMouseEnter={handleMouseEnter}
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setShowTooltip(false)}
        title={text} // Fallback browser tooltip
      >
        {displayText}
      </span>

      {/* Custom tooltip */}
      {showTooltip && (
        <div
          className="fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none"
          style={{
            top: mousePosition.y - 40,
            left: mousePosition.x - 150,
            maxWidth: '300px',
            wordWrap: 'break-word',
            whiteSpace: 'normal'
          }}
        >
          {text}
          {/* Arrow */}
          <div
            className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0"
            style={{
              borderLeft: '5px solid transparent',
              borderRight: '5px solid transparent',
              borderTop: '5px solid #1f2937'
            }}
          />
        </div>
      )}
    </div>
  )
}
