#!/usr/bin/env python
# run_system_advanced.py - Sistema di avvio avanzato per webapp-nextjs
import subprocess
import sys
import os
import time
import signal
import json
import webbrowser
import logging
from pathlib import Path
from datetime import datetime

class CablysRunSystem:
    def __init__(self, config_file="run_config.json"):
        self.config = self.load_config(config_file)
        self.backend_process = None
        self.frontend_process = None
        self.original_dir = os.getcwd()
        self.setup_logging()
        
    def load_config(self, config_file):
        """Carica la configurazione dal file JSON"""
        try:
            config_path = Path(__file__).resolve().parent / config_file
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️  File di configurazione {config_file} non trovato. Uso configurazione predefinita.")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"❌ Errore nel file di configurazione: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """Configurazione predefinita"""
        return {
            "system": {"name": "CABLYS Next.js Webapp", "version": "2.0.0"},
            "backend": {"enabled": True, "host": "0.0.0.0", "port": 8001, "reload": True},
            "frontend": {"enabled": True, "port": 3000, "turbopack": True},
            "development": {"auto_open_browser": False, "show_logs": True}
        }
    
    def setup_logging(self):
        """Configura il sistema di logging"""
        log_level = self.config.get("monitoring", {}).get("log_level", "INFO")
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        self.logger = logging.getLogger(__name__)
    
    def print_banner(self):
        """Stampa il banner di avvio"""
        system_info = self.config.get("system", {})
        name = system_info.get("name", "CABLYS Next.js Webapp")
        version = system_info.get("version", "2.0.0")
        
        print("\n" + "="*60)
        print(f"🚀 {name}")
        print(f"📦 Versione: {version}")
        print(f"🕒 Avvio: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        print("="*60 + "\n")
    
    def check_port_available(self, port):
        """Verifica se una porta è disponibile"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    def check_dependencies(self):
        """Verifica che le dipendenze necessarie siano installate"""
        self.logger.info("🔍 Verifica delle dipendenze...")
        
        dependencies = [
            ("Node.js", ["node", "--version"]),
            ("npm", ["npm", "--version"]),
            ("Python", [sys.executable, "--version"]),
            ("Uvicorn", [sys.executable, "-m", "uvicorn", "--version"])
        ]
        
        for name, cmd in dependencies:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"✅ {name}: {version}")
                    self.logger.info(f"{name} trovato: {version}")
                else:
                    print(f"❌ {name} non trovato")
                    self.logger.error(f"{name} non trovato")
                    return False
            except FileNotFoundError:
                print(f"❌ {name} non trovato")
                self.logger.error(f"{name} non trovato")
                return False
        
        # Verifica node_modules
        frontend_dir = Path(__file__).resolve().parent
        node_modules = frontend_dir / "node_modules"
        if node_modules.exists():
            print("✅ node_modules trovato")
            self.logger.info("node_modules trovato")
        else:
            print("❌ node_modules non trovato. Eseguire 'npm install'")
            self.logger.error("node_modules non trovato")
            return False
        
        print("✅ Tutte le dipendenze sono soddisfatte!\n")
        return True
    
    def check_database_connection(self):
        """Verifica la connessione al database (opzionale)"""
        if not self.config.get("database", {}).get("check_connection", False):
            return True
            
        self.logger.info("🔍 Verifica connessione database...")
        try:
            import psycopg2
            db_config = self.config.get("database", {})
            conn = psycopg2.connect(
                host=db_config.get("host", "localhost"),
                port=db_config.get("port", 5432),
                database=db_config.get("name", "cantieri"),
                user=db_config.get("user", "postgres"),
                password="Taranto"  # Password hardcoded come nel sistema originale
            )
            conn.close()
            print("✅ Connessione database riuscita")
            self.logger.info("Connessione database riuscita")
            return True
        except ImportError:
            print("⚠️  psycopg2 non installato, salto verifica database")
            return True
        except Exception as e:
            print(f"⚠️  Connessione database fallita: {e}")
            self.logger.warning(f"Connessione database fallita: {e}")
            return True  # Non blocchiamo l'avvio per problemi di DB
    
    def run_backend(self):
        """Avvia il server FastAPI (backend)"""
        backend_config = self.config.get("backend", {})
        
        if not backend_config.get("enabled", True):
            self.logger.info("Backend disabilitato nella configurazione")
            return "disabled"
        
        port = backend_config.get("port", 8001)
        
        # Verifica se la porta è disponibile
        if not self.check_port_available(port):
            print(f"⚠️  La porta {port} è già in uso. Il backend potrebbe essere già in esecuzione.")
            self.logger.warning(f"Porta {port} già in uso")
            return "already_running"
        
        print("🔧 Avvio del backend...")
        self.logger.info("Avvio backend FastAPI")
        
        # Ottieni il percorso della directory webapp
        current_dir = Path(__file__).resolve().parent
        relative_path = backend_config.get("relative_path", "../webapp")
        webapp_dir = current_dir / relative_path
        
        if not webapp_dir.exists():
            print(f"❌ Directory del backend non trovata: {webapp_dir}")
            self.logger.error(f"Directory backend non trovata: {webapp_dir}")
            return None
        
        # Cambia directory
        os.chdir(webapp_dir)
        
        # Costruisci il comando
        host = backend_config.get("host", "0.0.0.0")
        module = backend_config.get("module", "backend.main:app")
        reload = backend_config.get("reload", True)
        
        cmd = [sys.executable, "-m", "uvicorn", module, "--host", host, f"--port={port}"]
        if reload:
            cmd.append("--reload")
        
        print(f"📝 Comando: {' '.join(cmd)}")
        print(f"📁 Directory: {os.getcwd()}")
        
        try:
            process = subprocess.Popen(cmd)
            time.sleep(3)  # Attendi avvio
            print("✅ Backend avviato con successo!")
            self.logger.info("Backend avviato con successo")
            return process
        except Exception as e:
            print(f"❌ Errore durante l'avvio del backend: {e}")
            self.logger.error(f"Errore avvio backend: {e}")
            return None

    def run_frontend(self):
        """Avvia il server Next.js (frontend)"""
        frontend_config = self.config.get("frontend", {})

        if not frontend_config.get("enabled", True):
            self.logger.info("Frontend disabilitato nella configurazione")
            return "disabled"

        print("🎨 Avvio del frontend Next.js...")
        self.logger.info("Avvio frontend Next.js")

        # Torna alla directory del frontend
        frontend_dir = Path(__file__).resolve().parent
        os.chdir(frontend_dir)

        # Costruisci il comando
        command = frontend_config.get("command", "npm run dev")
        cmd = command.split()

        print(f"📝 Comando: {' '.join(cmd)}")
        print(f"📁 Directory: {os.getcwd()}")

        try:
            process = subprocess.Popen(cmd, shell=True)
            time.sleep(5)  # Attendi avvio
            print("✅ Frontend Next.js avviato con successo!")
            self.logger.info("Frontend avviato con successo")
            return process
        except Exception as e:
            print(f"❌ Errore durante l'avvio del frontend: {e}")
            self.logger.error(f"Errore avvio frontend: {e}")
            return None

    def open_browser(self):
        """Apre il browser automaticamente"""
        if self.config.get("development", {}).get("auto_open_browser", False):
            frontend_port = self.config.get("frontend", {}).get("port", 3000)
            url = f"http://localhost:{frontend_port}"
            print(f"🌐 Apertura browser: {url}")
            try:
                webbrowser.open(url)
            except Exception as e:
                self.logger.warning(f"Impossibile aprire il browser: {e}")

    def print_status(self):
        """Stampa lo stato dei servizi"""
        backend_config = self.config.get("backend", {})
        frontend_config = self.config.get("frontend", {})

        print("\n" + "="*50)
        print("🎉 Sistema CABLYS avviato con successo!")
        print("="*50)

        if backend_config.get("enabled", True):
            backend_port = backend_config.get("port", 8001)
            print(f"🔧 Backend API: http://localhost:{backend_port}")

        if frontend_config.get("enabled", True):
            frontend_port = frontend_config.get("port", 3000)
            print(f"🎨 Frontend: http://localhost:{frontend_port}")

        if hasattr(self, 'backend_already_running') and self.backend_already_running:
            print("ℹ️  (Il backend era già in esecuzione)")

        print("\n💡 Premi Ctrl+C per terminare i server")
        print("="*50 + "\n")

    def setup_signal_handlers(self):
        """Configura i gestori dei segnali"""
        def signal_handler(sig, frame):
            print("\n🛑 Terminazione dei server in corso...")
            self.logger.info("Ricevuto segnale di terminazione")

            if self.frontend_process and self.frontend_process != "disabled":
                try:
                    self.frontend_process.terminate()
                    print("✅ Frontend terminato")
                except:
                    pass

            if (self.backend_process and
                self.backend_process != "already_running" and
                self.backend_process != "disabled"):
                try:
                    self.backend_process.terminate()
                    print("✅ Backend terminato")
                except:
                    pass

            print("👋 Arrivederci!")
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)

    def run(self):
        """Funzione principale di avvio"""
        try:
            # Banner di avvio
            self.print_banner()

            # Verifica dipendenze
            if not self.check_dependencies():
                print("❌ Dipendenze mancanti. Installare le dipendenze necessarie.")
                return False

            # Verifica database (opzionale)
            self.check_database_connection()

            # Avvia backend
            self.backend_process = self.run_backend()
            self.backend_already_running = self.backend_process == "already_running"

            if not self.backend_process and self.backend_process != "disabled":
                print("❌ Impossibile avviare il backend.")
                return False

            # Torna alla directory originale
            os.chdir(self.original_dir)

            # Avvia frontend
            self.frontend_process = self.run_frontend()
            if not self.frontend_process and self.frontend_process != "disabled":
                print("❌ Impossibile avviare il frontend.")
                # Termina il backend se l'abbiamo avviato noi
                if (self.backend_process and
                    self.backend_process != "already_running" and
                    self.backend_process != "disabled"):
                    self.backend_process.terminate()
                return False

            # Torna alla directory originale
            os.chdir(self.original_dir)

            # Apri browser se configurato
            self.open_browser()

            # Stampa stato
            self.print_status()

            # Configura gestori segnali
            self.setup_signal_handlers()

            # Mantieni il programma in esecuzione
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass

            return True

        except Exception as e:
            self.logger.error(f"Errore durante l'avvio: {e}")
            print(f"❌ Errore durante l'avvio: {e}")
            return False

def main():
    """Punto di ingresso principale"""
    run_system = CablysRunSystem()
    success = run_system.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
