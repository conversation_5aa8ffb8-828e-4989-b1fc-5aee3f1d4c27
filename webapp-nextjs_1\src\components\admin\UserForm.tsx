'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { PrimaryButton, SecondaryButton } from '@/components/ui/animated-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { usersApi } from '@/lib/api'
import { User } from '@/types'
import { validateUserForm, checkRateLimit } from '@/utils/securityValidation'
import { Eye, EyeOff, Loader2, Save, X, CheckCircle, AlertCircle, Info, Shield, Building2, Mail, MapPin, User as UserIcon, Setting<PERSON>, Clock, Edit } from 'lucide-react'

interface UserFormProps {
  user?: User | null
  onSave: (user: User) => void
  onCancel: () => void
}

export default function UserForm({ user, onSave, onCancel }: UserFormProps) {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    ruolo: 'user', // L'amministratore può creare solo utenti standard
    data_scadenza: '',
    abilitato: true,
    // Nuovi campi aziendali
    ragione_sociale: '',
    indirizzo: '',
    nazione: '',
    email: '',
    vat: '',
    referente_aziendale: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [validationStatus, setValidationStatus] = useState<Record<string, 'valid' | 'invalid' | 'pending'>>({})
  const [isFormValid, setIsFormValid] = useState(false)

  // Calcola la forza della password
  const calculatePasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength += 25
    if (/[a-z]/.test(password)) strength += 25
    if (/[A-Z]/.test(password)) strength += 25
    if (/[0-9]/.test(password)) strength += 25
    if (/[^A-Za-z0-9]/.test(password)) strength += 25
    return Math.min(strength, 100)
  }

  // Validazione in tempo reale
  const validateField = (name: string, value: any) => {
    let status: 'valid' | 'invalid' | 'pending' = 'pending'

    switch (name) {
      case 'username':
        status = value && value.length >= 3 ? 'valid' : 'invalid'
        break
      case 'password':
        if (!user) { // Password obbligatoria solo per nuovi utenti
          status = value && value.length >= 8 ? 'valid' : 'invalid'
        } else {
          status = !value || value.length >= 8 ? 'valid' : 'invalid'
        }
        break
      case 'ragione_sociale':
        status = value && value.length >= 2 ? 'valid' : 'invalid'
        break
      case 'email':
        if (value) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          status = emailRegex.test(value) ? 'valid' : 'invalid'
        } else {
          status = 'valid' // Email opzionale
        }
        break
      default:
        status = 'valid'
    }

    setValidationStatus(prev => ({ ...prev, [name]: status }))
    return status
  }

  // Inizializza il form con i dati dell'utente se presente
  useEffect(() => {
    if (user) {
      const initialData = {
        username: user.username || '',
        password: '', // Non mostrare la password esistente
        ruolo: user.ruolo || 'user',
        data_scadenza: user.data_scadenza ? user.data_scadenza.split('T')[0] : '',
        abilitato: user.abilitato !== undefined ? user.abilitato : true,
        // Nuovi campi aziendali
        ragione_sociale: user.ragione_sociale || '',
        indirizzo: user.indirizzo || '',
        nazione: user.nazione || '',
        email: user.email || '',
        vat: user.vat || '',
        referente_aziendale: user.referente_aziendale || ''
      }
      setFormData(initialData)

      // Valida i campi iniziali
      Object.entries(initialData).forEach(([key, value]) => {
        validateField(key, value)
      })
    }
  }, [user])

  // Controlla se il form è valido
  useEffect(() => {
    const requiredFields = user ? ['username', 'ragione_sociale'] : ['username', 'password', 'ragione_sociale']
    const isValid = requiredFields.every(field => validationStatus[field] === 'valid')
    setIsFormValid(isValid)
  }, [validationStatus, user])

  // Gestisce il cambio dei valori del form
  const handleChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Validazione in tempo reale
    validateField(name, value)

    // Calcola forza password se è il campo password
    if (name === 'password') {
      setPasswordStrength(calculatePasswordStrength(value))
    }

    // Rimuovi l'errore per questo campo se presente
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }

    // Rimuovi messaggi di successo/errore quando l'utente modifica il form
    if (success) setSuccess('')
    if (error) setError('')
  }

  // Validazione del form con controlli di sicurezza
  const validateForm = () => {
    // Validazione sicura completa
    const validation = validateUserForm({
      username: formData.username,
      password: user ? undefined : formData.password, // Password obbligatoria solo per nuovi utenti
      ragione_sociale: formData.ragione_sociale,
      email: formData.email,
      vat: formData.vat,
      indirizzo: formData.indirizzo,
      nazione: formData.nazione,
      referente_aziendale: formData.referente_aziendale
    })

    setErrors(validation.errors)
    return validation.isValid
  }

  // Gestisce il submit del form con protezione rate limiting
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Rate limiting per prevenire spam/brute force
    const clientId = `user-form-${user?.id_utente || 'new'}-${Date.now()}`
    if (!checkRateLimit(clientId, 5, 60000)) { // Max 5 tentativi per minuto
      setError('Troppi tentativi. Riprova tra un minuto.')
      return
    }

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError('')

    try {
      // Prepara i dati da inviare
      const userData = {
        ...formData
      }

      // Per nuovi utenti, forza sempre il ruolo "user"
      if (!user) {
        userData.ruolo = 'user'
      }

      // Rimuovi la password se è vuota (modifica utente)
      if (user && !userData.password.trim()) {
        delete (userData as any).password
      }

      // Converti la data in formato ISO se presente
      if (userData.data_scadenza) {
        userData.data_scadenza = userData.data_scadenza
      }

      let result
      if (user) {
        // Aggiorna l'utente esistente
        result = await usersApi.updateUser(user.id_utente, userData)
      } else {
        // Crea un nuovo utente
        result = await usersApi.createUser(userData)
      }

      setSuccess(user ? 'Utente aggiornato con successo!' : 'Nuovo utente creato con successo!')

      // Attendi un momento per mostrare il messaggio di successo
      setTimeout(() => {
        onSave(result)
      }, 1500)
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante il salvataggio dell\'utente')
    } finally {
      setLoading(false)
    }
  }

  // Componente per indicatore di validazione
  const ValidationIndicator = ({ status }: { status: 'valid' | 'invalid' | 'pending' }) => {
    if (status === 'valid') {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else if (status === 'invalid') {
      return <AlertCircle className="h-4 w-4 text-red-500" />
    }
    return null
  }

  // Componente per indicatore forza password
  const PasswordStrengthIndicator = () => {
    if (!formData.password) return null

    const getStrengthColor = () => {
      if (passwordStrength < 50) return 'bg-red-500'
      if (passwordStrength < 75) return 'bg-yellow-500'
      return 'bg-green-500'
    }

    const getStrengthText = () => {
      if (passwordStrength < 25) return 'Molto debole'
      if (passwordStrength < 50) return 'Debole'
      if (passwordStrength < 75) return 'Media'
      if (passwordStrength < 100) return 'Forte'
      return 'Molto forte'
    }

    return (
      <div className="mt-2 space-y-1">
        <div className="flex items-center justify-between text-xs">
          <span className="text-slate-600">Forza password:</span>
          <span className={`font-medium ${passwordStrength < 50 ? 'text-red-600' : passwordStrength < 75 ? 'text-yellow-600' : 'text-green-600'}`}>
            {getStrengthText()}
          </span>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor()}`}
            style={{ width: `${passwordStrength}%` }}
          />
        </div>
      </div>
    )
  }

  return (
    <Card className="shadow-lg">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            {user ? <Edit className="h-5 w-5 text-blue-600" /> : <UserIcon className="h-5 w-5 text-blue-600" />}
          </div>
          <div>
            <CardTitle className="text-xl">
              {user ? `Modifica Utente: ${user.username}` : 'Crea Nuovo Utente Standard'}
            </CardTitle>
            <CardDescription>
              {user ? 'Aggiorna le informazioni dell\'utente esistente' : 'Inserisci i dati per creare un nuovo utente nel sistema'}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {/* Messaggi di stato */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2">
            <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2">
            <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
            <p className="text-green-600">{success}</p>
          </div>
        )}

        {/* Indicatore di progresso form */}
        <div className="mb-6">
          <div className="flex items-center justify-between text-sm text-slate-600 mb-2">
            <span>Completamento form</span>
            <span>{isFormValid ? '✓ Completo' : 'In corso...'}</span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-500 ${isFormValid ? 'bg-green-500' : 'bg-blue-500'}`}
              style={{ width: `${isFormValid ? 100 : 60}%` }}
            />
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Sezione Credenziali */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2 border-b border-slate-200">
              <Shield className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-slate-900">Credenziali di Accesso</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Username */}
              <div className="space-y-2">
                <Label htmlFor="username" className="flex items-center gap-2">
                  Username *
                  <ValidationIndicator status={validationStatus.username || 'pending'} />
                </Label>
                <div className="relative">
                  <Input
                    id="username"
                    value={formData.username}
                    onChange={(e) => handleChange('username', e.target.value)}
                    disabled={loading}
                    className={`${errors.username ? 'border-red-500' : validationStatus.username === 'valid' ? 'border-green-500' : ''} transition-colors duration-200`}
                    placeholder="Inserisci username univoco"
                  />
                </div>
                {errors.username && <p className="text-sm text-red-600 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.username}</p>}
                {validationStatus.username === 'valid' && !errors.username && (
                  <p className="text-sm text-green-600 flex items-center gap-1"><CheckCircle className="h-3 w-3" />Username valido</p>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password" className="flex items-center gap-2">
                  {user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password *'}
                  <ValidationIndicator status={validationStatus.password || 'pending'} />
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleChange('password', e.target.value)}
                    disabled={loading}
                    className={`${errors.password ? 'border-red-500' : validationStatus.password === 'valid' ? 'border-green-500' : ''} pr-10 transition-colors duration-200`}
                    placeholder={user ? "Lascia vuoto per mantenere la password attuale" : "Inserisci password sicura"}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
                {errors.password && <p className="text-sm text-red-600 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.password}</p>}
                <PasswordStrengthIndicator />
              </div>
            </div>
          </div>

          {/* Sezione Dati Aziendali */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2 border-b border-slate-200">
              <Building2 className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-slate-900">Informazioni Aziendali</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Ragione Sociale */}
              <div className="space-y-2">
                <Label htmlFor="ragione_sociale" className="flex items-center gap-2">
                  Ragione Sociale *
                  <ValidationIndicator status={validationStatus.ragione_sociale || 'pending'} />
                </Label>
                <Input
                  id="ragione_sociale"
                  value={formData.ragione_sociale}
                  onChange={(e) => handleChange('ragione_sociale', e.target.value)}
                  disabled={loading}
                  className={`${errors.ragione_sociale ? 'border-red-500' : validationStatus.ragione_sociale === 'valid' ? 'border-green-500' : ''} transition-colors duration-200`}
                  placeholder="Nome dell'azienda o organizzazione"
                />
                {errors.ragione_sociale && <p className="text-sm text-red-600 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.ragione_sociale}</p>}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email
                  <ValidationIndicator status={validationStatus.email || 'pending'} />
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  disabled={loading}
                  className={`${errors.email ? 'border-red-500' : validationStatus.email === 'valid' ? 'border-green-500' : ''} transition-colors duration-200`}
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="text-sm text-red-600 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.email}</p>}
              </div>

              {/* Indirizzo */}
              <div className="space-y-2">
                <Label htmlFor="indirizzo" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Indirizzo
                </Label>
                <Input
                  id="indirizzo"
                  value={formData.indirizzo}
                  onChange={(e) => handleChange('indirizzo', e.target.value)}
                  disabled={loading}
                  placeholder="Via, numero civico, città"
                  className="transition-colors duration-200"
                />
              </div>

              {/* Nazione */}
              <div className="space-y-2">
                <Label htmlFor="nazione">Nazione</Label>
                <Input
                  id="nazione"
                  value={formData.nazione}
                  onChange={(e) => handleChange('nazione', e.target.value)}
                  disabled={loading}
                  placeholder="Italia"
                  className="transition-colors duration-200"
                />
              </div>

              {/* VAT */}
              <div className="space-y-2">
                <Label htmlFor="vat">Partita IVA</Label>
                <Input
                  id="vat"
                  value={formData.vat}
                  onChange={(e) => handleChange('vat', e.target.value)}
                  disabled={loading}
                  placeholder="*************"
                  className="transition-colors duration-200"
                />
              </div>

              {/* Referente Aziendale */}
              <div className="space-y-2">
                <Label htmlFor="referente_aziendale">Referente Aziendale</Label>
                <Input
                  id="referente_aziendale"
                  value={formData.referente_aziendale}
                  onChange={(e) => handleChange('referente_aziendale', e.target.value)}
                  disabled={loading}
                  placeholder="Nome e cognome del referente"
                  className="transition-colors duration-200"
                />
              </div>
            </div>
          </div>

          {/* Sezione Configurazioni */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2 border-b border-slate-200">
              <Settings className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-slate-900">Configurazioni Account</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Data Scadenza */}
              <div className="space-y-2">
                <Label htmlFor="data_scadenza" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Data Scadenza
                </Label>
                <Input
                  id="data_scadenza"
                  type="date"
                  value={formData.data_scadenza}
                  onChange={(e) => handleChange('data_scadenza', e.target.value)}
                  disabled={loading}
                  className="transition-colors duration-200"
                />
                <p className="text-xs text-slate-500">Lascia vuoto per account senza scadenza</p>
              </div>

              {/* Ruolo - Solo per modifica, per creazione è sempre "user" */}
              <div className="space-y-2">
                <Label htmlFor="ruolo">Ruolo Utente</Label>
                {user ? (
                  <Select
                    value={formData.ruolo}
                    onValueChange={(value) => handleChange('ruolo', value)}
                    disabled={loading}
                  >
                    <SelectTrigger className="transition-colors duration-200">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User Standard</SelectItem>
                      <SelectItem value="cantieri_user">Cantieri User</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2">
                    <Badge variant="outline" className="bg-blue-100 text-blue-700">User Standard</Badge>
                    <span>Ruolo predefinito per nuovi utenti</span>
                  </div>
                )}
              </div>
            </div>

            {/* Utente Abilitato */}
            <div className="flex items-center space-x-3 p-4 bg-slate-50 rounded-lg">
              <Checkbox
                id="abilitato"
                checked={formData.abilitato}
                onCheckedChange={(checked) => handleChange('abilitato', checked)}
                disabled={loading || (user && user.ruolo === 'owner')}
              />
              <div className="flex-1">
                <Label htmlFor="abilitato" className="font-medium">Account Abilitato</Label>
                <p className="text-sm text-slate-600">L'utente può accedere al sistema e utilizzare le funzionalità</p>
              </div>
              {formData.abilitato ? (
                <Badge className="bg-green-100 text-green-700">Attivo</Badge>
              ) : (
                <Badge variant="outline" className="bg-red-100 text-red-700">Disabilitato</Badge>
              )}
            </div>
          </div>

          {/* Pulsanti */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-slate-200">
            <div className="text-sm text-slate-600">
              {isFormValid ? (
                <span className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  Form completato correttamente
                </span>
              ) : (
                <span className="flex items-center gap-1">
                  <Info className="h-4 w-4" />
                  Completa i campi obbligatori per continuare
                </span>
              )}
            </div>

            <div className="flex space-x-4">
              <SecondaryButton
                type="button"
                onClick={onCancel}
                disabled={loading}
                icon={<X className="h-4 w-4" />}
                className="min-w-[120px]"
              >
                Annulla
              </SecondaryButton>
              <PrimaryButton
                type="submit"
                loading={loading}
                disabled={!isFormValid}
                icon={<Save className="h-4 w-4" />}
                glow={isFormValid}
                className="min-w-[120px]"
              >
                {loading ? 'Salvataggio...' : user ? 'Aggiorna Utente' : 'Crea Utente'}
              </PrimaryButton>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
