'use client'

import { useEffect, useState } from 'react'

interface SecurityEvent {
  type: 'login_attempt' | 'form_submission' | 'suspicious_activity' | 'rate_limit_hit'
  timestamp: number
  details: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
}

interface SecurityMetrics {
  totalEvents: number
  loginAttempts: number
  blockedRequests: number
  suspiciousActivity: number
  lastEventTime: number | null
}

export const useSecurityMonitoring = () => {
  const [events, setEvents] = useState<SecurityEvent[]>([])
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalEvents: 0,
    loginAttempts: 0,
    blockedRequests: 0,
    suspiciousActivity: 0,
    lastEventTime: null
  })

  // Registra un evento di sicurezza
  const logSecurityEvent = (
    type: SecurityEvent['type'],
    details: Record<string, any>,
    severity: SecurityEvent['severity'] = 'medium'
  ) => {
    const event: SecurityEvent = {
      type,
      timestamp: Date.now(),
      details,
      severity
    }

    setEvents(prev => {
      const newEvents = [...prev, event].slice(-100) // Mantieni solo ultimi 100 eventi
      return newEvents
    })

    // Aggiorna metriche
    setMetrics(prev => ({
      totalEvents: prev.totalEvents + 1,
      loginAttempts: prev.loginAttempts + (type === 'login_attempt' ? 1 : 0),
      blockedRequests: prev.blockedRequests + (severity === 'high' || severity === 'critical' ? 1 : 0),
      suspiciousActivity: prev.suspiciousActivity + (type === 'suspicious_activity' ? 1 : 0),
      lastEventTime: event.timestamp
    }))

    // Log in console per debug
    console.log('Security Event:', {
      type,
      details,
      timestamp: new Date(event.timestamp).toISOString()
    })

    // In produzione, inviare a sistema di monitoring
    if (process.env.NODE_ENV === 'production' && (severity === 'high' || severity === 'critical')) {
      // Qui integrare con servizi come Sentry, LogRocket, etc.
    }
  }

  // Monitora eventi del browser
  useEffect(() => {
    // Disabilita il monitoraggio DevTools in sviluppo
    const detectDevTools = () => {
      // Solo in produzione
      if (process.env.NODE_ENV === 'production') {
        const threshold = 160
        if (window.outerHeight - window.innerHeight > threshold ||
            window.outerWidth - window.innerWidth > threshold) {
          logSecurityEvent('suspicious_activity', {
            action: 'devtools_detected',
            windowSize: { outer: [window.outerWidth, window.outerHeight], inner: [window.innerWidth, window.innerHeight] }
          }, 'low')
        }
      }
    }

    // Monitora copia/incolla sospetti
    const handlePaste = (e: ClipboardEvent) => {
      const pastedText = e.clipboardData?.getData('text') || ''
      
      // Controlla pattern sospetti
      const suspiciousPatterns = [
        /script/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /onload|onerror|onclick/gi,
        /<iframe|<object|<embed/gi,
        /union.*select/gi,
        /drop.*table/gi
      ]

      if (suspiciousPatterns.some(pattern => pattern.test(pastedText))) {
        e.preventDefault()
        logSecurityEvent('suspicious_activity', {
          action: 'malicious_paste_blocked',
          content: pastedText.substring(0, 100) // Solo primi 100 caratteri per privacy
        }, 'high')
      }
    }

    // Monitora tentativi di accesso a localStorage/sessionStorage
    const originalSetItem = Storage.prototype.setItem
    Storage.prototype.setItem = function(key: string, value: string) {
      // Controlla tentativi di inserire script
      if (/<script|javascript:|vbscript:/gi.test(value)) {
        logSecurityEvent('suspicious_activity', {
          action: 'malicious_storage_attempt',
          key,
          value: value.substring(0, 50)
        }, 'high')
        return
      }
      return originalSetItem.call(this, key, value)
    }

    // Monitora errori JavaScript sospetti
    const handleError = (event: ErrorEvent) => {
      const message = event.message.toLowerCase()
      
      // Errori che potrebbero indicare attacchi
      const suspiciousErrors = [
        'script error',
        'permission denied',
        'access denied',
        'blocked by cors',
        'network error'
      ]

      if (suspiciousErrors.some(error => message.includes(error))) {
        logSecurityEvent('suspicious_activity', {
          action: 'suspicious_js_error',
          message: event.message,
          filename: event.filename,
          lineno: event.lineno
        }, 'medium')
      }
    }

    // Monitora tentativi di navigazione sospetti
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Se l'utente sta lasciando la pagina in modo anomalo
      if (performance.now() < 5000) { // Meno di 5 secondi sulla pagina
        logSecurityEvent('suspicious_activity', {
          action: 'rapid_page_exit',
          timeOnPage: performance.now()
        }, 'low')
      }
    }

    // Aggiungi event listeners
    window.addEventListener('resize', detectDevTools)
    window.addEventListener('paste', handlePaste)
    window.addEventListener('error', handleError)
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Controllo periodico DevTools (solo in produzione)
    const devToolsInterval = process.env.NODE_ENV === 'production'
      ? setInterval(detectDevTools, 5000)
      : null

    // Cleanup
    return () => {
      window.removeEventListener('resize', detectDevTools)
      window.removeEventListener('paste', handlePaste)
      window.removeEventListener('error', handleError)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      if (devToolsInterval) clearInterval(devToolsInterval)

      // Ripristina localStorage originale
      Storage.prototype.setItem = originalSetItem
    }
  }, [])

  // Funzioni di utilità
  const getRecentEvents = (minutes: number = 10) => {
    const cutoff = Date.now() - (minutes * 60 * 1000)
    return events.filter(event => event.timestamp > cutoff)
  }

  const getEventsByType = (type: SecurityEvent['type']) => {
    return events.filter(event => event.type === type)
  }

  const getEventsBySeverity = (severity: SecurityEvent['severity']) => {
    return events.filter(event => event.severity === severity)
  }

  const isUnderAttack = () => {
    const recentEvents = getRecentEvents(5) // Ultimi 5 minuti
    const highSeverityEvents = recentEvents.filter(e => e.severity === 'high' || e.severity === 'critical')
    
    return highSeverityEvents.length > 3 // Più di 3 eventi critici in 5 minuti
  }

  const getThreatLevel = (): 'low' | 'medium' | 'high' | 'critical' => {
    const recentEvents = getRecentEvents(10)
    const criticalCount = recentEvents.filter(e => e.severity === 'critical').length
    const highCount = recentEvents.filter(e => e.severity === 'high').length
    
    if (criticalCount > 0) return 'critical'
    if (highCount > 2) return 'high'
    if (recentEvents.length > 10) return 'medium'
    return 'low'
  }

  // Funzioni per logging specifico
  const logLoginAttempt = (username: string, success: boolean, details?: Record<string, any>) => {
    logSecurityEvent('login_attempt', {
      username,
      success,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      ...details
    }, success ? 'low' : 'medium')
  }

  const logFormSubmission = (formType: string, success: boolean, details?: Record<string, any>) => {
    logSecurityEvent('form_submission', {
      formType,
      success,
      userAgent: navigator.userAgent,
      ...details
    }, 'low')
  }

  const logSuspiciousActivity = (activity: string, details?: Record<string, any>) => {
    logSecurityEvent('suspicious_activity', {
      activity,
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...details
    }, 'high')
  }

  const logRateLimitHit = (endpoint: string, details?: Record<string, any>) => {
    logSecurityEvent('rate_limit_hit', {
      endpoint,
      userAgent: navigator.userAgent,
      ...details
    }, 'medium')
  }

  return {
    // Dati
    events,
    metrics,
    
    // Funzioni di analisi
    getRecentEvents,
    getEventsByType,
    getEventsBySeverity,
    isUnderAttack,
    getThreatLevel,
    
    // Funzioni di logging
    logSecurityEvent,
    logLoginAttempt,
    logFormSubmission,
    logSuspiciousActivity,
    logRateLimitHit
  }
}
