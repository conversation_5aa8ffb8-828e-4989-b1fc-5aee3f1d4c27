(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[222],{13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(59434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...l}=e,d=o?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(c({variant:a,size:n,className:t})),...l})}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},47924:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>c,Zp:()=>n,aR:()=>i});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},72487:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var r=a(95155),s=a(12115),n=a(35695),i=a(66695),c=a(30285),o=a(62523),l=a(85127),d=a(40283),u=a(25731),m=a(51154),x=a(47924),h=a(84616),g=a(19946);let p=(0,g.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var v=a(78749),f=a(92657),b=a(72713),y=a(13717);let j=(0,g.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function w(){let{user:e,isAuthenticated:t,isLoading:a}=(0,d.A)(),g=(0,n.useRouter)(),[w,N]=(0,s.useState)([]),[_,k]=(0,s.useState)(!0),[z,A]=(0,s.useState)(""),[C,S]=(0,s.useState)(""),[M,P]=(0,s.useState)({}),[E,I]=(0,s.useState)(!1),[$,L]=(0,s.useState)({}),[F,B]=(0,s.useState)({}),[D,R]=(0,s.useState)(!1),[H,T]=(0,s.useState)(!1),[V,Z]=(0,s.useState)(!1),[X,q]=(0,s.useState)(null),[O,G]=(0,s.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[Q,W]=(0,s.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[J,K]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a||t||g.push("/login")},[t,a,g]),(0,s.useEffect)(()=>{t&&U()},[t]);let U=async()=>{try{k(!0);let e=await u._I.getCantieri();N(e),await Y(e)}catch(e){A("Errore nel caricamento dei cantieri")}finally{k(!1)}},Y=async e=>{try{I(!0);let t=e.map(async e=>{try{let t=await u._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:t}}catch(t){return console.error("Errore nel caricamento statistiche cantiere ".concat(e.id_cantiere,":"),t),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),a=(await Promise.all(t)).reduce((e,t)=>{let{id:a,stats:r}=t;return e[a]=r,e},{});P(a)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{I(!1)}},ee=async e=>{let t=e.id_cantiere;if($[t])L(e=>({...e,[t]:!1})),B(e=>({...e,[t]:""}));else if(F[t])L(e=>({...e,[t]:!0}));else try{K(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),a=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(t,"/view-password"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!a.ok){let e=await a.json();throw Error(e.detail||"Errore nel recupero password")}let r=await a.json();B(e=>({...e,[t]:r.password_cantiere})),L(e=>({...e,[t]:!0}))}catch(e){A(e instanceof Error?e.message:"Errore nel recupero password")}finally{K(!1)}},et=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),g.push("/cantieri/".concat(e.id_cantiere))},ea=e=>{q(e),G({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),T(!0)},er=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Failed to copy to clipboard:",e)}},es=w.filter(e=>{var t,a;return e.commessa.toLowerCase().includes(C.toLowerCase())||(null==(t=e.descrizione)?void 0:t.toLowerCase().includes(C.toLowerCase()))||(null==(a=e.nome_cliente)?void 0:a.toLowerCase().includes(C.toLowerCase()))});return a?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)(m.A,{className:"h-8 w-8 animate-spin"})}):(0,r.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsxs)("div",{className:"relative w-80",children:[(0,r.jsx)(x.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(o.p,{placeholder:"Cerca cantieri...",value:C,onChange:e=>S(e.target.value),className:"pl-8 w-full"})]})}),(0,r.jsxs)(c.$,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})]}),z&&(0,r.jsx)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-700",children:z})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Zp,{children:(0,r.jsxs)(l.XI,{children:[(0,r.jsx)(l.A0,{children:(0,r.jsxs)(l.Hj,{className:"border-b border-gray-200",children:[(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700 w-32",children:"Avanzamento"}),(0,r.jsx)(l.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,r.jsx)(l.nd,{className:"text-center font-semibold text-gray-700 w-48",children:"Azioni"})]})}),(0,r.jsx)(l.BF,{children:es.map(e=>{var t,a,s,n,i,o,d,u,x,h;return(0,r.jsxs)(l.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,r.jsx)(l.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,r.jsx)(l.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,r.jsx)(l.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,r.jsx)(l.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,r.jsx)(l.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,r.jsx)(c.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>er(e.codice_univoco),children:(0,r.jsx)(p,{className:"h-3 w-3"})})]})}),(0,r.jsx)(l.nA,{className:"py-4",children:(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsx)("div",{className:"flex items-center gap-2",children:$[e.id_cantiere]&&F[e.id_cantiere]?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:F[e.id_cantiere]}),(0,r.jsx)(c.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>ee(e),children:(0,r.jsx)(v.A,{className:"h-4 w-4"})})]}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,r.jsx)(c.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>ee(e),disabled:J,children:J?(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})})})}),(0,r.jsx)(l.nA,{className:"py-4",children:(0,r.jsx)("div",{className:"flex items-center gap-2",children:E?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,r.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out shadow-sm ".concat(((null==(t=M[e.id_cantiere])?void 0:t.percentuale_avanzamento)||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":((null==(a=M[e.id_cantiere])?void 0:a.percentuale_avanzamento)||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":((null==(s=M[e.id_cantiere])?void 0:s.percentuale_avanzamento)||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":((null==(n=M[e.id_cantiere])?void 0:n.percentuale_avanzamento)||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"),style:{width:"".concat(Math.min((null==(i=M[e.id_cantiere])?void 0:i.percentuale_avanzamento)||0,100),"%")}})})})})})}),(0,r.jsx)(l.nA,{className:"py-4 text-center",children:E?(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,r.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("span",{className:"text-sm font-semibold ".concat(((null==(o=M[e.id_cantiere])?void 0:o.percentuale_avanzamento)||0)>=90?"text-green-700":((null==(d=M[e.id_cantiere])?void 0:d.percentuale_avanzamento)||0)>=75?"text-blue-700":((null==(u=M[e.id_cantiere])?void 0:u.percentuale_avanzamento)||0)>=50?"text-yellow-700":((null==(x=M[e.id_cantiere])?void 0:x.percentuale_avanzamento)||0)>=25?"text-orange-700":"text-red-700"),children:[((null==(h=M[e.id_cantiere])?void 0:h.percentuale_avanzamento)||0).toFixed(1),"%"]})]})}),(0,r.jsx)(l.nA,{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsxs)(c.$,{size:"sm",variant:"outline",onClick:()=>ea(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,r.jsxs)(c.$,{size:"sm",onClick:()=>et(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out",title:"Accedi al cantiere",children:[(0,r.jsx)(j,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere)})})]})})})]})}},72713:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},78749:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>i,BF:()=>c,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>l});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",t),...a})})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},92524:(e,t,a)=>{Promise.resolve().then(a.bind(a,72487))},92657:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[464,455,283,441,684,358],()=>t(92524)),_N_E=e.O()}]);