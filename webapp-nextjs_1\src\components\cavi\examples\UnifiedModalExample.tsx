'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { UnifiedCableBobbinModal } from '../modals/BobinaManagementModals'
import { Cavo } from '@/types'

// Mock cable data for testing
const mockCavo: Cavo = {
  id_cavo: 'C001',
  id_cantiere: 1,
  revisione_ufficiale: 'Rev 1.0',
  tipologia: 'LIYCY',
  sezione: '3X2.5MM',
  formazione: '3X2.5MM',
  da: 'Quadro A',
  a: 'Quadro B',
  ubicazione_partenza: 'Quadro A',
  ubicazione_arrivo: 'Quadro B',
  metri_teorici: 150,
  metri_posati: 75,
  metratura_reale: 75,
  stato_installazione: 'installato',
  id_bobina: 'BOB001'
}

const mockCantiere = {
  id_cantiere: 1,
  commessa: 'TEST-001'
}

export const UnifiedModalExample: React.FC = () => {
  const [showAggiungiMetri, setShowAggiungiMetri] = useState(false)
  const [showModificaBobina, setShowModificaBobina] = useState(false)

  const handleSave = async (data: any) => {
    console.log('💾 Unified Modal Save:', data)
    
    // Simula chiamata API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Simula successo
    alert(`Operazione completata con successo!\nModalità: ${data.mode}\nCavo: ${data.cableId}`)
  }

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-2xl font-bold mb-4">Test Interfaccia Unificata</h2>
      
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Cavo di Test: {mockCavo.id_cavo}</h3>
        <p className="text-sm text-gray-600">
          {mockCavo.tipologia} {mockCavo.sezione} - Da: {mockCavo.da} A: {mockCavo.a}
        </p>
        <p className="text-sm text-gray-600">
          Metri posati: {mockCavo.metri_posati}m / {mockCavo.metri_teorici}m
        </p>
      </div>

      <div className="flex gap-4">
        <Button 
          onClick={() => setShowAggiungiMetri(true)}
          className="bg-green-600 hover:bg-green-700"
        >
          Test Modalità: Aggiungi Metri
        </Button>
        
        <Button 
          onClick={() => setShowModificaBobina(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Test Modalità: Modifica Bobina
        </Button>
      </div>

      {/* Modalità Aggiungi Metri */}
      <UnifiedCableBobbinModal
        mode="aggiungi_metri"
        open={showAggiungiMetri}
        onClose={() => setShowAggiungiMetri(false)}
        cavo={mockCavo}
        cantiere={mockCantiere}
        onSave={handleSave}
      />

      {/* Modalità Modifica Bobina */}
      <UnifiedCableBobbinModal
        mode="modifica_bobina"
        open={showModificaBobina}
        onClose={() => setShowModificaBobina(false)}
        cavo={mockCavo}
        cantiere={mockCantiere}
        onSave={handleSave}
      />
    </div>
  )
}

export default UnifiedModalExample
