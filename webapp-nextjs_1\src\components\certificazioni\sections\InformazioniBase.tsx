'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'

interface InformazioniBaseProps {
  formData: Partial<CertificazioneCavoCreate>
  cavi: any[]
  responsabili: any[]
  strumenti: StrumentoCertificato[]
  validationErrors: Record<string, string>
  isCavoLocked: boolean
  onInputChange: (field: string, value: any) => void
}

export function InformazioniBase({
  formData,
  cavi,
  responsabili,
  strumenti,
  validationErrors,
  isCavoLocked,
  onInputChange
}: InformazioniBaseProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Cavo */}
      <div className="space-y-1">
        <Label htmlFor="id_cavo" className="text-xs font-medium text-gray-700">Cavo *</Label>
        <Select
          value={formData.id_cavo}
          onValueChange={(value) => onInputChange('id_cavo', value)}
          disabled={isCavoLocked}
        >
          <SelectTrigger className={`h-11 text-sm ${validationErrors.id_cavo ? 'border-red-500' : 'border-gray-300'}`}>
            <SelectValue placeholder="Seleziona cavo..." />
          </SelectTrigger>
          <SelectContent>
            {cavi.map((cavo) => (
              <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>
                <div className="flex flex-col py-1">
                  <span className="font-medium text-sm">{cavo.id_cavo}</span>
                  <span className="text-xs text-gray-500">{cavo.tipologia} {cavo.sezione}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {validationErrors.id_cavo && (
          <p className="text-sm text-red-600">{validationErrors.id_cavo}</p>
        )}
      </div>

      {/* Operatore */}
      <div className="space-y-1">
        <Label htmlFor="id_operatore" className="text-xs font-medium text-gray-700">Operatore</Label>
        <Select
          value={formData.id_operatore?.toString() || ''}
          onValueChange={(value) => onInputChange('id_operatore', parseInt(value))}
        >
          <SelectTrigger className="h-11 text-sm border-gray-300">
            <SelectValue placeholder="Seleziona operatore..." />
          </SelectTrigger>
          <SelectContent>
            {responsabili.map((resp) => (
              <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>
                <span className="text-sm">{resp.nome_responsabile}</span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Strumento */}
      <div className="space-y-1">
        <Label htmlFor="id_strumento" className="text-xs font-medium text-gray-700">Strumento di Misura</Label>
        <Select
          value={formData.id_strumento?.toString() || ''}
          onValueChange={(value) => {
            const strumento = strumenti.find(s => s.id_strumento === parseInt(value))
            onInputChange('id_strumento', parseInt(value))
            if (strumento) {
              onInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)
            }
          }}
        >
          <SelectTrigger className="h-11 text-sm border-gray-300">
            <SelectValue placeholder="Seleziona strumento..." />
          </SelectTrigger>
          <SelectContent>
            {strumenti.map((strumento) => (
              <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>
                <div className="flex flex-col py-1">
                  <span className="font-medium text-sm">{strumento.nome}</span>
                  <span className="text-xs text-gray-500">{strumento.marca} {strumento.modello}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Tipo Certificato */}
      <div className="space-y-1">
        <Label htmlFor="tipo_certificato" className="text-xs font-medium text-gray-700">Tipo Certificato</Label>
        <Select
          value={formData.tipo_certificato || 'SINGOLO'}
          onValueChange={(value) => onInputChange('tipo_certificato', value)}
        >
          <SelectTrigger className="h-11 text-sm border-gray-300">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="SINGOLO">
              <span className="text-sm">🔍 Singolo</span>
            </SelectItem>
            <SelectItem value="GRUPPO">
              <span className="text-sm">📊 Gruppo</span>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
