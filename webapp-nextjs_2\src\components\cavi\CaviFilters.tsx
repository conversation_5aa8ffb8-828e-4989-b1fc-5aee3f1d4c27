'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { 
  Filter, 
  ChevronDown, 
  ChevronUp, 
  X,
  Search,
  Cable,
  Activity,
  Zap,
  CheckCircle
} from 'lucide-react'
import { Cavo } from '@/types'

interface FilterState {
  search: string
  tipologia: string[]
  sistema: string[]
  utility: string[]
  formazione: string[]
  statoInstallazione: string[]
  collegamento: string[]
  certificazione: string[]
  bobina: string[]
}

interface CaviFiltersProps {
  cavi: Cavo[]
  onFilterChange: (filteredCavi: Cavo[]) => void
  onSelectionToggle?: () => void
  selectionEnabled?: boolean
}

export default function CaviFilters({
  cavi,
  onFilterChange,
  onSelectionToggle,
  selectionEnabled = false
}: CaviFiltersProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    tipologia: [],
    sistema: [],
    utility: [],
    formazione: [],
    statoInstallazione: [],
    collegamento: [],
    certificazione: [],
    bobina: []
  })

  // Estrai valori unici per i filtri
  const getUniqueValues = (field: keyof Cavo) => {
    return [...new Set(cavi.map(c => c[field]).filter(Boolean))].sort()
  }

  const tipologie = getUniqueValues('tipologia')
  const sistemi = getUniqueValues('sistema')
  const utilities = getUniqueValues('utility')
  const formazioni = getUniqueValues('formazione')
  const bobine = getUniqueValues('id_bobina')

  // Applica filtri
  const applyFilters = (newFilters: FilterState) => {
    let filtered = cavi

    // Filtro ricerca
    if (newFilters.search) {
      const search = newFilters.search.toLowerCase()
      filtered = filtered.filter(cavo =>
        cavo.id_cavo?.toString().toLowerCase().includes(search) ||
        cavo.sistema?.toLowerCase().includes(search) ||
        cavo.utility?.toLowerCase().includes(search) ||
        cavo.tipologia?.toLowerCase().includes(search) ||
        cavo.formazione?.toLowerCase().includes(search) ||
        cavo.da?.toLowerCase().includes(search) ||
        cavo.a?.toLowerCase().includes(search)
      )
    }

    // Filtri multipli
    if (newFilters.tipologia.length > 0) {
      filtered = filtered.filter(c => newFilters.tipologia.includes(c.tipologia || ''))
    }

    if (newFilters.sistema.length > 0) {
      filtered = filtered.filter(c => newFilters.sistema.includes(c.sistema || ''))
    }

    if (newFilters.utility.length > 0) {
      filtered = filtered.filter(c => newFilters.utility.includes(c.utility || ''))
    }

    if (newFilters.formazione.length > 0) {
      filtered = filtered.filter(c => newFilters.formazione.includes(c.formazione || ''))
    }

    if (newFilters.bobina.length > 0) {
      filtered = filtered.filter(c => newFilters.bobina.includes(c.id_bobina || ''))
    }

    // Filtro stato installazione
    if (newFilters.statoInstallazione.length > 0) {
      filtered = filtered.filter(cavo => {
        const isInstalled = cavo.metri_posati > 0
        return newFilters.statoInstallazione.some(stato => {
          if (stato === 'installato' && isInstalled) return true
          if (stato === 'non_installato' && !isInstalled) return true
          return false
        })
      })
    }

    // Filtro collegamento
    if (newFilters.collegamento.length > 0) {
      filtered = filtered.filter(cavo => {
        return newFilters.collegamento.some(stato => {
          if (stato === 'collegato' && cavo.collegamento === 3) return true
          if (stato === 'partenza' && cavo.collegamento === 1) return true
          if (stato === 'arrivo' && cavo.collegamento === 2) return true
          if (stato === 'non_collegato' && (!cavo.collegamento || cavo.collegamento === 0)) return true
          return false
        })
      })
    }

    // Filtro certificazione
    if (newFilters.certificazione.length > 0) {
      filtered = filtered.filter(cavo => {
        return newFilters.certificazione.some(stato => {
          if (stato === 'certificato' && cavo.certificato) return true
          if (stato === 'non_certificato' && !cavo.certificato) return true
          return false
        })
      })
    }

    onFilterChange(filtered)
  }

  const updateFilter = (key: keyof FilterState, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    applyFilters(newFilters)
  }

  const toggleArrayFilter = (key: keyof FilterState, value: string) => {
    const currentArray = filters[key] as string[]
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    updateFilter(key, newArray)
  }

  const clearFilters = () => {
    const emptyFilters: FilterState = {
      search: '',
      tipologia: [],
      sistema: [],
      utility: [],
      formazione: [],
      statoInstallazione: [],
      collegamento: [],
      certificazione: [],
      bobina: []
    }
    setFilters(emptyFilters)
    applyFilters(emptyFilters)
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    count += filters.tipologia.length
    count += filters.sistema.length
    count += filters.utility.length
    count += filters.formazione.length
    count += filters.statoInstallazione.length
    count += filters.collegamento.length
    count += filters.certificazione.length
    count += filters.bobina.length
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <CardTitle className="text-base">Filtri</CardTitle>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary">{activeFiltersCount}</Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {selectionEnabled !== undefined && (
              <Button
                variant="outline"
                size="sm"
                onClick={onSelectionToggle}
              >
                {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}
              </Button>
            )}
            
            {activeFiltersCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
              >
                <X className="h-3 w-3 mr-1" />
                Pulisci
              </Button>
            )}
            
            <Collapsible open={isOpen} onOpenChange={setIsOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          </div>
        </div>

        {/* Ricerca sempre visibile */}
        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Cerca cavi..."
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="flex-1"
          />
        </div>
      </CardHeader>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleContent>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              
              {/* Tipologia */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Tipologia</Label>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {tipologie.map(tip => (
                    <div key={tip} className="flex items-center space-x-2">
                      <Checkbox
                        id={`tip-${tip}`}
                        checked={filters.tipologia.includes(tip)}
                        onCheckedChange={() => toggleArrayFilter('tipologia', tip)}
                      />
                      <Label htmlFor={`tip-${tip}`} className="text-xs">{tip}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Sistema */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Sistema</Label>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {sistemi.map(sis => (
                    <div key={sis} className="flex items-center space-x-2">
                      <Checkbox
                        id={`sis-${sis}`}
                        checked={filters.sistema.includes(sis)}
                        onCheckedChange={() => toggleArrayFilter('sistema', sis)}
                      />
                      <Label htmlFor={`sis-${sis}`} className="text-xs">{sis}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Utility */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Utility</Label>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {utilities.map(util => (
                    <div key={util} className="flex items-center space-x-2">
                      <Checkbox
                        id={`util-${util}`}
                        checked={filters.utility.includes(util)}
                        onCheckedChange={() => toggleArrayFilter('utility', util)}
                      />
                      <Label htmlFor={`util-${util}`} className="text-xs">{util}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Stato Installazione */}
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center">
                  <Activity className="h-3 w-3 mr-1" />
                  Installazione
                </Label>
                <div className="space-y-1">
                  {[
                    { value: 'installato', label: 'Installato' },
                    { value: 'non_installato', label: 'Da Installare' }
                  ].map(stato => (
                    <div key={stato.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`inst-${stato.value}`}
                        checked={filters.statoInstallazione.includes(stato.value)}
                        onCheckedChange={() => toggleArrayFilter('statoInstallazione', stato.value)}
                      />
                      <Label htmlFor={`inst-${stato.value}`} className="text-xs">{stato.label}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Collegamento */}
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center">
                  <Zap className="h-3 w-3 mr-1" />
                  Collegamento
                </Label>
                <div className="space-y-1">
                  {[
                    { value: 'collegato', label: 'Collegato' },
                    { value: 'partenza', label: 'Lato Partenza' },
                    { value: 'arrivo', label: 'Lato Arrivo' },
                    { value: 'non_collegato', label: 'Non Collegato' }
                  ].map(stato => (
                    <div key={stato.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`coll-${stato.value}`}
                        checked={filters.collegamento.includes(stato.value)}
                        onCheckedChange={() => toggleArrayFilter('collegamento', stato.value)}
                      />
                      <Label htmlFor={`coll-${stato.value}`} className="text-xs">{stato.label}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Certificazione */}
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Certificazione
                </Label>
                <div className="space-y-1">
                  {[
                    { value: 'certificato', label: 'Certificato' },
                    { value: 'non_certificato', label: 'Non Certificato' }
                  ].map(stato => (
                    <div key={stato.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`cert-${stato.value}`}
                        checked={filters.certificazione.includes(stato.value)}
                        onCheckedChange={() => toggleArrayFilter('certificazione', stato.value)}
                      />
                      <Label htmlFor={`cert-${stato.value}`} className="text-xs">{stato.label}</Label>
                    </div>
                  ))}
                </div>
              </div>

            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
