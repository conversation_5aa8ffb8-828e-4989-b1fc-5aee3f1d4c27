'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON>, Check<PERSON><PERSON>cle, Trash2, Alert<PERSON>riangle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SimpleActionsProps {
  user: {
    id_utente: number
    ruolo: string
    abilitato: boolean
    username: string
  }
  onEdit: () => void
  onToggleStatus: () => void
  onDelete: () => void
}

export default function SimpleActions({
  user,
  onEdit,
  onToggleStatus,
  onDelete
}: SimpleActionsProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onEdit()
  }

  const handleToggleStatus = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Conferma per disabilitazione
    if (user.abilitato) {
      const confirmed = window.confirm(
        `Sei sicuro di voler disabilitare l'utente "${user.username}"?\n\nL'utente non potrà più accedere al sistema.`
      )
      if (!confirmed) return
    }

    onToggleStatus()
  }

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setShowDeleteConfirm(true)
  }

  const handleDeleteConfirm = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setShowDeleteConfirm(false)
    onDelete()
  }

  const handleDeleteCancel = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setShowDeleteConfirm(false)
  }

  if (showDeleteConfirm) {
    return (
      <div className="flex items-center gap-1 bg-red-50 border border-red-200 rounded-md p-2">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <span className="text-xs text-red-700 font-medium">Eliminare?</span>
        </div>
        <div className="flex items-center gap-1 ml-2">
          <Button
            size="sm"
            variant="destructive"
            onClick={handleDeleteConfirm}
            className="h-6 px-2 text-xs"
          >
            Sì
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleDeleteCancel}
            className="h-6 px-2 text-xs"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-1">
      {/* Modifica - sempre disponibile */}
      <div className="relative group">
        <button
          onClick={handleEdit}
          type="button"
          className="p-1.5 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
          aria-label={`Modifica utente ${user.username}`}
        >
          <Edit className="h-4 w-4 text-blue-600 hover:text-blue-700" />
        </button>
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          Modifica utente
        </div>
      </div>

      {/* Abilita/Disabilita */}
      <div className="relative group">
        <button
          onClick={handleToggleStatus}
          disabled={user.ruolo === 'owner'}
          type="button"
          className={`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${
            user.ruolo === 'owner'
              ? 'opacity-50 cursor-not-allowed'
              : 'hover:scale-105 hover:bg-slate-50 focus:ring-slate-500'
          }`}
          aria-label={user.abilitato ? `Disabilita utente ${user.username}` : `Abilita utente ${user.username}`}
        >
          {user.abilitato ? (
            <Clock className="h-4 w-4 text-red-500 hover:text-red-600" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-500 hover:text-green-600" />
          )}
        </button>
        {user.ruolo !== 'owner' && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
            {user.abilitato ? 'Disabilita utente' : 'Abilita utente'}
          </div>
        )}
      </div>

      {/* Elimina */}
      <div className="relative group">
        <button
          onClick={handleDeleteClick}
          disabled={user.ruolo === 'owner'}
          type="button"
          className={`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${
            user.ruolo === 'owner'
              ? 'opacity-50 cursor-not-allowed'
              : 'hover:scale-105 hover:bg-red-50 focus:ring-red-500'
          }`}
          aria-label={`Elimina utente ${user.username}`}
        >
          <Trash2 className="h-4 w-4 text-red-500 hover:text-red-600" />
        </button>
        {user.ruolo !== 'owner' && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
            Elimina utente
          </div>
        )}
      </div>
    </div>
  )
}
