'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function Home() {
  const { user, cantiere, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    console.log('🏠 HomePage: Stato corrente:', {
      isLoading,
      isAuthenticated,
      user: user ? { id: user.id_utente, ruolo: user.ruolo } : null,
      cantiere: cantiere ? { id: cantiere.id_cantiere } : null
    })

    // ASPETTA che il caricamento sia completato
    if (isLoading) {
      console.log('🏠 HomePage: Ancora in caricamento, attendo...')
      return
    }

    // Se NON autenticato, reindirizza al login
    if (!isAuthenticated) {
      console.log('🏠 HomePage: Utente NON autenticato, reindirizzamento a /login')
      router.replace('/login')
      return
    }

    // Se autenticato, reindirizza in base al ruolo SOLO se abbiamo dati validi
    if (isAuthenticated && user) {
      console.log('🏠 HomePage: Utente autenticato, reindirizzamento in base al ruolo:', user.ruolo)

      if (user.ruolo === 'owner') {
        console.log('🏠 HomePage: Reindirizzamento admin a /admin')
        router.replace('/admin')
      } else if (user.ruolo === 'user') {
        console.log('🏠 HomePage: Reindirizzamento utente standard a /cantieri')
        router.replace('/cantieri')
      } else if (user.ruolo === 'cantieri_user') {
        console.log('🏠 HomePage: Reindirizzamento utente cantiere a /cavi')
        router.replace('/cavi')
      } else {
        console.log('🏠 HomePage: Ruolo sconosciuto, reindirizzamento a /login')
        router.replace('/login')
      }
    } else if (isAuthenticated && cantiere && !user) {
      // Login cantiere senza utente
      console.log('🏠 HomePage: Login cantiere rilevato, reindirizzamento a /cavi')
      router.replace('/cavi')
    } else if (isAuthenticated && !user && !cantiere) {
      // Stato inconsistente - autenticato ma senza dati
      console.error('🏠 HomePage: Stato inconsistente - autenticato ma senza user/cantiere')
      router.replace('/login')
    }
  }, [isAuthenticated, isLoading, user, cantiere, router])

  // Mostra un indicatore di caricamento durante il reindirizzamento
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          CABLYS
        </h1>
        <p className="text-slate-600 mb-6">
          {isLoading ? 'Verifica autenticazione...' : 'Reindirizzamento in corso...'}
        </p>
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
      </div>
    </div>
  )
}
