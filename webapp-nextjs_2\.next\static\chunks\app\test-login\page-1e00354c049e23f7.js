(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9483],{9025:(e,s,t)=>{Promise.resolve().then(t.bind(t,20185))},20185:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var n=t(95155),a=t(12115),o=t(40283);function r(){let{login:e,loginCantiere:s,user:t,cantiere:r,isLoading:c}=(0,o.A)(),[i,l]=(0,a.useState)(""),[d,g]=(0,a.useState)(!1),u=async()=>{g(!0),l("");try{console.log("Testing user login...");let s=await e("admin","admin");console.log("Login result:",s),s.success&&s.user?l("SUCCESS: Login riuscito per ".concat(s.user.username," (").concat(s.user.ruolo,")")):l("ERROR: ".concat(s.error||"Login fallito"))}catch(e){console.error("Login test error:",e),l("EXCEPTION: ".concat(e.message))}finally{g(!1)}},p=async()=>{g(!0),l("");try{console.log("Testing cantiere login...");let e=await s("TEST123","test123");console.log("Cantiere login result:",e),e.success&&e.cantiere?l("SUCCESS: Login cantiere riuscito per ".concat(e.cantiere.commessa)):l("ERROR: ".concat(e.error||"Login cantiere fallito"))}catch(e){console.error("Cantiere login test error:",e),l("EXCEPTION: ".concat(e.message))}finally{g(!1)}},h=async()=>{g(!0),l("");try{console.log("Testing direct API call...");let e=new FormData;e.append("username","admin"),e.append("password","admin");let s=await fetch("http://localhost:8001/api/auth/login",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:e});console.log("Response status:",s.status);let t=await s.json();console.log("Response data:",t),s.ok?l("SUCCESS: API diretta funzionante - Token: ".concat(t.access_token.substring(0,50),"...")):l("ERROR: ".concat(s.status," - ").concat(JSON.stringify(t,null,2)))}catch(e){console.error("Direct API test error:",e),l("EXCEPTION: ".concat(e.message))}finally{g(!1)}};return(0,n.jsx)("div",{className:"min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-md p-6",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-center mb-6",children:"Test Login Sistema"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,n.jsx)("p",{children:(0,n.jsx)("strong",{children:"Stato Auth:"})}),(0,n.jsxs)("p",{children:["Loading: ",c?"S\xec":"No"]}),(0,n.jsxs)("p",{children:["User: ",t?"".concat(t.username," (").concat(t.ruolo,")"):"Nessuno"]}),(0,n.jsxs)("p",{children:["Cantiere: ",r?r.commessa:"Nessuno"]})]}),(0,n.jsx)("hr",{}),(0,n.jsx)("button",{onClick:u,disabled:d,className:"w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50",children:d?"Testing...":"Test Login Utente (admin/admin)"}),(0,n.jsx)("button",{onClick:p,disabled:d,className:"w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50",children:d?"Testing...":"Test Login Cantiere (TEST123/test123)"}),(0,n.jsx)("button",{onClick:h,disabled:d,className:"w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50",children:d?"Testing...":"Test API Diretta"}),i&&(0,n.jsx)("div",{className:"p-4 rounded ".concat(i.startsWith("SUCCESS")?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:(0,n.jsx)("pre",{className:"whitespace-pre-wrap text-sm",children:i})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3464,283,8441,1684,7358],()=>s(9025)),_N_E=e.O()}]);