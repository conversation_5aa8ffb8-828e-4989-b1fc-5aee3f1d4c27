{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        // Reindirizza automaticamente in base al ruolo dell'utente come nel sistema React originale\n        // Breve timeout per evitare reindirizzamenti troppo rapidi\n        const redirectTimer = setTimeout(() => {\n          if (user?.ruolo === 'owner') {\n            router.replace('/admin')\n          } else if (user?.ruolo === 'user') {\n            router.replace('/cantieri')\n          } else if (user?.ruolo === 'cantieri_user') {\n            router.replace('/cavi')\n          } else {\n            router.replace('/cantieri')\n          }\n        }, 300)\n\n        return () => clearTimeout(redirectTimer)\n      } else {\n        // Se non autenticato, reindirizza al login\n        router.replace('/login')\n      }\n    }\n  }, [isAuthenticated, isLoading, user, router])\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento (come nel React originale)\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold text-slate-900 mb-4\">\n          Benvenuto nel Sistema di Gestione Cantieri\n        </h1>\n        <p className=\"text-slate-600 mb-6\">\n          Reindirizzamento in corso...\n        </p>\n        <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto\"></div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,iBAAiB;oBACnB,4FAA4F;oBAC5F,2DAA2D;oBAC3D,MAAM,gBAAgB;wDAAW;4BAC/B,IAAI,MAAM,UAAU,SAAS;gCAC3B,OAAO,OAAO,CAAC;4BACjB,OAAO,IAAI,MAAM,UAAU,QAAQ;gCACjC,OAAO,OAAO,CAAC;4BACjB,OAAO,IAAI,MAAM,UAAU,iBAAiB;gCAC1C,OAAO,OAAO,CAAC;4BACjB,OAAO;gCACL,OAAO,OAAO,CAAC;4BACjB;wBACF;uDAAG;oBAEH;0CAAO,IAAM,aAAa;;gBAC5B,OAAO;oBACL,2CAA2C;oBAC3C,OAAO,OAAO,CAAC;gBACjB;YACF;QACF;yBAAG;QAAC;QAAiB;QAAW;QAAM;KAAO;IAE7C,6FAA6F;IAC7F,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;8BAGnC,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;GA3CwB;;QACuB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}