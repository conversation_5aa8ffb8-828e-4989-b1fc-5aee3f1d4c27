(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6222],{30285:(e,a,s)=>{"use strict";s.d(a,{$:()=>c});var t=s(95155);s(12115);var r=s(99708),n=s(74466),i=s(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:a,variant:s,size:n,asChild:c=!1,...l}=e,d=c?r.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:n,className:a})),...l})}},42712:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>_});var t=s(95155),r=s(12115),n=s(35695),i=s(66695),o=s(30285),c=s(62523),l=s(85127),d=s(54165),m=s(85057),u=s(40283),x=s(25731),g=s(51154),h=s(47924),p=s(84616),v=s(13717),f=s(69803),b=s(24357),j=s(78749),w=s(92657),N=s(72713),y=s(92138);function _(){let{user:e,isAuthenticated:a,isLoading:s,selectCantiere:_}=(0,u.A)(),z=(0,n.useRouter)(),[C,k]=(0,r.useState)([]),[A,P]=(0,r.useState)(!0),[E,S]=(0,r.useState)(""),[F,I]=(0,r.useState)(""),[J,L]=(0,r.useState)({}),[$,M]=(0,r.useState)(!1),[D,O]=(0,r.useState)({}),[B,T]=(0,r.useState)({}),[G,Z]=(0,r.useState)(!1),[H,X]=(0,r.useState)(!1),[R,V]=(0,r.useState)(!1),[Q,U]=(0,r.useState)(null),[W,Y]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[q,K]=(0,r.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[ee,ea]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s||a||z.push("/login")},[a,s,z]),(0,r.useEffect)(()=>{a&&es()},[a]);let es=async()=>{try{P(!0);let e=await x._I.getCantieri();k(e),await et(e)}catch(e){S("Errore nel caricamento dei cantieri")}finally{P(!1)}},et=async e=>{try{M(!0);let a=e.map(async e=>{try{let a=await x._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:a}}catch(a){return console.error("Errore nel caricamento statistiche cantiere ".concat(e.id_cantiere,":"),a),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),s=(await Promise.all(a)).reduce((e,a)=>{let{id:s,stats:t}=a;return e[s]=t,e},{});L(s)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{M(!1)}},er=async e=>{let a=e.id_cantiere;if(D[a])O(e=>({...e,[a]:!1})),T(e=>({...e,[a]:""}));else if(B[a])O(e=>({...e,[a]:!0}));else try{ea(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),s=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(a,"/view-password"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!s.ok){let e=await s.json();throw Error(e.detail||"Errore nel recupero password")}let t=await s.json();T(e=>({...e,[a]:t.password_cantiere})),O(e=>({...e,[a]:!0}))}catch(e){S(e instanceof Error?e.message:"Errore nel recupero password")}finally{ea(!1)}},en=e=>{_(e),z.push("/cantieri/".concat(e.id_cantiere))},ei=e=>{U(e),K({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),X(!0)},eo=async()=>{try{await x._I.createCantiere(q),Z(!1),K({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"Italia",password_cantiere:"",codice_univoco:""}),es()}catch(e){S("Errore nella creazione del cantiere")}},ec=async()=>{if(Q)try{await x._I.updateCantiere(Q.id_cantiere,q),X(!1),U(null),es()}catch(e){S("Errore nella modifica del cantiere")}},el=async()=>{if(Q){if(W.newPassword!==W.confirmPassword)return void S("Le password non coincidono");if(!W.currentPassword)return void S("Inserisci la password attuale per confermare il cambio");if(!W.newPassword||W.newPassword.length<6)return void S("La nuova password deve essere di almeno 6 caratteri");try{P(!0),S("");let e=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(Q.id_cantiere,"/change-password"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({password_attuale:W.currentPassword,password_nuova:W.newPassword,conferma_password:W.confirmPassword})});if(!e.ok){let a=await e.json();throw Error(a.detail||"Errore nel cambio password")}let a=await e.json();if(a.success)Y({currentPassword:"",newPassword:"",confirmPassword:""}),V(!1),S(""),alert(a.message||"Password cambiata con successo");else throw Error(a.message||"Errore nel cambio password")}catch(e){S(e instanceof Error?e.message:"Errore nel cambio password")}finally{P(!1)}}},ed=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Failed to copy to clipboard:",e)}},em=C.filter(e=>{var a,s;return e.commessa.toLowerCase().includes(F.toLowerCase())||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(F.toLowerCase()))||(null==(s=e.nome_cliente)?void 0:s.toLowerCase().includes(F.toLowerCase()))});return s?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)(g.A,{className:"h-8 w-8 animate-spin"})}):(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{className:"relative w-80",children:[(0,t.jsx)(h.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(c.p,{placeholder:"Cerca cantieri...",value:F,onChange:e=>I(e.target.value),className:"pl-8 w-full"})]})}),(0,t.jsxs)(d.lG,{open:G,onOpenChange:Z,children:[(0,t.jsx)(d.zM,{asChild:!0,children:(0,t.jsxs)(o.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25",children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,t.jsxs)(d.Cf,{className:"sm:max-w-[425px]",children:[(0,t.jsxs)(d.c7,{children:[(0,t.jsx)(d.L3,{children:"Crea Nuovo Cantiere"}),(0,t.jsx)(d.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"commessa",className:"text-right",children:"Commessa *"}),(0,t.jsx)(c.p,{id:"commessa",value:q.commessa,onChange:e=>K({...q,commessa:e.target.value}),className:"col-span-3",placeholder:"Nome commessa"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,t.jsx)(c.p,{id:"descrizione",value:q.descrizione,onChange:e=>K({...q,descrizione:e.target.value}),className:"col-span-3",placeholder:"Descrizione cantiere"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,t.jsx)(c.p,{id:"nome_cliente",value:q.nome_cliente,onChange:e=>K({...q,nome_cliente:e.target.value}),className:"col-span-3",placeholder:"Nome cliente"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,t.jsx)(c.p,{id:"indirizzo_cantiere",value:q.indirizzo_cantiere,onChange:e=>K({...q,indirizzo_cantiere:e.target.value}),className:"col-span-3",placeholder:"Indirizzo cantiere"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,t.jsx)(c.p,{id:"citta_cantiere",value:q.citta_cantiere,onChange:e=>K({...q,citta_cantiere:e.target.value}),className:"col-span-3",placeholder:"Citt\xe0"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,t.jsx)(c.p,{id:"password_cantiere",type:"password",value:q.password_cantiere,onChange:e=>K({...q,password_cantiere:e.target.value}),className:"col-span-3",placeholder:"Password cantiere"})]})]}),(0,t.jsxs)(d.Es,{children:[(0,t.jsx)(o.$,{variant:"outline",onClick:()=>Z(!1),children:"Annulla"}),(0,t.jsx)(o.$,{onClick:eo,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25",children:"Crea Cantiere"})]})]})]}),(0,t.jsx)(d.lG,{open:H,onOpenChange:e=>{X(e),e||(U(null),K({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"Italia",password_cantiere:"",codice_univoco:""}))},children:(0,t.jsxs)(d.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(d.c7,{children:[(0,t.jsxs)(d.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5"}),"Modifica Cantiere - ",null==Q?void 0:Q.commessa]}),(0,t.jsx)(d.rr,{children:"Modifica i dati del cantiere selezionato"})]}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,t.jsx)("input",{id:"edit-commessa",value:q.commessa,onChange:e=>K({...q,commessa:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,t.jsx)("input",{id:"edit-descrizione",value:q.descrizione,onChange:e=>K({...q,descrizione:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"edit-cliente",className:"text-right",children:"Cliente"}),(0,t.jsx)("input",{id:"edit-cliente",value:q.nome_cliente,onChange:e=>K({...q,nome_cliente:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"edit-indirizzo",className:"text-right",children:"Indirizzo"}),(0,t.jsx)("input",{id:"edit-indirizzo",value:q.indirizzo_cantiere,onChange:e=>K({...q,indirizzo_cantiere:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"edit-citta",className:"text-right",children:"Citt\xe0"}),(0,t.jsx)("input",{id:"edit-citta",value:q.citta_cantiere,onChange:e=>K({...q,citta_cantiere:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{className:"text-right",children:"Password"}),(0,t.jsx)("div",{className:"col-span-3",children:(0,t.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{V(!0)},className:"w-full border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300",children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})})]})]}),(0,t.jsxs)(d.Es,{children:[(0,t.jsx)(o.$,{variant:"outline",onClick:()=>X(!1),children:"Annulla"}),(0,t.jsx)(o.$,{onClick:ec,className:"bg-blue-600 hover:bg-blue-700",children:"Salva Modifiche"})]})]})}),(0,t.jsx)(d.lG,{open:R,onOpenChange:e=>{V(e),e||(Y({currentPassword:"",newPassword:"",confirmPassword:""}),S(""))},children:(0,t.jsxs)(d.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(d.c7,{children:[(0,t.jsxs)(d.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"Cambia Password - ",null==Q?void 0:Q.commessa]}),(0,t.jsx)(d.rr,{children:"Inserisci la password attuale e la nuova password per il cantiere"})]}),E&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:E}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"current-password",className:"text-right",children:"Password Attuale"}),(0,t.jsx)("input",{id:"current-password",type:"password",value:W.currentPassword,onChange:e=>Y({...W,currentPassword:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Inserisci password attuale"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"new-password",className:"text-right",children:"Nuova Password"}),(0,t.jsx)("input",{id:"new-password",type:"password",value:W.newPassword,onChange:e=>Y({...W,newPassword:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Inserisci nuova password"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(m.J,{htmlFor:"confirm-password",className:"text-right",children:"Conferma Password"}),(0,t.jsx)("input",{id:"confirm-password",type:"password",value:W.confirmPassword,onChange:e=>Y({...W,confirmPassword:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Conferma nuova password"})]})]}),(0,t.jsxs)(d.Es,{children:[(0,t.jsx)(o.$,{variant:"outline",onClick:()=>V(!1),children:"Annulla"}),(0,t.jsx)(o.$,{onClick:el,disabled:A||!W.currentPassword||!W.newPassword,className:"bg-orange-600 hover:bg-orange-700",children:A?"Cambiando...":"Cambia Password"})]})]})})]}),E&&(0,t.jsx)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,t.jsx)("p",{className:"text-red-700",children:E})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Zp,{children:(0,t.jsxs)(l.XI,{children:[(0,t.jsx)(l.A0,{children:(0,t.jsxs)(l.Hj,{className:"border-b border-gray-200",children:[(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700 w-32",children:"Avanzamento"}),(0,t.jsx)(l.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,t.jsx)(l.nd,{className:"text-center font-semibold text-gray-700 w-48",children:"Azioni"})]})}),(0,t.jsx)(l.BF,{children:em.map(e=>{var a,s,r,n,i,c,d,m,u,x;return(0,t.jsxs)(l.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,t.jsx)(l.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,t.jsx)(l.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,t.jsx)(l.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,t.jsx)(l.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,t.jsx)(l.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,t.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>ed(e.codice_univoco),children:(0,t.jsx)(b.A,{className:"h-3 w-3"})})]})}),(0,t.jsx)(l.nA,{className:"py-4",children:(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsx)("div",{className:"flex items-center gap-2",children:D[e.id_cantiere]&&B[e.id_cantiere]?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:B[e.id_cantiere]}),(0,t.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>er(e),children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,t.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>er(e),disabled:ee,children:ee?(0,t.jsx)(g.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})})})}),(0,t.jsx)(l.nA,{className:"py-4",children:(0,t.jsx)("div",{className:"flex items-center gap-2",children:$?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,t.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out shadow-sm ".concat(((null==(a=J[e.id_cantiere])?void 0:a.percentuale_avanzamento)||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":((null==(s=J[e.id_cantiere])?void 0:s.percentuale_avanzamento)||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":((null==(r=J[e.id_cantiere])?void 0:r.percentuale_avanzamento)||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":((null==(n=J[e.id_cantiere])?void 0:n.percentuale_avanzamento)||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"),style:{width:"".concat(Math.min((null==(i=J[e.id_cantiere])?void 0:i.percentuale_avanzamento)||0,100),"%")}})})})})})}),(0,t.jsx)(l.nA,{className:"py-4 text-center",children:$?(0,t.jsx)(g.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsxs)("span",{className:"text-sm font-semibold ".concat(((null==(c=J[e.id_cantiere])?void 0:c.percentuale_avanzamento)||0)>=90?"text-green-700":((null==(d=J[e.id_cantiere])?void 0:d.percentuale_avanzamento)||0)>=75?"text-blue-700":((null==(m=J[e.id_cantiere])?void 0:m.percentuale_avanzamento)||0)>=50?"text-yellow-700":((null==(u=J[e.id_cantiere])?void 0:u.percentuale_avanzamento)||0)>=25?"text-orange-700":"text-red-700"),children:[((null==(x=J[e.id_cantiere])?void 0:x.percentuale_avanzamento)||0).toFixed(1),"%"]})]})}),(0,t.jsx)(l.nA,{className:"text-center py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>ei(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,t.jsxs)(o.$,{size:"sm",onClick:()=>en(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out",title:"Accedi al cantiere",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere)})})]})})})]})}},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>x,L3:()=>g,c7:()=>u,lG:()=>o,rr:()=>h,zM:()=>c});var t=s(95155);s(12115);var r=s(15452),n=s(54416),i=s(59434);function o(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...a})}function c(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function l(e){let{...a}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function m(e){let{className:a,children:s,showCloseButton:o=!0,...c}=e;return(0,t.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...c,children:[s,o&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function x(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function g(e){let{className:a,...s}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...s})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...s})}},59434:(e,a,s)=>{"use strict";s.d(a,{cn:()=>n});var t=s(52596),r=s(39688);function n(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,r.QP)((0,t.$)(a))}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>n});var t=s(95155);s(12115);var r=s(59434);function n(e){let{className:a,type:s,...n}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...n})}},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>c,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i});var t=s(95155);s(12115);var r=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...s})}function i(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...s})}},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>i});var t=s(95155);s(12115);var r=s(40968),n=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>i,BF:()=>o,Hj:()=>c,XI:()=>n,nA:()=>d,nd:()=>l});var t=s(95155);s(12115);var r=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",a),...s})})}function i(e){let{className:a,...s}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}},92524:(e,a,s)=>{Promise.resolve().then(s.bind(s,42712))}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3455,5585,2419,283,8441,1684,7358],()=>a(92524)),_N_E=e.O()}]);