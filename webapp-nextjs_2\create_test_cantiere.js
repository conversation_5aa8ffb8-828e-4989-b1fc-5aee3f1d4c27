const axios = require('axios');

async function createTestCantiere() {
  try {
    console.log('🔧 Creazione cantiere di test...');

    // 1. Login come admin per creare utente
    console.log('1. Login admin...');
    const loginResponse = await axios.post('http://localhost:8001/api/auth/login',
      'username=admin&password=admin',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    const adminToken = loginResponse.data.access_token;
    console.log('✅ Login admin riuscito');

    // 2. Crea utente standard se non esiste
    console.log('2. Creazione utente standard...');
    try {
      const userData = {
        username: "testuser2",
        password: "test123",
        ruolo: "user",
        email: "<EMAIL>",
        data_scadenza: "2025-12-31"
      };

      await axios.post(
        'http://localhost:8001/api/users',
        userData,
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('✅ Utente standard creato');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('ℹ️ Utente standard già esistente');
      } else {
        throw error;
      }
    }

    // 3. Login come utente standard
    console.log('3. Login utente standard...');
    const userLoginResponse = await axios.post('http://localhost:8001/api/auth/login',
      'username=testuser2&password=test123',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    const userToken = userLoginResponse.data.access_token;
    console.log('✅ Login utente standard riuscito');
    
    // 4. Crea cantiere di test
    console.log('4. Creazione cantiere...');
    const cantiereData = {
      commessa: "Cantiere Test Login",
      descrizione: "Cantiere di test per verificare il sistema di login",
      nome_cliente: "Cliente Test",
      indirizzo_cantiere: "Via Test 123",
      citta_cantiere: "Test City",
      nazione_cantiere: "Italia",
      riferimenti_normativi: "CEI 64-8",
      documentazione_progetto: "Progetto test",
      password_cantiere: "test123"
    };
    
    const cantiereResponse = await axios.post(
      'http://localhost:8001/api/cantieri',
      cantiereData,
      {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Cantiere creato con successo!');
    console.log('📋 Dettagli cantiere:');
    console.log(`   ID: ${cantiereResponse.data.id_cantiere}`);
    console.log(`   Commessa: ${cantiereResponse.data.commessa}`);
    console.log(`   Codice Univoco: ${cantiereResponse.data.codice_univoco}`);
    console.log(`   Password: test123`);
    
    // 5. Test login cantiere
    console.log('\n5. Test login cantiere...');
    const loginCantiereResponse = await axios.post(
      'http://localhost:8001/api/auth/login/cantiere',
      {
        codice_univoco: cantiereResponse.data.codice_univoco,
        password: "test123"
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Login cantiere riuscito!');
    console.log('🎯 Credenziali di test per il frontend:');
    console.log(`   Codice Cantiere: ${cantiereResponse.data.codice_univoco}`);
    console.log(`   Password Cantiere: test123`);
    
  } catch (error) {
    console.error('❌ Errore:', error.response?.data || error.message);
  }
}

createTestCantiere();
