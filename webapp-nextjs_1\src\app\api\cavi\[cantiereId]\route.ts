import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ cantiereId: string }> }
) {
  try {
    const { cantiereId } = await params

    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        {
          detail: 'Token di autorizzazione mancante'
        },
        { status: 401 }
      )
    }

    // Estrai i parametri di query
    const { searchParams } = new URL(request.url)
    const queryParams = new URLSearchParams()

    // Passa tutti i parametri al backend
    searchParams.forEach((value, key) => {
      queryParams.append(key, value)
    })

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    const queryString = queryParams.toString()
    const url = `${backendUrl}/api/cavi/${cantiereId}${queryString ? `?${queryString}` : ''}`

    console.log('🔄 Cavi API: Proxying request to backend:', url)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      }
    })

    console.log('📡 Cavi API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Cavi API: Backend error:', errorData)
      return NextResponse.json(errorData, {
        status: response.status
      })
    }

    const data = await response.json()
    console.log('📡 Cavi API: Backend response data:', data)
    console.log('📡 Cavi API: Data type:', typeof data)
    console.log('📡 Cavi API: Is array:', Array.isArray(data))

    // Verifica se i dati sono un array o se sono wrappati in un oggetto
    let caviData = data
    if (data && typeof data === 'object' && !Array.isArray(data)) {
      // Se i dati sono un oggetto, cerca l'array dentro
      if (data.cavi && Array.isArray(data.cavi)) {
        caviData = data.cavi
      } else if (data.data && Array.isArray(data.data)) {
        caviData = data.data
      } else if (data.items && Array.isArray(data.items)) {
        caviData = data.items
      }
    }

    console.log('📡 Cavi API: Final cavi data:', caviData)
    console.log('📡 Cavi API: Final data type:', typeof caviData)
    console.log('📡 Cavi API: Final is array:', Array.isArray(caviData))

    return NextResponse.json(caviData, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Cavi API: Error:', error)
    return NextResponse.json(
      {
        detail: 'Errore interno del server'
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ cantiereId: string }> }
) {
  try {
    const { cantiereId } = await params
    const body = await request.json()

    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        {
          detail: 'Token di autorizzazione mancante'
        },
        { status: 401 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'

    console.log('🔄 Cavi API: Proxying POST request to backend:', `${backendUrl}/api/cavi/${cantiereId}`)

    const response = await fetch(`${backendUrl}/api/cavi/${cantiereId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      },
      body: JSON.stringify(body)
    })

    console.log('📡 Cavi API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Cavi API: Backend error:', errorData)
      return NextResponse.json(errorData, {
        status: response.status
      })
    }

    const data = await response.json()
    console.log('📡 Cavi API: Backend response data:', data)

    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Cavi API: POST Error:', error)
    return NextResponse.json(
      {
        detail: 'Errore interno del server'
      },
      { status: 500 }
    )
  }
}