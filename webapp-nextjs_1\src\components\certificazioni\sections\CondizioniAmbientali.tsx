'use client'

import React from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Cloud, Loader2, Settings, X } from 'lucide-react'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface WeatherData {
  temperature: number
  humidity: number
  city?: string
  isDemo: boolean
  source: string
}

interface CondizioniAmbientaliProps {
  formData: Partial<CertificazioneCavoCreate>
  weatherData: WeatherData | null
  isLoadingWeather: boolean
  isWeatherOverride: boolean
  onInputChange: (field: string, value: any) => void
  onToggleWeatherOverride: () => void
}

export function CondizioniAmbientali({
  formData,
  weatherData,
  isLoadingWeather,
  isWeatherOverride,
  onInputChange,
  onToggleWeatherOverride
}: CondizioniAmbientaliProps) {
  if (!weatherData) return null

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Dati Meteorologici */}
      <div className={`p-4 rounded-lg border-2 col-span-full ${
        weatherData.isDemo
          ? 'bg-amber-50 border-amber-200'
          : 'bg-emerald-50 border-emerald-200'
      }`}>
        <div className="flex items-start gap-4">
          {isLoadingWeather ? (
            <Loader2 className="h-5 w-5 animate-spin text-blue-600 mt-1" />
          ) : (
            <div className="text-2xl">
              {weatherData.isDemo ? '🔧' : '🌤️'}
            </div>
          )}

          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {weatherData.temperature}°C • {weatherData.humidity}% UR
                </div>
                {weatherData.city && (
                  <div className="text-sm text-gray-600">{weatherData.city}</div>
                )}
              </div>

              <Button
                type="button"
                variant={isWeatherOverride ? "default" : "outline"}
                size="sm"
                onClick={onToggleWeatherOverride}
                className="h-8"
              >
                {isWeatherOverride ? (
                  <>
                    <X className="h-3 w-3 mr-1" />
                    Automatico
                  </>
                ) : (
                  <>
                    <Settings className="h-3 w-3 mr-1" />
                    Manuale
                    </>
                  )}
                </Button>
              </div>

            <div className="text-xs text-gray-500">
              📡 {weatherData.source}
              {weatherData.isDemo && ' • Dati dimostrativi'}
            </div>
          </div>
        </div>
      </div>

      {/* Override Manuale */}
      {isWeatherOverride && (
        <div className="col-span-full p-4 bg-blue-50 border-2 border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 mb-4">
            <Settings className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">
              Inserimento Manuale Parametri
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="space-y-1">
              <Label htmlFor="temperatura_prova" className="text-xs font-medium text-gray-700">
                Temperatura (°C)
              </Label>
              <Input
                id="temperatura_prova"
                type="number"
                step="0.1"
                value={formData.temperatura_prova || ''}
                onChange={(e) => onInputChange('temperatura_prova', parseFloat(e.target.value))}
                placeholder="20.0"
                className="h-11 text-sm"
              />
              <p className="text-xs text-gray-500">Range tipico: 15-30°C</p>
            </div>

            <div className="space-y-1">
              <Label htmlFor="umidita_prova" className="text-xs font-medium text-gray-700">
                Umidità Relativa (%)
              </Label>
              <Input
                id="umidita_prova"
                type="number"
                min="0"
                max="100"
                value={formData.umidita_prova || ''}
                onChange={(e) => onInputChange('umidita_prova', parseFloat(e.target.value))}
                placeholder="50"
                className="h-11 text-sm"
              />
              <p className="text-xs text-gray-500">Range tipico: 30-70%</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
