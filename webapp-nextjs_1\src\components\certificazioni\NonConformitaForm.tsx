'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Save, X, AlertTriangle } from 'lucide-react'
import { NonConformita, NonConformitaCreate } from '@/types/certificazioni'
import { nonConformitaApi, caviApi, responsabiliApi } from '@/lib/api'

interface NonConformitaFormProps {
  cantiereId: number
  nonConformita?: NonConformita | null
  onSuccess: () => void
  onCancel: () => void
}

interface Cavo {
  id_cavo: string
  tipologia?: string
  sezione?: string
}

interface Responsabile {
  id_responsabile: number
  nome_responsabile: string
}

export default function NonConformitaForm({ 
  cantiereId, 
  nonConformita, 
  onSuccess, 
  onCancel 
}: NonConformitaFormProps) {
  const [formData, setFormData] = useState<NonConformitaCreate>({
    id_cavo: '',
    tipo_non_conformita: '',
    descrizione: '',
    severita: 'MEDIA',
    stato: 'APERTA',
    responsabile_rilevazione: '',
    data_rilevazione: new Date().toISOString().split('T')[0],
    azione_correttiva: '',
    responsabile_risoluzione: '',
    data_risoluzione: null,
    note: ''
  })

  const [cavi, setCavi] = useState<Cavo[]>([])
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const isEdit = !!nonConformita

  useEffect(() => {
    loadInitialData()
    if (nonConformita) {
      setFormData({
        id_cavo: nonConformita.id_cavo || '',
        tipo_non_conformita: nonConformita.tipo_non_conformita || '',
        descrizione: nonConformita.descrizione || '',
        severita: nonConformita.severita || 'MEDIA',
        stato: nonConformita.stato || 'APERTA',
        responsabile_rilevazione: nonConformita.responsabile_rilevazione || '',
        data_rilevazione: nonConformita.data_rilevazione ? 
          new Date(nonConformita.data_rilevazione).toISOString().split('T')[0] : 
          new Date().toISOString().split('T')[0],
        azione_correttiva: nonConformita.azione_correttiva || '',
        responsabile_risoluzione: nonConformita.responsabile_risoluzione || '',
        data_risoluzione: nonConformita.data_risoluzione ? 
          new Date(nonConformita.data_risoluzione).toISOString().split('T')[0] : 
          null,
        note: nonConformita.note || ''
      })
    }
  }, [nonConformita])

  const loadInitialData = async () => {
    try {
      setIsLoading(true)
      const [caviData, responsabiliData] = await Promise.all([
        caviApi.getCavi(cantiereId),
        responsabiliApi.getResponsabili(cantiereId)
      ])
      setCavi(caviData)
      setResponsabili(responsabiliData)
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.id_cavo) {
      errors.id_cavo = 'ID Cavo è obbligatorio'
    }
    if (!formData.tipo_non_conformita) {
      errors.tipo_non_conformita = 'Tipo non conformità è obbligatorio'
    }
    if (!formData.descrizione) {
      errors.descrizione = 'Descrizione è obbligatoria'
    }
    if (!formData.responsabile_rilevazione) {
      errors.responsabile_rilevazione = 'Responsabile rilevazione è obbligatorio'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      setIsSaving(true)
      setError('')

      const dataToSubmit = {
        ...formData,
        data_risoluzione: formData.data_risoluzione || null
      }

      if (isEdit && nonConformita) {
        await nonConformitaApi.updateNonConformita(cantiereId, nonConformita.id_non_conformita, dataToSubmit)
      } else {
        await nonConformitaApi.createNonConformita(cantiereId, dataToSubmit)
      }

      onSuccess()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il salvataggio')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof NonConformitaCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Rimuovi errore di validazione se presente
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          Caricamento dati...
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 flex items-center gap-3">
            <AlertTriangle className="h-6 w-6 text-red-600" />
            {isEdit ? 'Modifica Non Conformità' : 'Nuova Non Conformità'}
          </h2>
          <p className="text-slate-600 mt-1">
            {isEdit ? 'Aggiorna i dettagli della non conformità' : 'Registra una nuova non conformità'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informazioni Base */}
        <Card>
          <CardHeader>
            <CardTitle>Informazioni Base</CardTitle>
            <CardDescription>Dettagli principali della non conformità</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="id_cavo">ID Cavo *</Label>
                <Select value={formData.id_cavo} onValueChange={(value) => handleInputChange('id_cavo', value)}>
                  <SelectTrigger className={validationErrors.id_cavo ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Seleziona cavo" />
                  </SelectTrigger>
                  <SelectContent>
                    {cavi.map((cavo) => (
                      <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>
                        {cavo.id_cavo} {cavo.tipologia && `- ${cavo.tipologia}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.id_cavo && (
                  <p className="text-sm text-red-600">{validationErrors.id_cavo}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipo_non_conformita">Tipo Non Conformità *</Label>
                <Select 
                  value={formData.tipo_non_conformita} 
                  onValueChange={(value) => handleInputChange('tipo_non_conformita', value)}
                >
                  <SelectTrigger className={validationErrors.tipo_non_conformita ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Seleziona tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ISOLAMENTO">Isolamento</SelectItem>
                    <SelectItem value="CONTINUITA">Continuità</SelectItem>
                    <SelectItem value="RESISTENZA">Resistenza</SelectItem>
                    <SelectItem value="INSTALLAZIONE">Installazione</SelectItem>
                    <SelectItem value="COLLEGAMENTO">Collegamento</SelectItem>
                    <SelectItem value="DOCUMENTAZIONE">Documentazione</SelectItem>
                    <SelectItem value="ALTRO">Altro</SelectItem>
                  </SelectContent>
                </Select>
                {validationErrors.tipo_non_conformita && (
                  <p className="text-sm text-red-600">{validationErrors.tipo_non_conformita}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="severita">Severità</Label>
                <Select value={formData.severita} onValueChange={(value) => handleInputChange('severita', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BASSA">Bassa</SelectItem>
                    <SelectItem value="MEDIA">Media</SelectItem>
                    <SelectItem value="ALTA">Alta</SelectItem>
                    <SelectItem value="CRITICA">Critica</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stato">Stato</Label>
                <Select value={formData.stato} onValueChange={(value) => handleInputChange('stato', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="APERTA">Aperta</SelectItem>
                    <SelectItem value="IN_RISOLUZIONE">In Risoluzione</SelectItem>
                    <SelectItem value="RISOLTA">Risolta</SelectItem>
                    <SelectItem value="CHIUSA">Chiusa</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="descrizione">Descrizione *</Label>
              <Textarea
                id="descrizione"
                value={formData.descrizione}
                onChange={(e) => handleInputChange('descrizione', e.target.value)}
                className={validationErrors.descrizione ? 'border-red-500' : ''}
                placeholder="Descrivi dettagliatamente la non conformità rilevata..."
                rows={3}
              />
              {validationErrors.descrizione && (
                <p className="text-sm text-red-600">{validationErrors.descrizione}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Responsabili e Date */}
        <Card>
          <CardHeader>
            <CardTitle>Responsabili e Date</CardTitle>
            <CardDescription>Informazioni su rilevazione e risoluzione</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="responsabile_rilevazione">Responsabile Rilevazione *</Label>
                <Input
                  id="responsabile_rilevazione"
                  value={formData.responsabile_rilevazione}
                  onChange={(e) => handleInputChange('responsabile_rilevazione', e.target.value)}
                  className={validationErrors.responsabile_rilevazione ? 'border-red-500' : ''}
                  placeholder="Nome responsabile"
                />
                {validationErrors.responsabile_rilevazione && (
                  <p className="text-sm text-red-600">{validationErrors.responsabile_rilevazione}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="data_rilevazione">Data Rilevazione</Label>
                <Input
                  id="data_rilevazione"
                  type="date"
                  value={formData.data_rilevazione}
                  onChange={(e) => handleInputChange('data_rilevazione', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="responsabile_risoluzione">Responsabile Risoluzione</Label>
                <Input
                  id="responsabile_risoluzione"
                  value={formData.responsabile_risoluzione}
                  onChange={(e) => handleInputChange('responsabile_risoluzione', e.target.value)}
                  placeholder="Nome responsabile risoluzione"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="data_risoluzione">Data Risoluzione</Label>
                <Input
                  id="data_risoluzione"
                  type="date"
                  value={formData.data_risoluzione || ''}
                  onChange={(e) => handleInputChange('data_risoluzione', e.target.value || null)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="azione_correttiva">Azione Correttiva</Label>
              <Textarea
                id="azione_correttiva"
                value={formData.azione_correttiva}
                onChange={(e) => handleInputChange('azione_correttiva', e.target.value)}
                placeholder="Descrivi l'azione correttiva intrapresa..."
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={formData.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                placeholder="Note aggiuntive..."
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Azioni */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            <X className="h-4 w-4 mr-2" />
            Annulla
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Salvataggio...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEdit ? 'Aggiorna' : 'Crea'} Non Conformità
              </>
            )}
          </Button>
        </div>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}
      </form>
    </div>
  )
}
