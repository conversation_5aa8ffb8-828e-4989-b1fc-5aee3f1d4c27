'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import { useCantiere } from '@/hooks/useCantiere'
import { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'
import { parcoCaviApi } from '@/lib/api'
import { ParcoCavo } from '@/types'
import {
  REEL_STATES,
  getReelStateColor,
  getReelRowColor,
  determineReelState,
  calculateReelUsagePercentage,
  formatMeters,
  getReelStateDescription,
  canReelAcceptNewCables
} from '@/utils/bobineUtils'
import CreaBobinaDialog from '@/components/bobine/CreaBobinaDialog'
import ModificaBobinaDialog from '@/components/bobine/ModificaBobinaDialog'
import EliminaBobinaDialog from '@/components/bobine/EliminaBobinaDialog'
import AggiungiCaviDialogSimple from '@/components/bobine/AggiungiCaviDialogSimple'
import BobineStatistics from '@/components/bobine/BobineStatistics'
import {
  Package,
  Search,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Upload,
  Loader2,
  Cable,
  FileDown,
  FileUp
} from 'lucide-react'
import { ContextMenuCustom } from '@/components/ui/context-menu-custom'
import { getBobinaColorClasses } from '@/utils/softColors'

export default function ParcoCaviPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [bobine, setBobine] = useState<ParcoCavo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, isLoading: authLoading } = useAuth()
  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()

  // Stati per i dialoghi
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showAddCavoDialog, setShowAddCavoDialog] = useState(false)
  const [selectedBobina, setSelectedBobina] = useState<ParcoCavo | null>(null)

  // Stati per notifiche
  const [successMessage, setSuccessMessage] = useState('')
  const [errorMessage, setErrorMessage] = useState('')

  // Carica le bobine dal backend - MIGLIORATO con nuovo hook
  useEffect(() => {
    if (isValidCantiere && cantiereId && cantiereId > 0 && !cantiereLoading) {
      console.log('🏗️ ParcoCaviPage: Caricamento bobine per cantiere:', cantiereId)
      loadBobine()
    } else if (!cantiereLoading && !isValidCantiere) {
      console.warn('🏗️ ParcoCaviPage: Cantiere non valido, reset dati')
      setBobine([])
      setError(cantiereError || 'Nessun cantiere selezionato')
    }
  }, [cantiereId, isValidCantiere, cantiereLoading, cantiereError])

  const loadBobine = async () => {
    try {
      setIsLoading(true)
      setError('')

      // CONTROLLO: Il cantiere DEVE essere selezionato
      if (!cantiereId || cantiereId <= 0) {
        setError('Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine.')
        setBobine([]) // Svuota la lista per sicurezza
        return
      }

      const data = await parcoCaviApi.getBobine(cantiereId)
      setBobine(data || [])
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')
      setBobine([]) // Svuota la lista in caso di errore
    } finally {
      setIsLoading(false)
    }
  }

  // I filtri vengono applicati solo lato client per sicurezza
  // Non ricarichiamo dal server quando cambiano i filtri

  // Gestione notifiche
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(''), 5000)
      return () => clearTimeout(timer)
    }
  }, [successMessage])

  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => setErrorMessage(''), 5000)
      return () => clearTimeout(timer)
    }
  }, [errorMessage])

  // Funzioni per gestire i dialoghi
  const handleAddCavoToBobina = (bobina: ParcoCavo) => {
    setSelectedBobina(bobina)
    setShowAddCavoDialog(true)
  }

  const handleEditBobina = (bobina: ParcoCavo) => {
    setSelectedBobina(bobina)
    setShowEditDialog(true)
  }

  const handleDeleteBobina = (bobina: ParcoCavo) => {
    setSelectedBobina(bobina)
    setShowDeleteDialog(true)
  }

  const handleCreateSuccess = (message: string) => {
    setSuccessMessage(message)
    loadBobine() // Ricarica la lista
  }

  const handleCreateError = (message: string) => {
    setErrorMessage(message)
  }

  const handleEditSuccess = (message: string) => {
    setSuccessMessage(message)
    loadBobine() // Ricarica la lista
  }

  const handleEditError = (message: string) => {
    setErrorMessage(message)
  }

  const handleDeleteSuccess = (message: string) => {
    setSuccessMessage(message)
    loadBobine() // Ricarica la lista
  }

  const handleDeleteError = (message: string) => {
    setErrorMessage(message)
  }

  // Gestione menu contestuale
  const handleContextMenuAction = (action: string, data?: any) => {

    switch (action) {
      case 'import':
        handleImportBobine()
        break
      case 'export':
        handleExportBobine()
        break
      case 'add_bobina':
        setShowCreateDialog(true)
        break
      default:
    }
  }

  const handleImportBobine = () => {
    // TODO: Implementare import bobine
    setSuccessMessage('Funzione import in sviluppo')
  }

  const handleExportBobine = () => {
    // TODO: Implementare export bobine
    setSuccessMessage('Funzione export in sviluppo')
  }

  // Menu contestuale items
  const getContextMenuItems = () => [
    {
      id: 'import',
      label: 'Importa Bobine',
      icon: <FileUp className="h-4 w-4" />,
      action: 'import',
      disabled: !cantiereId || cantiereId <= 0
    },
    {
      id: 'export',
      label: 'Esporta Bobine',
      icon: <FileDown className="h-4 w-4" />,
      action: 'export',
      disabled: !cantiereId || cantiereId <= 0
    },
    {
      id: 'separator1',
      separator: true
    },
    {
      id: 'add_bobina',
      label: 'Aggiungi Bobina',
      icon: <Plus className="h-4 w-4" />,
      action: 'add_bobina',
      disabled: !cantiereId || cantiereId <= 0
    }
  ]

  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {
    // Determina lo stato effettivo della bobina
    const statoEffettivo = stato || determineReelState(metri_residui, metri_totali)

    // Configurazione colori per il nuovo design con pallini
    const stateConfig = {
      'disponibile': {
        dotColor: 'bg-green-500',
        textColor: 'text-green-700',
        label: 'DISPONIBILE'
      },
      'in uso': {
        dotColor: 'bg-blue-500',
        textColor: 'text-blue-700',
        label: 'IN USO'
      },
      'terminata': {
        dotColor: 'bg-gray-500',
        textColor: 'text-gray-700',
        label: 'TERMINATA'
      },
      'over': {
        dotColor: 'bg-red-500',
        textColor: 'text-red-700',
        label: 'OVER'
      }
    }

    const config = stateConfig[statoEffettivo?.toLowerCase() as keyof typeof stateConfig] || stateConfig['terminata']

    return (
      <div
        className="flex items-center gap-2"
        title={getReelStateDescription(statoEffettivo)}
      >
        <div className={`w-2 h-2 rounded-full ${config.dotColor}`} />
        <span className={`text-sm font-semibold ${config.textColor}`}>
          {config.label}
        </span>
      </div>
    )
  }

  const filteredBobine = bobine.filter(bobina => {
    const matchesSearch = bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())

    let matchesStatus = true
    if (selectedStatus !== 'all') {
      // Determina lo stato effettivo della bobina
      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)

      switch (selectedStatus) {
        case 'disponibile':
          matchesStatus = statoEffettivo === REEL_STATES.DISPONIBILE
          break
        case 'in_uso':
          matchesStatus = statoEffettivo === REEL_STATES.IN_USO
          break
        case 'esaurita':
          matchesStatus = statoEffettivo === REEL_STATES.TERMINATA
          break
        case 'over':
          matchesStatus = statoEffettivo === REEL_STATES.OVER
          break
      }
    }

    return matchesSearch && matchesStatus
  })

  return (
    <CantiereErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="max-w-[90%] mx-auto py-6 space-y-6">

          {/* Mostra errore specifico delle bobine se presente */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

        {/* Statistics */}
        <BobineStatistics
          bobine={bobine}
          filteredBobine={filteredBobine}
          className="mb-6"
        />

        {/* Filters and Search - Migliorato */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-1">Ricerca e Filtri Bobine</h2>
          </div>

          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
            {/* Barra di ricerca migliorata */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <Input
                placeholder="Cerca per ID bobina, tipologia, o numero..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            {/* Filtri a pillola */}
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'all', label: 'Tutte', count: bobine.length },
                { key: 'disponibile', label: 'Disponibili', count: bobine.filter(b => (b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)) === REEL_STATES.DISPONIBILE).length },
                { key: 'in_uso', label: 'In Uso', count: bobine.filter(b => (b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)) === REEL_STATES.IN_USO).length },
                { key: 'esaurita', label: 'Esaurite', count: bobine.filter(b => (b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)) === REEL_STATES.TERMINATA).length },
                { key: 'over', label: 'Over', count: bobine.filter(b => (b.stato_bobina || determineReelState(b.metri_residui, b.metri_totali)) === REEL_STATES.OVER).length }
              ].map((filter) => (
                <button
                  key={filter.key}
                  onClick={() => setSelectedStatus(filter.key)}
                  className={`inline-flex items-center gap-1.5 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    selectedStatus === filter.key
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                  }`}
                >
                  {filter.label}
                  <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                    selectedStatus === filter.key
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {filter.count}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Bobine Table - Migliorato */}
        <ContextMenuCustom
          items={getContextMenuItems()}
          onAction={handleContextMenuAction}
        >
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-gray-800">Elenco Bobine ({filteredBobine.length})</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Gestisci le bobine, visualizza lo stato di utilizzo e le metrature. Clicca tasto destro per azioni aggiuntive.
                  </p>
                </div>
                <Button
                  onClick={() => setShowCreateDialog(true)}
                  disabled={!cantiereId || cantiereId <= 0}
                  title={(!cantiereId || cantiereId <= 0) ? 'Seleziona un cantiere per creare una bobina' : 'Crea nuova bobina'}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Nuova Bobina
                </Button>
              </div>
            </div>
            <div className="overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-gray-200">
                      <TableHead className="text-left font-semibold text-gray-700 py-3 px-4">Bobina</TableHead>
                      <TableHead className="text-left font-semibold text-gray-700 py-3 px-4">Utility</TableHead>
                      <TableHead className="text-left font-semibold text-gray-700 py-3 px-4">Tipologia</TableHead>
                      <TableHead className="text-left font-semibold text-gray-700 py-3 px-4">Formazione</TableHead>
                      <TableHead className="text-left font-semibold text-gray-700 py-3 px-4">Metrature</TableHead>
                      <TableHead className="text-center font-semibold text-gray-700 py-3 px-4">Utilizzo</TableHead>
                      <TableHead className="text-left font-semibold text-gray-700 py-3 px-4">Stato</TableHead>
                      <TableHead className="text-left font-semibold text-gray-700 py-3 px-4">Ubicazione</TableHead>
                      <TableHead className="text-center font-semibold text-gray-700 py-3 px-4">Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Caricamento bobine...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2 text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          {error}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredBobine.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-slate-500">
                        Nessuna bobina trovata
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredBobine.map((bobina) => {
                      const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)
                      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)
                      const rowColorClass = getReelRowColor(statoEffettivo)

                      return (
                        <TableRow
                          key={bobina.id_bobina}
                          className="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150"
                        >
                          <TableCell className="py-4 px-4">
                            <div className="font-semibold text-gray-900">{bobina.numero_bobina || '-'}</div>
                          </TableCell>
                          <TableCell className="py-4 px-4">
                            <div className="text-gray-700">{bobina.utility || '-'}</div>
                          </TableCell>
                          <TableCell className="py-4 px-4">
                            <div className="text-gray-700">{bobina.tipologia || '-'}</div>
                          </TableCell>
                          <TableCell className="py-4 px-4">
                            <div className="font-medium text-gray-900">{bobina.sezione || '-'}</div>
                          </TableCell>
                          <TableCell className="py-4 px-4">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-600">Residuo:</span>
                                <span className={`font-semibold ${bobina.metri_residui < 0 ? 'text-red-600' : 'text-gray-900'}`}>
                                  {formatMeters(bobina.metri_residui)}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-600">Totale:</span>
                                <span className="font-medium text-gray-700">{formatMeters(bobina.metri_totali)}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-4 text-center">
                            <div className="font-semibold text-gray-900">{Math.round(percentualeUtilizzo)}%</div>
                          </TableCell>
                          <TableCell className="py-4 px-4">
                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}
                          </TableCell>
                          <TableCell className="py-4 px-4">
                            <div className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                              {bobina.ubicazione_bobina || 'Non specificata'}
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-4">
                            <div className="flex items-center justify-center gap-2">
                              <button
                                onClick={() => handleAddCavoToBobina(bobina)}
                                disabled={!canReelAcceptNewCables(statoEffettivo)}
                                title={
                                  statoEffettivo === REEL_STATES.OVER
                                    ? "Bobina OVER - Non può accettare nuovi cavi"
                                    : statoEffettivo === REEL_STATES.TERMINATA
                                    ? "Bobina terminata - Non può accettare nuovi cavi"
                                    : "Aggiungi cavo a bobina"
                                }
                                className={`p-2 rounded-lg transition-colors duration-200 ${
                                  !canReelAcceptNewCables(statoEffettivo)
                                    ? "opacity-50 cursor-not-allowed text-gray-400"
                                    : "text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                                }`}
                              >
                                <Cable className="h-4 w-4" />
                              </button>

                              <button
                                onClick={() => handleEditBobina(bobina)}
                                title={
                                  statoEffettivo === REEL_STATES.OVER
                                    ? "Modifica bobina (limitata per bobine OVER)"
                                    : "Modifica bobina"
                                }
                                className="p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200"
                              >
                                <Edit className="h-4 w-4" />
                              </button>

                              <button
                                onClick={() => handleDeleteBobina(bobina)}
                                disabled={statoEffettivo === REEL_STATES.OVER || statoEffettivo !== REEL_STATES.DISPONIBILE}
                                title={
                                  statoEffettivo === REEL_STATES.OVER
                                    ? "Bobina OVER - Non può essere eliminata"
                                    : statoEffettivo !== REEL_STATES.DISPONIBILE
                                    ? "Solo bobine disponibili possono essere eliminate"
                                    : "Elimina bobina"
                                }
                                className={`p-2 rounded-lg transition-colors duration-200 ${
                                  (statoEffettivo === REEL_STATES.OVER || statoEffettivo !== REEL_STATES.DISPONIBILE)
                                    ? "opacity-50 cursor-not-allowed text-gray-400"
                                    : "text-gray-600 hover:text-red-600 hover:bg-red-50"
                                }`}
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
        </ContextMenuCustom>

        {/* Notifiche */}
        {successMessage && (
          <div className="fixed top-4 right-4 z-50">
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                {successMessage}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {errorMessage && (
          <div className="fixed top-4 right-4 z-50">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {errorMessage}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Dialoghi */}
      <CreaBobinaDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        cantiereId={cantiereId!}
        onSuccess={handleCreateSuccess}
        onError={handleCreateError}
      />

      <ModificaBobinaDialog
        open={showEditDialog}
        onClose={() => setShowEditDialog(false)}
        bobina={selectedBobina}
        cantiereId={cantiereId}
        onSuccess={handleEditSuccess}
        onError={handleEditError}
      />

      <EliminaBobinaDialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        bobina={selectedBobina}
        cantiereId={cantiereId!}
        onSuccess={handleDeleteSuccess}
        onError={handleDeleteError}
      />

      <AggiungiCaviDialogSimple
        open={showAddCavoDialog}
        onClose={() => setShowAddCavoDialog(false)}
        bobina={selectedBobina}
        cantiereId={cantiereId!}
        onSuccess={handleCreateSuccess}
        onError={handleCreateError}
      />
        </div>
      </div>
    </CantiereErrorBoundary>
  )
}
