import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = params.cantiereId
    
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    console.log('🔄 Statistics API: Proxying request to backend:', `${backendUrl}/api/cantieri/${cantiereId}/statistics`)
    
    const response = await fetch(`${backendUrl}/api/cantieri/${cantiereId}/statistics`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      }
    })

    console.log('📡 Statistics API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Statistics API: Backend error:', errorData)
      return NextResponse.json(errorData, { 
        status: response.status 
      })
    }

    const data = await response.json()
    console.log('📡 Statistics API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Statistics API: Error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
