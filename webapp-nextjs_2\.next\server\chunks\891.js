"use strict";exports.id=891,exports.ids=[891],exports.modules={26134:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>z,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(43210),o=r(70569),a=r(98599),s=r(11273),i=r(96963),l=r(65551),d=r(31355),c=r(32547),u=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),h=r(63376),x=r(8730),y=r(60687),v="Dialog",[b,j]=(0,s.A)(v),[C,D]=b(v),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:s,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,l.i)({prop:o,defaultProp:a??!1,onChange:s,caller:v});return(0,y.jsx)(C,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=v;var w="DialogTrigger",k=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=D(w,r),i=(0,a.s)(t,s.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":K(s.open),...n,ref:i,onClick:(0,o.m)(e.onClick,s.onOpenToggle)})});k.displayName=w;var E="DialogPortal",[I,N]=b(E,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,s=D(E,t);return(0,y.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||s.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=E;var _="DialogOverlay",F=n.forwardRef((e,t)=>{let r=N(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(_,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(A,{...o,ref:t})}):null});F.displayName=_;var P=(0,x.TL)("DialogOverlay.RemoveScroll"),A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(_,r);return(0,y.jsx)(m.A,{as:P,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),G="DialogContent",T=n.forwardRef((e,t)=>{let r=N(G,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(G,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||a.open,children:a.modal?(0,y.jsx)(L,{...o,ref:t}):(0,y.jsx)(B,{...o,ref:t})})});T.displayName=G;var L=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),s=n.useRef(null),i=(0,a.s)(t,r.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,h.Eq)(e)},[]),(0,y.jsx)(M,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),M=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:i,...l}=e,u=D(G,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:s,onUnmountAutoFocus:i,children:(0,y.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":K(u.open),...l,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:u.titleId}),(0,y.jsx)(Y,{contentRef:p,descriptionId:u.descriptionId})]})]})}),S="DialogTitle",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(S,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});q.displayName=S;var $="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D($,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=$;var Z="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(Z,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}H.displayName=Z;var U="DialogTitleWarning",[V,X]=(0,s.q)(U,{contentName:G,titleName:S,docsSlug:"dialog"}),J=({titleId:e})=>{let t=X(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Y=({contentRef:e,descriptionId:t})=>{let r=X("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},z=R,Q=k,ee=O,et=F,er=T,en=q,eo=W,ea=H},40211:(e,t,r)=>{r.d(t,{C1:()=>D,bL:()=>j});var n=r(43210),o=r(98599),a=r(11273),s=r(70569),i=r(65551),l=r(83721),d=r(18853),c=r(46059),u=r(14163),p=r(60687),f="Checkbox",[g,m]=(0,a.A)(f),[h,x]=g(f);function y(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:s,form:l,name:d,onCheckedChange:c,required:u,value:g="on",internal_do_not_use_render:m}=e,[x,y]=(0,i.i)({prop:r,defaultProp:a??!1,onChange:c,caller:f}),[v,b]=n.useState(null),[j,C]=n.useState(null),D=n.useRef(!1),R=!v||!!l||!!v.closest("form"),w={checked:x,disabled:s,setChecked:y,control:v,setControl:b,name:d,form:l,value:g,hasConsumerStoppedPropagationRef:D,required:u,defaultChecked:!k(a)&&a,isFormControl:R,bubbleInput:j,setBubbleInput:C};return(0,p.jsx)(h,{scope:t,...w,children:"function"==typeof m?m(w):o})}var v="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},i)=>{let{control:l,value:d,disabled:c,checked:f,required:g,setControl:m,setChecked:h,hasConsumerStoppedPropagationRef:y,isFormControl:b,bubbleInput:j}=x(v,e),C=(0,o.s)(i,m),D=n.useRef(f);return n.useEffect(()=>{let e=l?.form;if(e){let t=()=>h(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[l,h]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":k(f)?"mixed":f,"aria-required":g,"data-state":E(f),"data-disabled":c?"":void 0,disabled:c,value:d,...a,ref:C,onKeyDown:(0,s.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.m)(r,e=>{h(e=>!!k(e)||!e),j&&b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});b.displayName=v;var j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:s,disabled:i,value:l,onCheckedChange:d,form:c,...u}=e;return(0,p.jsx)(y,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:i,required:s,onCheckedChange:d,name:n,form:c,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(b,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(w,{__scopeCheckbox:r})]})})});j.displayName=f;var C="CheckboxIndicator",D=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=x(C,r);return(0,p.jsx)(c.C,{present:n||k(a.checked)||!0===a.checked,children:(0,p.jsx)(u.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});D.displayName=C;var R="CheckboxBubbleInput",w=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:s,checked:i,defaultChecked:c,required:f,disabled:g,name:m,value:h,form:y,bubbleInput:v,setBubbleInput:b}=x(R,e),j=(0,o.s)(r,b),C=(0,l.Z)(i),D=(0,d.X)(a);n.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!s.current;if(C!==i&&e){let r=new Event("click",{bubbles:t});v.indeterminate=k(i),e.call(v,!k(i)&&i),v.dispatchEvent(r)}},[v,C,i,s]);let w=n.useRef(!k(i)&&i);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??w.current,required:f,disabled:g,name:m,value:h,form:y,...t,tabIndex:-1,ref:j,style:{...t.style,...D,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function k(e){return"indeterminate"===e}function E(e){return k(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=R},99270:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};