'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, ClipboardList, Users, Calendar, User } from 'lucide-react'
import { comandeApi, responsabiliApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { validateCaviForComanda, formatValidationResults, COMMAND_TYPES } from '@/utils/comandeValidation'

interface CreaComandaDialogProps {
  open: boolean
  onClose: () => void
  caviSelezionati?: string[]
  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'
  onSuccess: (message: string) => void
  onError: (message: string) => void
  onComandaCreated?: () => void
}

interface Responsabile {
  id_responsabile: number
  nome_responsabile: string
  numero_telefono?: string
  mail?: string
}

interface FormData {
  tipo_comanda: string
  responsabile: string
  descrizione: string
  data_scadenza: string
  numero_componenti_squadra: number
}

export default function CreaComandaDialog({
  open,
  onClose,
  caviSelezionati = [],
  tipoComanda,
  onSuccess,
  onError,
  onComandaCreated
}: CreaComandaDialogProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [loadingResponsabili, setLoadingResponsabili] = useState(false)
  const [validationResults, setValidationResults] = useState<string>('')
  const [showValidation, setShowValidation] = useState(false)

  const { cantiere } = useAuth()

  // Get cantiere ID con fallback al localStorage
  const [cantiereId, setCantiereId] = useState<number>(0)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      setCantiereId(storedId)
    }
  }, [cantiere])

  const [formData, setFormData] = useState<FormData>({
    tipo_comanda: tipoComanda || 'POSA',
    responsabile: '',
    descrizione: '',
    data_scadenza: '',
    numero_componenti_squadra: 1
  })

  // Carica responsabili quando si apre il dialog
  useEffect(() => {
    if (open && cantiereId > 0) {
      loadResponsabili()
    }
  }, [open, cantiereId])

  // Reset form quando si chiude il dialog
  useEffect(() => {
    if (!open) {
      setFormData({
        tipo_comanda: tipoComanda || 'POSA',
        responsabile: '',
        descrizione: '',
        data_scadenza: '',
        numero_componenti_squadra: 1
      })
      setError('')
    }
  }, [open, tipoComanda])

  const loadResponsabili = async () => {
    try {
      setLoadingResponsabili(true)
      const response = await responsabiliApi.getResponsabili(cantiereId)
      const responsabiliData = response?.data || response || []
      setResponsabili(Array.isArray(responsabiliData) ? responsabiliData : [])
    } catch (error: any) {
      setResponsabili([])
    } finally {
      setLoadingResponsabili(false)
    }
  }

  const validateCavi = () => {
    if (caviSelezionati.length === 0) {
      setValidationResults('Nessun cavo selezionato per la validazione')
      setShowValidation(true)
      return
    }

    const validation = validateCaviForComanda(
      caviSelezionati,
      formData.tipo_comanda,
      formData.responsabile
    )

    const formattedResults = formatValidationResults(validation)
    setValidationResults(formattedResults)
    setShowValidation(true)
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      setError('')

      // Validazioni base
      if (!formData.tipo_comanda) {
        setError('Seleziona il tipo di comanda')
        return
      }

      if (!formData.responsabile) {
        setError('Seleziona un responsabile')
        return
      }

      if (!cantiereId || cantiereId <= 0) {
        setError('Cantiere non selezionato')
        return
      }

      // Validazione cavi se presenti
      if (caviSelezionati.length > 0) {
        const validation = validateCaviForComanda(
          caviSelezionati,
          formData.tipo_comanda,
          formData.responsabile
        )

        if (!validation.isValid) {
          setError('Validazione cavi fallita. Controllare i dettagli nella sezione validazione.')
          setValidationResults(formatValidationResults(validation))
          setShowValidation(true)
          return
        }

        // Mostra avvisi se presenti ma permetti di continuare
        if (validation.warnings.length > 0) {
          setValidationResults(formatValidationResults(validation))
          setShowValidation(true)
        }
      }

      // Prepara i dati per la creazione
      const comandaData = {
        tipo_comanda: formData.tipo_comanda,
        responsabile: formData.responsabile,
        descrizione: formData.descrizione || null,
        data_scadenza: formData.data_scadenza || null,
        numero_componenti_squadra: formData.numero_componenti_squadra
      }

      let response
      if (caviSelezionati && caviSelezionati.length > 0) {
        // Crea comanda con cavi pre-selezionati
        response = await comandeApi.createComandaWithCavi(
          cantiereId,
          comandaData,
          caviSelezionati
        )
      } else {
        // Crea comanda vuota
        response = await comandeApi.createComanda(cantiereId, comandaData)
      }

      const codiceComanda = response?.data?.codice_comanda || response?.codice_comanda
      const successMessage = caviSelezionati && caviSelezionati.length > 0
        ? `Comanda ${codiceComanda} creata con successo per ${caviSelezionati.length} cavi`
        : `Comanda ${codiceComanda} creata con successo`

      onSuccess(successMessage)
      onComandaCreated?.()
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const tipoComandaOptions = [
    { value: 'POSA', label: '🔧 Posa Cavi', description: 'Installazione e posa dei cavi' },
    { value: 'COLLEGAMENTO_PARTENZA', label: '🔌 Collegamento Partenza', description: 'Collegamento lato partenza' },
    { value: 'COLLEGAMENTO_ARRIVO', label: '⚡ Collegamento Arrivo', description: 'Collegamento lato arrivo' },
    { value: 'CERTIFICAZIONE', label: '📋 Certificazione', description: 'Test e certificazione' }
  ]

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ClipboardList className="h-5 w-5" />
            Crea Nuova Comanda
          </DialogTitle>
          <DialogDescription>
            {caviSelezionati && caviSelezionati.length > 0
              ? `Crea una comanda per ${caviSelezionati.length} cavi selezionati`
              : 'Crea una nuova comanda di lavoro'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Sezione validazione cavi */}
          {caviSelezionati.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Validazione Cavi ({caviSelezionati.length} selezionati)</h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={validateCavi}
                  disabled={!formData.tipo_comanda || !formData.responsabile}
                >
                  Valida Cavi
                </Button>
              </div>

              {showValidation && validationResults && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <pre className="whitespace-pre-wrap text-xs font-mono">
                      {validationResults}
                    </pre>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Tipo comanda */}
          <div className="space-y-2">
            <Label htmlFor="tipo">Tipo Comanda *</Label>
            <Select
              value={formData.tipo_comanda}
              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {tipoComandaOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-sm text-slate-500">{option.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Responsabile */}
          <div className="space-y-2">
            <Label htmlFor="responsabile">Responsabile *</Label>
            {loadingResponsabili ? (
              <div className="flex items-center gap-2 p-2 text-sm text-slate-500">
                <Loader2 className="h-4 w-4 animate-spin" />
                Caricamento responsabili...
              </div>
            ) : (
              <Select
                value={formData.responsabile}
                onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleziona responsabile" />
                </SelectTrigger>
                <SelectContent>
                  {responsabili.map((resp) => (
                    <SelectItem key={resp.id_responsabile} value={resp.nome_responsabile}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{resp.nome_responsabile}</div>
                          {resp.numero_telefono && (
                            <div className="text-sm text-slate-500">{resp.numero_telefono}</div>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Descrizione */}
          <div className="space-y-2">
            <Label htmlFor="descrizione">Descrizione</Label>
            <Textarea
              id="descrizione"
              placeholder="Descrizione opzionale della comanda..."
              value={formData.descrizione}
              onChange={(e) => setFormData(prev => ({ ...prev, descrizione: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Data scadenza e numero componenti squadra */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="data_scadenza">Data Scadenza</Label>
              <Input
                id="data_scadenza"
                type="date"
                value={formData.data_scadenza}
                onChange={(e) => setFormData(prev => ({ ...prev, data_scadenza: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="squadra">Componenti Squadra</Label>
              <Input
                id="squadra"
                type="number"
                min="1"
                max="20"
                value={formData.numero_componenti_squadra}
                onChange={(e) => setFormData(prev => ({ ...prev, numero_componenti_squadra: parseInt(e.target.value) || 1 }))}
              />
            </div>
          </div>

          {/* Info cavi selezionati */}
          {caviSelezionati && caviSelezionati.length > 0 && (
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 text-blue-700">
                <ClipboardList className="h-4 w-4" />
                <span className="font-medium">Cavi da assegnare: {caviSelezionati.length}</span>
              </div>
              <div className="text-sm text-blue-600 mt-1">
                I cavi selezionati verranno automaticamente assegnati a questa comanda
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Annulla
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Crea Comanda
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
