'use client'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Eye, Package, Calendar, MapPin, Truck, FileText } from 'lucide-react'
import { ParcoCavo } from '@/types'
import { 
  getReelStateColor, 
  getReelStateDescription, 
  calculateReelUsagePercentage,
  formatMeters 
} from '@/utils/bobineUtils'

interface VisualizzaBobinaDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
}

export default function VisualizzaBobinaDialog({
  open,
  onClose,
  bobina
}: VisualizzaBobinaDialogProps) {
  if (!bobina) return null

  const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)
  const statoColorClass = getReelStateColor(bobina.stato_bobina)

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Dettagli Bobina {bobina.numero_bobina}
          </DialogTitle>
          <DialogDescription>
            Informazioni complete sulla bobina {bobina.numero_bobina}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Informazioni principali */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Identificazione
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Bobina:</span>
                    <span className="font-medium">{bobina.numero_bobina}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Utility:</span>
                    <span className="font-medium">{bobina.utility || '-'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Configurazione:</span>
                    <span className="font-medium">
                      {bobina.configurazione === 's' ? 'Standard' : 'Manuale'}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-lg mb-3">Specifiche Tecniche</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Tipologia:</span>
                    <span className="font-medium">{bobina.tipologia || '-'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Formazione:</span>
                    <span className="font-medium">{bobina.sezione || '-'}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg mb-3">Stato e Utilizzo</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Stato:</span>
                    <Badge className={statoColorClass} title={getReelStateDescription(bobina.stato_bobina)}>
                      {bobina.stato_bobina}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-slate-600">Metri Totali:</span>
                      <span className="font-medium">{formatMeters(bobina.metri_totali)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Metri Residui:</span>
                      <span className="font-medium">{formatMeters(bobina.metri_residui)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Metri Utilizzati:</span>
                      <span className="font-medium">
                        {formatMeters(bobina.metri_totali - bobina.metri_residui)}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-slate-600">Utilizzo:</span>
                      <span className="font-medium">{Math.round(percentualeUtilizzo)}%</span>
                    </div>
                    <Progress value={percentualeUtilizzo} className="h-3" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Informazioni logistiche */}
          <div className="border-t pt-4">
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Informazioni Logistiche
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-slate-500" />
                  <span className="text-slate-600">Ubicazione:</span>
                  <span className="font-medium">{bobina.ubicazione_bobina || 'Non specificata'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Truck className="h-4 w-4 text-slate-500" />
                  <span className="text-slate-600">Fornitore:</span>
                  <span className="font-medium">{bobina.fornitore || 'Non specificato'}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-slate-500" />
                  <span className="text-slate-600">N° DDT:</span>
                  <span className="font-medium">{bobina.n_DDT || 'Non specificato'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-slate-500" />
                  <span className="text-slate-600">Data DDT:</span>
                  <span className="font-medium">
                    {bobina.data_DDT ? new Date(bobina.data_DDT).toLocaleDateString('it-IT') : 'Non specificata'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Informazioni aggiuntive */}
          {(bobina.metri_residui < 0 || bobina.stato_bobina === 'Over') && (
            <div className="border-t pt-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-800 mb-2">⚠️ Attenzione - Bobina Sovra-utilizzata</h4>
                <p className="text-red-700 text-sm">
                  Questa bobina ha metri residui negativi ({formatMeters(bobina.metri_residui)}), 
                  indicando che sono stati posati più metri di quelli disponibili. 
                  Verificare l'accuratezza delle misurazioni e considerare una revisione dei dati.
                </p>
              </div>
            </div>
          )}

          {bobina.stato_bobina === 'Terminata' && (
            <div className="border-t pt-4">
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <h4 className="font-medium text-orange-800 mb-2">📋 Bobina Terminata</h4>
                <p className="text-orange-700 text-sm">
                  Questa bobina è completamente esaurita. Non è più disponibile per nuove installazioni.
                </p>
              </div>
            </div>
          )}

          {bobina.stato_bobina === 'In uso' && (
            <div className="border-t pt-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 mb-2">🔄 Bobina in Uso</h4>
                <p className="text-blue-700 text-sm">
                  Questa bobina è parzialmente utilizzata. Disponibili ancora {formatMeters(bobina.metri_residui)} 
                  per nuove installazioni.
                </p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
