'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function SimpleLoginPage() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setResult('Logging in...')
    
    try {
      console.log('Starting login with:', { username, password })
      
      const formData = new FormData()
      formData.append('username', username)
      formData.append('password', password)

      console.log('Sending request to backend...')
      
      const response = await fetch('http://localhost:8001/api/auth/login', {
        method: 'POST',
        body: formData
      })

      console.log('Response status:', response.status)
      const data = await response.json()
      console.log('Response data:', data)

      if (response.ok) {
        // Salva il token
        localStorage.setItem('token', data.access_token)
        
        setResult(`SUCCESS: Login riuscito! Token salvato. Reindirizzamento...`)
        
        // Reindirizza in base al ruolo
        setTimeout(() => {
          if (data.role === 'owner') {
            console.log('Redirecting to /admin')
            router.push('/admin')
          } else if (data.role === 'user') {
            console.log('Redirecting to /cantieri')
            router.push('/cantieri')
          } else {
            console.log('Redirecting to /')
            router.push('/')
          }
        }, 1000)
      } else {
        setResult(`ERROR: ${response.status} - ${data.detail || 'Login failed'}`)
      }
    } catch (error) {
      console.error('Login error:', error)
      setResult(`EXCEPTION: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f8f9fa',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '10px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        width: '100%',
        maxWidth: '400px'
      }}>
        <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#333' }}>
          Simple Login Test
        </h1>
        
        <form onSubmit={handleLogin} style={{ marginBottom: '20px' }}>
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Username:
            </label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="admin"
              required
              disabled={loading}
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #ddd',
                borderRadius: '5px',
                fontSize: '16px'
              }}
            />
          </div>
          
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Password:
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="admin"
              required
              disabled={loading}
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #ddd',
                borderRadius: '5px',
                fontSize: '16px'
              }}
            />
          </div>
          
          <button
            type="submit"
            disabled={loading}
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: loading ? '#ccc' : '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              fontSize: '16px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>

        <div style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '15px', 
          borderRadius: '5px',
          whiteSpace: 'pre-wrap',
          minHeight: '100px',
          fontSize: '14px',
          fontFamily: 'monospace'
        }}>
          {result || 'Enter credentials and click Login...'}
        </div>
        
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <small style={{ color: '#666' }}>
            Test credentials: admin/admin
          </small>
        </div>
      </div>
    </div>
  )
}
