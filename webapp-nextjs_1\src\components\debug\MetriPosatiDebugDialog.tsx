'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Bug, Cable, Database, AlertTriangle } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Cavo, ParcoCavo } from '@/types'

interface MetriPosatiDebugDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  caviSelezionati: any[]
  caviMetri: Record<string, string>
  forceOver: boolean
}

export default function MetriPosatiDebugDialog({
  open,
  onClose,
  bobina,
  caviSelezionati,
  caviMetri,
  forceOver
}: MetriPosatiDebugDialogProps) {
  const { cantiere } = useAuth()
  const [debugData, setDebugData] = useState<{
    metriTotaliRichiesti: number
    metriResiduiBobina: number
    metriDisponibili: number
    caviAnalisi: any[]
    problemi: string[]
    warnings: string[]
    forceOverNecessario: boolean
  } | null>(null)

  useEffect(() => {
    if (open && bobina && caviSelezionati.length > 0) {
      analyzeMetriPosati()
    }
  }, [open, bobina, caviSelezionati, caviMetri, forceOver])

  const analyzeMetriPosati = () => {
    if (!bobina) return

    const metriResiduiBobina = bobina.metri_residui || 0
    const problemi: string[] = []
    const warnings: string[] = []
    const caviAnalisi: any[] = []
    let metriTotaliRichiesti = 0

    // Analizza ogni cavo selezionato
    caviSelezionati.forEach(cavo => {
      const metriRichiesti = parseFloat(caviMetri[cavo.id_cavo] || '0')
      metriTotaliRichiesti += metriRichiesti

      const analisi = {
        id_cavo: cavo.id_cavo,
        tipologia: cavo.tipologia,
        sezione: cavo.sezione,
        metri_teorici: cavo.metri_teorici,
        metri_richiesti: metriRichiesti,
        metri_posati_attuali: cavo.metri_posati || cavo.metratura_reale || 0,
        bobina_attuale: cavo.id_bobina,
        is_compatible: cavo.tipologia === bobina.tipologia && cavo.sezione === bobina.sezione,
        is_incompatible: cavo._isIncompatible || false,
        problemi_cavo: [] as string[],
        warnings_cavo: [] as string[]
      }

      // Validazioni per singolo cavo
      if (metriRichiesti <= 0) {
        analisi.problemi_cavo.push('Metri richiesti <= 0')
        problemi.push(`${cavo.id_cavo}: Metri richiesti non validi`)
      }

      if (metriRichiesti > (cavo.metri_teorici || 0)) {
        analisi.problemi_cavo.push('Metri > metri teorici')
        problemi.push(`${cavo.id_cavo}: Metri richiesti (${metriRichiesti}) > metri teorici (${cavo.metri_teorici})`)
      }

      if (!analisi.is_compatible && !analisi.is_incompatible) {
        analisi.warnings_cavo.push('Incompatibilità non gestita')
        warnings.push(`${cavo.id_cavo}: Incompatibile ma non marcato come tale`)
      }

      if (analisi.metri_posati_attuali > 0) {
        analisi.warnings_cavo.push('Cavo già posato')
        warnings.push(`${cavo.id_cavo}: Già posato con ${analisi.metri_posati_attuali}m`)
      }

      if (analisi.bobina_attuale && analisi.bobina_attuale !== 'BOBINA_VUOTA' && analisi.bobina_attuale !== bobina.id_bobina) {
        analisi.warnings_cavo.push('Cambio bobina')
        warnings.push(`${cavo.id_cavo}: Cambio da bobina ${analisi.bobina_attuale} a ${bobina.id_bobina}`)
      }

      caviAnalisi.push(analisi)
    })

    // Analisi globale metri
    const metriDisponibili = metriResiduiBobina
    const forceOverNecessario = metriTotaliRichiesti > metriDisponibili

    if (forceOverNecessario && !forceOver) {
      problemi.push(`Metri totali richiesti (${metriTotaliRichiesti}) > metri disponibili (${metriDisponibili}) - Force Over necessario`)
    }

    if (metriTotaliRichiesti > metriDisponibili) {
      warnings.push(`Bobina andrà in stato OVER: ${metriTotaliRichiesti - metriDisponibili}m oltre il limite`)
    }

    setDebugData({
      metriTotaliRichiesti,
      metriResiduiBobina,
      metriDisponibili,
      caviAnalisi,
      problemi,
      warnings,
      forceOverNecessario
    })
  }

  if (!debugData) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Debug: Aggiungi Metri a Bobina {bobina?.id_bobina}
          </DialogTitle>
          <DialogDescription>
            Analisi dettagliata dell'operazione di aggiunta metri
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Statistiche generali */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Riepilogo Operazione</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium">Metri Richiesti</div>
                  <div className="text-2xl font-bold text-blue-600">{debugData.metriTotaliRichiesti}m</div>
                </div>
                <div>
                  <div className="font-medium">Metri Disponibili</div>
                  <div className="text-2xl font-bold text-green-600">{debugData.metriDisponibili}m</div>
                </div>
                <div>
                  <div className="font-medium">Differenza</div>
                  <div className={`text-2xl font-bold ${
                    debugData.metriTotaliRichiesti > debugData.metriDisponibili ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {debugData.metriTotaliRichiesti - debugData.metriDisponibili > 0 ? '+' : ''}
                    {debugData.metriTotaliRichiesti - debugData.metriDisponibili}m
                  </div>
                </div>
                <div>
                  <div className="font-medium">Force Over</div>
                  <div className={`text-2xl font-bold ${forceOver ? 'text-orange-600' : 'text-gray-600'}`}>
                    {forceOver ? 'SÌ' : 'NO'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Problemi e Warning */}
          {(debugData.problemi.length > 0 || debugData.warnings.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Problemi e Avvisi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {debugData.problemi.map((problema, index) => (
                    <div key={index} className="flex items-center gap-2 text-red-600">
                      <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                      <span className="text-sm">{problema}</span>
                    </div>
                  ))}
                  {debugData.warnings.map((warning, index) => (
                    <div key={index} className="flex items-center gap-2 text-orange-600">
                      <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
                      <span className="text-sm">{warning}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Dettagli bobina */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Dettagli Bobina Target</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-5 gap-4 text-sm">
                <div>
                  <div className="font-medium">ID</div>
                  <div>{bobina?.id_bobina}</div>
                </div>
                <div>
                  <div className="font-medium">Tipologia</div>
                  <div>{bobina?.tipologia}</div>
                </div>
                <div>
                  <div className="font-medium">Formazione</div>
                  <div>{bobina?.sezione}</div>
                </div>
                <div>
                  <div className="font-medium">Metri Totali</div>
                  <div>{bobina?.metri_totali}m</div>
                </div>
                <div>
                  <div className="font-medium">Metri Residui</div>
                  <div className={`font-bold ${
                    (bobina?.metri_residui || 0) < debugData.metriTotaliRichiesti ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {bobina?.metri_residui}m
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Analisi cavi */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Analisi Cavi Selezionati</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {debugData.caviAnalisi.map(cavo => (
                  <Card key={cavo.id_cavo} className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <span className="font-medium">{cavo.id_cavo}</span>
                        <div className="text-xs text-gray-500">
                          {cavo.tipologia} - {cavo.sezione} - {cavo.metri_teorici}m teorici
                        </div>
                      </div>
                      <div className="flex gap-1">
                        {cavo.is_compatible ? (
                          <Badge variant="default" className="text-xs">Compatibile</Badge>
                        ) : (
                          <Badge variant="destructive" className="text-xs">Incompatibile</Badge>
                        )}
                        {cavo.metri_posati_attuali > 0 && (
                          <Badge variant="secondary" className="text-xs">Già posato</Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div>
                        <span className="font-medium">Richiesti:</span> {cavo.metri_richiesti}m
                      </div>
                      <div>
                        <span className="font-medium">Attuali:</span> {cavo.metri_posati_attuali}m
                      </div>
                      <div>
                        <span className="font-medium">Bobina:</span> {cavo.bobina_attuale || 'Nessuna'}
                      </div>
                    </div>

                    {(cavo.problemi_cavo.length > 0 || cavo.warnings_cavo.length > 0) && (
                      <div className="mt-2 space-y-1">
                        {cavo.problemi_cavo.map((problema: string, index: number) => (
                          <div key={index} className="text-xs text-red-600">• {problema}</div>
                        ))}
                        {cavo.warnings_cavo.map((warning: string, index: number) => (
                          <div key={index} className="text-xs text-orange-600">• {warning}</div>
                        ))}
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Raccomandazioni */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Raccomandazioni</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                {debugData.forceOverNecessario && !forceOver && (
                  <div className="text-red-600">
                    ⚠️ Attivare "Force Over" per procedere con l'operazione
                  </div>
                )}
                {debugData.metriTotaliRichiesti > debugData.metriDisponibili && (
                  <div className="text-orange-600">
                    ⚠️ La bobina andrà in stato OVER dopo l'operazione
                  </div>
                )}
                {debugData.problemi.length === 0 && debugData.warnings.length === 0 && (
                  <div className="text-green-600">
                    ✅ Operazione può procedere senza problemi
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end">
          <Button onClick={onClose}>Chiudi</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
