(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{24944:(e,r,t)=>{"use strict";t.d(r,{k:()=>n});var a=t(95155);t(12115);var s=t(55863),i=t(59434);function n(e){let{className:r,value:t,...n}=e;return(0,a.jsx)(s.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",r),...n,children:(0,a.jsx)(s.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(t||0),"%)")}})})}},28883:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var a=t(95155);t(12115);var s=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:i,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:r})),...d})}},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,r,t)=>{"use strict";t.d(r,{b:()=>l});var a=t(12115),s=t(63655),i=t(95155),n=a.forwardRef((e,r)=>(0,i.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var l=n},46081:(e,r,t)=>{"use strict";t.d(r,{A:()=>n,q:()=>i});var a=t(12115),s=t(95155);function i(e,r){let t=a.createContext(r),i=e=>{let{children:r,...i}=e,n=a.useMemo(()=>i,Object.values(i));return(0,s.jsx)(t.Provider,{value:n,children:r})};return i.displayName=e+"Provider",[i,function(s){let i=a.useContext(t);if(i)return i;if(void 0!==r)return r;throw Error(`\`${s}\` must be used within \`${e}\``)}]}function n(e,r=[]){let t=[],i=()=>{let r=t.map(e=>a.createContext(e));return function(t){let s=t?.[e]||r;return a.useMemo(()=>({[`__scope${e}`]:{...t,[e]:s}}),[t,s])}};return i.scopeName=e,[function(r,i){let n=a.createContext(i),l=t.length;t=[...t,i];let o=r=>{let{scope:t,children:i,...o}=r,d=t?.[e]?.[l]||n,c=a.useMemo(()=>o,Object.values(o));return(0,s.jsx)(d.Provider,{value:c,children:i})};return o.displayName=r+"Provider",[o,function(t,s){let o=s?.[e]?.[l]||n,d=a.useContext(o);if(d)return d;if(void 0!==i)return i;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=t.reduce((r,{useScope:t,scopeName:a})=>{let s=t(e)[`__scope${a}`];return{...r,...s}},{});return a.useMemo(()=>({[`__scope${r.scopeName}`]:s}),[s])}};return t.scopeName=r.scopeName,t}(i,...r)]}},55365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d});var a=t(95155),s=t(12115),i=t(74466),n=t(59434);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,r)=>{let{className:t,variant:s,...i}=e;return(0,a.jsx)("div",{ref:r,role:"alert",className:(0,n.cn)(l({variant:s}),t),...i})});o.displayName="Alert",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...s})}).displayName="AlertTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...s})});d.displayName="AlertDescription"},55863:(e,r,t)=>{"use strict";t.d(r,{C1:()=>y,bL:()=>j});var a=t(12115),s=t(46081),i=t(63655),n=t(95155),l="Progress",[o,d]=(0,s.A)(l),[c,u]=o(l),m=a.forwardRef((e,r)=>{var t,a,s,l;let{__scopeProgress:o,value:d=null,max:u,getValueLabel:m=v,...x}=e;(u||0===u)&&!g(u)&&console.error((t="".concat(u),a="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=g(u)?u:100;null===d||b(d,p)||console.error((s="".concat(d),l="Progress","Invalid prop `value` of value `".concat(s,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let j=b(d,p)?d:null,y=h(j)?m(j,p):void 0;return(0,n.jsx)(c,{scope:o,value:j,max:p,children:(0,n.jsx)(i.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":h(j)?j:void 0,"aria-valuetext":y,role:"progressbar","data-state":f(j,p),"data-value":null!=j?j:void 0,"data-max":p,...x,ref:r})})});m.displayName=l;var x="ProgressIndicator",p=a.forwardRef((e,r)=>{var t;let{__scopeProgress:a,...s}=e,l=u(x,a);return(0,n.jsx)(i.sG.div,{"data-state":f(l.value,l.max),"data-value":null!=(t=l.value)?t:void 0,"data-max":l.max,...s,ref:r})});function v(e,r){return"".concat(Math.round(e/r*100),"%")}function f(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function h(e){return"number"==typeof e}function g(e){return h(e)&&!isNaN(e)&&e>0}function b(e,r){return h(e)&&!isNaN(e)&&e<=r&&e>=0}p.displayName=x;var j=m,y=p},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(52596),s=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var a=t(95155);t(12115);var s=t(59434);function i(e){let{className:r,type:t,...i}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},63438:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var a=t(95155),s=t(12115),i=t(6874),n=t.n(i),l=t(3493),o=t(35169),d=t(30285),c=t(62523),u=t(85057),m=t(66695),x=t(55365);t(24944);var p=t(28883),v=t(40646),f=t(1243),h=t(75525),g=t(59434);function b(){let[e,r]=(0,s.useState)({email:"",userType:"user"}),[t,i]=(0,s.useState)(!1),[n,l]=(0,s.useState)(null),o=async t=>{t.preventDefault(),i(!0),l(null);try{if(!e.email)throw Error("L'indirizzo email \xe8 obbligatorio");let t=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,user_type:e.userType})}),a=await t.json();if(t.ok&&a.success)l({type:"success",text:"Se l'email \xe8 registrata, riceverai le istruzioni per il reset della password."}),r({email:"",userType:"user"});else throw Error(a.detail||a.message||"Errore durante la richiesta di reset")}catch(e){l({type:"error",text:e instanceof Error?e.message:"Errore durante la richiesta di reset"})}finally{i(!1)}};return(0,a.jsxs)(m.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(m.aR,{className:"space-y-1",children:[(0,a.jsxs)(m.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-mariner-600"}),"Recupera Password"]}),(0,a.jsx)(m.BT,{children:"Inserisci la tua email per ricevere le istruzioni di reset"})]}),(0,a.jsxs)(m.Wu,{children:[(0,a.jsxs)("form",{onSubmit:o,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"Tipo di Account"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",value:"user",checked:"user"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,a.jsx)("span",{children:"Utente"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",value:"cantiere",checked:"cantiere"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,a.jsx)("span",{children:"Cantiere"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"email",children:"Indirizzo Email"}),(0,a.jsx)(c.p,{id:"email",type:"email",value:e.email,onChange:e=>r(r=>({...r,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),n&&(0,a.jsxs)(x.Fc,{className:(0,g.cn)("success"===n.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===n.type?(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(f.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)(x.TN,{className:(0,g.cn)("success"===n.type?"text-green-800":"text-red-800"),children:n.text})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:t,children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Invio in corso..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Invia Link di Reset"]})})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Informazioni sulla Sicurezza"}),(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• Il link di reset \xe8 valido per 30 minuti"}),(0,a.jsx)("li",{children:"• Pu\xf2 essere utilizzato una sola volta"}),(0,a.jsx)("li",{children:"• Se non ricevi l'email, controlla la cartella spam"}),(0,a.jsx)("li",{children:"• Per motivi di sicurezza, non riveleremo se l'email \xe8 registrata"})]})]})]})})]})]})}function j(){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"w-8 h-8 text-white"})})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,a.jsx)("p",{className:"text-slate-600",children:"Recupero Password"})]}),(0,a.jsx)(b,{}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(n(),{href:"/login",children:(0,a.jsxs)(d.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},63655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>l});var a=t(12115),s=t(47650),i=t(99708),n=t(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,i.TL)(`Primitive.${r}`),s=a.forwardRef((e,a)=>{let{asChild:s,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?t:r,{...i,ref:a})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{});function o(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=t(95155);t(12115);var s=t(59434);function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}},75525:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},85057:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var a=t(95155);t(12115);var s=t(40968),i=t(59434);function n(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},88418:(e,r,t)=>{Promise.resolve().then(t.bind(t,63438))}},e=>{var r=r=>e(e.s=r);e.O(0,[3455,541,8441,1684,7358],()=>r(88418)),_N_E=e.O()}]);