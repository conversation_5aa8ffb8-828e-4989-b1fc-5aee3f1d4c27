'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Plus, AlertCircle } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface AggiungiCavoDialogProps {
  open: boolean
  onClose: () => void
  cantiere?: { id_cantiere: string; nome_cantiere: string } | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface CavoFormData {
  id_cavo: string
  utility: string
  sistema: string
  colore_cavo: string
  tipologia: string
  sezione: string
  ubicazione_partenza: string
  utenza_partenza: string
  descrizione_utenza_partenza: string
  ubicazione_arrivo: string
  utenza_arrivo: string
  descrizione_utenza_arrivo: string
  metri_teorici: string
}

const defaultFormData: CavoFormData = {
  id_cavo: '',
  utility: '',
  sistema: '',
  colore_cavo: '',
  tipologia: '',
  sezione: '',
  ubicazione_partenza: '',
  utenza_partenza: '',
  descrizione_utenza_partenza: '',
  ubicazione_arrivo: '',
  utenza_arrivo: '',
  descrizione_utenza_arrivo: '',
  metri_teorici: ''
}

export default function AggiungiCavoDialog({
  open,
  onClose,
  cantiere: cantiereProp,
  onSuccess,
  onError
}: AggiungiCavoDialogProps) {
  const { cantiere: cantiereFromContext } = useAuth()
  const cantiere = cantiereProp || cantiereFromContext

  const [formData, setFormData] = useState<CavoFormData>(defaultFormData)
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Reset form quando il dialog si apre
  useEffect(() => {
    if (open) {
      setFormData(defaultFormData)
      setErrors({})
    }
  }, [open])

  const handleInputChange = (field: keyof CavoFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'id_cavo' ? value.toUpperCase() : value
    }))
    
    // Rimuovi errore quando l'utente inizia a digitare
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Campi obbligatori
    if (!formData.id_cavo.trim()) {
      newErrors.id_cavo = 'ID Cavo è obbligatorio'
    }
    if (!formData.utility.trim()) {
      newErrors.utility = 'Utility è obbligatoria'
    }
    if (!formData.metri_teorici.trim()) {
      newErrors.metri_teorici = 'Metri Teorici sono obbligatori'
    } else {
      const metri = parseFloat(formData.metri_teorici)
      if (isNaN(metri) || metri <= 0) {
        newErrors.metri_teorici = 'Metri Teorici deve essere un numero positivo'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) {
      return
    }

    if (!cantiere?.id_cantiere) {
      onError('Cantiere non selezionato')
      return
    }

    try {
      setLoading(true)

      // Prepara i dati per l'API
      const dataToSubmit = {
        ...formData,
        metri_teorici: parseFloat(formData.metri_teorici),
        id_cantiere: cantiere.id_cantiere
      }

      await caviApi.createCavo(parseInt(cantiere.id_cantiere), dataToSubmit)

      onSuccess(`Cavo ${formData.id_cavo} aggiunto con successo`)
      handleClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\'aggiunta del cavo'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setFormData(defaultFormData)
      setErrors({})
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Aggiungi Nuovo Cavo
          </DialogTitle>
          <DialogDescription>
            Inserisci i dati del nuovo cavo per il cantiere {cantiere?.nome_cantiere}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informazioni Generali */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Informazioni Generali</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="id_cavo">ID Cavo *</Label>
                <Input
                  id="id_cavo"
                  value={formData.id_cavo}
                  onChange={(e) => handleInputChange('id_cavo', e.target.value)}
                  placeholder="Es. C001"
                  className={errors.id_cavo ? 'border-red-500' : ''}
                />
                {errors.id_cavo && (
                  <p className="text-sm text-red-500">{errors.id_cavo}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="utility">Utility *</Label>
                <Input
                  id="utility"
                  value={formData.utility}
                  onChange={(e) => handleInputChange('utility', e.target.value)}
                  placeholder="Es. ENEL"
                  className={errors.utility ? 'border-red-500' : ''}
                />
                {errors.utility && (
                  <p className="text-sm text-red-500">{errors.utility}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="sistema">Sistema</Label>
                <Input
                  id="sistema"
                  value={formData.sistema}
                  onChange={(e) => handleInputChange('sistema', e.target.value)}
                  placeholder="Es. MT"
                />
              </div>
            </div>
          </div>

          {/* Caratteristiche Tecniche */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Caratteristiche Tecniche</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="colore_cavo">Colore Cavo</Label>
                <Input
                  id="colore_cavo"
                  value={formData.colore_cavo}
                  onChange={(e) => handleInputChange('colore_cavo', e.target.value)}
                  placeholder="Es. Nero"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tipologia">Tipologia</Label>
                <Input
                  id="tipologia"
                  value={formData.tipologia}
                  onChange={(e) => handleInputChange('tipologia', e.target.value)}
                  placeholder="Es. ARE4H5E"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="sezione">Formazione</Label>
                <Input
                  id="sezione"
                  value={formData.sezione}
                  onChange={(e) => handleInputChange('sezione', e.target.value)}
                  placeholder="Es. 3X240+120"
                />
              </div>
            </div>
          </div>

          {/* Partenza */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Partenza</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ubicazione_partenza">Ubicazione Partenza</Label>
                <Input
                  id="ubicazione_partenza"
                  value={formData.ubicazione_partenza}
                  onChange={(e) => handleInputChange('ubicazione_partenza', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="utenza_partenza">Utenza Partenza</Label>
                <Input
                  id="utenza_partenza"
                  value={formData.utenza_partenza}
                  onChange={(e) => handleInputChange('utenza_partenza', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="descrizione_utenza_partenza">Descrizione Utenza Partenza</Label>
                <Input
                  id="descrizione_utenza_partenza"
                  value={formData.descrizione_utenza_partenza}
                  onChange={(e) => handleInputChange('descrizione_utenza_partenza', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Arrivo */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Arrivo</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ubicazione_arrivo">Ubicazione Arrivo</Label>
                <Input
                  id="ubicazione_arrivo"
                  value={formData.ubicazione_arrivo}
                  onChange={(e) => handleInputChange('ubicazione_arrivo', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="utenza_arrivo">Utenza Arrivo</Label>
                <Input
                  id="utenza_arrivo"
                  value={formData.utenza_arrivo}
                  onChange={(e) => handleInputChange('utenza_arrivo', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="descrizione_utenza_arrivo">Descrizione Utenza Arrivo</Label>
                <Input
                  id="descrizione_utenza_arrivo"
                  value={formData.descrizione_utenza_arrivo}
                  onChange={(e) => handleInputChange('descrizione_utenza_arrivo', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Metratura */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Metratura</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="metri_teorici">Metri Teorici *</Label>
                <Input
                  id="metri_teorici"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.metri_teorici}
                  onChange={(e) => handleInputChange('metri_teorici', e.target.value)}
                  placeholder="Es. 100.50"
                  className={errors.metri_teorici ? 'border-red-500' : ''}
                />
                {errors.metri_teorici && (
                  <p className="text-sm text-red-500">{errors.metri_teorici}</p>
                )}
              </div>
            </div>
          </div>

          {/* Errori generali */}
          {Object.keys(errors).length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Correggere i campi evidenziati in rosso prima di salvare.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? 'Aggiungendo...' : 'Aggiungi Cavo'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
