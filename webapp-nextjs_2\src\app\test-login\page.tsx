'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

export default function TestLoginPage() {
  const { login, loginCantiere, user, cantiere, isLoading } = useAuth()
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)

  const testUserLogin = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing user login...')
      
      const result = await login('admin', 'admin')
      
      console.log('Login result:', result)
      
      if (result.success && result.user) {
        setResult(`SUCCESS: Login riuscito per ${result.user.username} (${result.user.ruolo})`)
      } else {
        setResult(`ERROR: ${result.error || 'Login fallito'}`)
      }
    } catch (error: any) {
      console.error('Login test error:', error)
      setResult(`EXCEPTION: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testCantiereLogin = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing cantiere login...')
      
      const result = await loginCantiere('TEST123', 'test123')
      
      console.log('Cantiere login result:', result)
      
      if (result.success && result.cantiere) {
        setResult(`SUCCESS: Login cantiere riuscito per ${result.cantiere.commessa}`)
      } else {
        setResult(`ERROR: ${result.error || 'Login cantiere fallito'}`)
      }
    } catch (error: any) {
      console.error('Cantiere login test error:', error)
      setResult(`EXCEPTION: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testDirectAPI = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing direct API call...')
      
      const formData = new FormData()
      formData.append('username', 'admin')
      formData.append('password', 'admin')
      
      const response = await fetch('http://localhost:8001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData
      })

      console.log('Response status:', response.status)
      const data = await response.json()
      console.log('Response data:', data)

      if (response.ok) {
        setResult(`SUCCESS: API diretta funzionante - Token: ${data.access_token.substring(0, 50)}...`)
      } else {
        setResult(`ERROR: ${response.status} - ${JSON.stringify(data, null, 2)}`)
      }
    } catch (error: any) {
      console.error('Direct API test error:', error)
      setResult(`EXCEPTION: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Test Login Sistema</h1>
        
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            <p><strong>Stato Auth:</strong></p>
            <p>Loading: {isLoading ? 'Sì' : 'No'}</p>
            <p>User: {user ? `${user.username} (${user.ruolo})` : 'Nessuno'}</p>
            <p>Cantiere: {cantiere ? cantiere.commessa : 'Nessuno'}</p>
          </div>
          
          <hr />
          
          <button
            onClick={testUserLogin}
            disabled={loading}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Login Utente (admin/admin)'}
          </button>
          
          <button
            onClick={testCantiereLogin}
            disabled={loading}
            className="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Login Cantiere (TEST123/test123)'}
          </button>
          
          <button
            onClick={testDirectAPI}
            disabled={loading}
            className="w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test API Diretta'}
          </button>
          
          {result && (
            <div className={`p-4 rounded ${
              result.startsWith('SUCCESS') 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <pre className="whitespace-pre-wrap text-sm">{result}</pre>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
