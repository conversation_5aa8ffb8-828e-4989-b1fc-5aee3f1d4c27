import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    console.log('🔄 Auth API: Proxying test-token request to backend:', `${backendUrl}/api/auth/test-token`)
    
    const response = await fetch(`${backendUrl}/api/auth/test-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      }
    })

    console.log('📡 Auth API: Backend response status:', response.status)

    const data = await response.json()
    console.log('📡 Auth API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Auth API: Test token error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
