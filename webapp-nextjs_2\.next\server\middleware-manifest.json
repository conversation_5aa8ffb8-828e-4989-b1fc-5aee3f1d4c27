{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Nai6Gg2iDNwuYA9HGBXTaidaLgp3TuN421CBgdpfMQw=", "__NEXT_PREVIEW_MODE_ID": "d1a54cbe359980828b8e701a772c341a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2e4080f87c4c9494e0beeba8b4f5b74ae3ea8f1c305977be33707ee257764cb8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cc9c32c79d3d3468db475dcd37451f5a21aa711fb5e368f4e642a7667a959de6"}}}, "sortedMiddleware": ["/"], "functions": {}}