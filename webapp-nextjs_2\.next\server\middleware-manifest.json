{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Nai6Gg2iDNwuYA9HGBXTaidaLgp3TuN421CBgdpfMQw=", "__NEXT_PREVIEW_MODE_ID": "3d1f8dbc041b0538231b13002a0391db", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d3b682786747268fce637c7a6991bd77191286e6ed55691e840e84e50e2de60e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "51d694317b869fa45290b605cebbfd8117b536f21744db441941ee31d3c7b6ad"}}}, "sortedMiddleware": ["/"], "functions": {}}