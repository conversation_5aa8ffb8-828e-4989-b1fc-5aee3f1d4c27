'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  FileText, 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  Cloud, 
  Link,
  Settings,
  Thermometer,
  Zap
} from 'lucide-react'
import CertificazioneForm from '@/components/certificazioni/CertificazioneForm'

export default function TestFormCertificazionePage() {
  const [showForm, setShowForm] = useState(false)
  const [cantiereId] = useState(1) // Test con cantiere ID 1

  const handleFormSuccess = () => {
    setShowForm(false)
    alert('✅ Certificazione creata con successo!\n\nVerifica che:\n- I dati meteorologici siano stati caricati automaticamente\n- Il collegamento automatico sia stato eseguito se necessario\n- La conformità sia stata determinata automaticamente')
  }

  const handleFormCancel = () => {
    setShowForm(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <TestTube className="h-8 w-8 text-blue-600" />
              Test Form Certificazione CEI 64-8
            </h1>
            <p className="text-slate-600 mt-1">Test delle automazioni e dell'interfaccia utente</p>
          </div>
          
          <Button onClick={() => setShowForm(true)} disabled={showForm}>
            <FileText className="h-4 w-4 mr-2" />
            Apri Form Certificazione
          </Button>
        </div>

        {/* Funzionalità da Testare */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cloud className="h-5 w-5 text-blue-500" />
                Dati Meteorologici Automatici
              </CardTitle>
              <CardDescription>Test del caricamento automatico</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Caricamento automatico all'apertura del form</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Visualizzazione temperatura e umidità</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Precompilazione campi temperatura/umidità</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Indicatore fonte dati (demo/live)</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5 text-green-500" />
                Collegamento Automatico Cavi
              </CardTitle>
              <CardDescription>Test della logica CEI 64-8</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Verifica stato collegamento cavo</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Dialog di conferma se non collegato</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Collegamento automatico a "cantiere"</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Aggiornamento stato cavi</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-purple-500" />
                Validazione Automatica
              </CardTitle>
              <CardDescription>Test della conformità automatica</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Calcolo automatico conformità</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Confronto con valore minimo isolamento</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Impostazione stato certificato</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Esito complessivo automatico</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-orange-500" />
                Interfaccia Utente
              </CardTitle>
              <CardDescription>Test dell'esperienza utente</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Layout responsive e moderno</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Feedback visivo per operazioni</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Validazione campi in tempo reale</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Gestione errori e stati di caricamento</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Istruzioni per il Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Istruzioni per il Test
            </CardTitle>
            <CardDescription>Come testare le funzionalità implementate</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">1. Test Dati Meteorologici</h4>
                <p className="text-blue-800 text-sm">
                  Apri il form e verifica che nella sezione "Condizioni Ambientali" vengano mostrati 
                  automaticamente temperatura e umidità. I campi dovrebbero essere precompilati.
                </p>
              </div>
              
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-semibold text-green-900 mb-2">2. Test Collegamento Automatico</h4>
                <p className="text-green-800 text-sm">
                  Seleziona un cavo non completamente collegato e prova a salvare. 
                  Dovrebbe apparire un dialog di conferma per il collegamento automatico.
                </p>
              </div>
              
              <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <h4 className="font-semibold text-purple-900 mb-2">3. Test Validazione Automatica</h4>
                <p className="text-purple-800 text-sm">
                  Inserisci un valore di isolamento (es. 600 MΩ) e verifica che lo stato 
                  venga automaticamente impostato su "CONFORME" se superiore al minimo.
                </p>
              </div>
              
              <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <h4 className="font-semibold text-orange-900 mb-2">4. Test Interfaccia</h4>
                <p className="text-orange-800 text-sm">
                  Verifica la responsività, i feedback visivi, la validazione dei campi 
                  e la gestione degli stati di caricamento durante le operazioni.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Risultati Attesi */}
        <Card>
          <CardHeader>
            <CardTitle>Risultati Attesi</CardTitle>
            <CardDescription>Cosa dovrebbe succedere durante il test</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  ✅ Dati meteorologici caricati automaticamente
                </Badge>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  ✅ Campi temperatura/umidità precompilati
                </Badge>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  ✅ Dialog conferma collegamento automatico
                </Badge>
              </div>
              <div className="space-y-2">
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  ✅ Stato conformità calcolato automaticamente
                </Badge>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  ✅ Interfaccia responsive e moderna
                </Badge>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  ✅ Feedback visivo per tutte le operazioni
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[95vh] min-w-[800px] overflow-y-auto">
            <div className="p-6">
              <CertificazioneForm
                cantiereId={cantiereId}
                certificazione={null}
                strumenti={[]}
                onSuccess={handleFormSuccess}
                onCancel={handleFormCancel}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
