
> webapp-nextjs@0.1.0 build
> next build

   ▲ Next.js 15.3.3
   - Environments: .env.local

   Creating an optimized production build ...
 ✓ Compiled successfully in 3.0s
   Skipping validation of types
   Skipping linting
   Collecting page data ...
   Generating static pages (0/23) ...
 ⚠ Unsupported metadata themeColor is configured in metadata export in /cavi. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /cavi. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
{ cantiereFromAuth: null, user: null, userRole: undefined }
 ⚠ Unsupported metadata themeColor is configured in metadata export in /forgot-password. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /forgot-password. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
   Generating static pages (5/23) 
 ⚠ Unsupported metadata themeColor is configured in metadata export in /cantieri. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /cantieri. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /_not-found. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /_not-found. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /comande. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /comande. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
   Generating static pages (11/23) 
 ⚠ Unsupported metadata themeColor is configured in metadata export in /productivity. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /productivity. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /reset-password. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /reset-password. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /reports. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /reports. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
   Generating static pages (17/23) 
 ⚠ Unsupported metadata themeColor is configured in metadata export in /certificazioni. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /certificazioni. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /parco-cavi. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /parco-cavi. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata themeColor is configured in metadata export in /admin. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /admin. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ✓ Generating static pages (23/23)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                  Size  First Load JS
┌ ○ /                                       789 B         125 kB
├ ○ /_not-found                             986 B         103 kB
├ ○ /admin                                14.6 kB         179 kB
├ ƒ /api/cavi/bulk-delete                   154 B         102 kB
├ ƒ /api/cavi/bulk-status                   154 B         102 kB
├ ƒ /api/cavi/export                        154 B         102 kB
├ ƒ /api/password/confirm-password-reset    154 B         102 kB
├ ƒ /api/password/request-password-reset    154 B         102 kB
├ ƒ /api/password/validate-password         154 B         102 kB
├ ƒ /api/password/verify-reset-token        154 B         102 kB
├ ○ /cantieri                             9.25 kB         153 kB
├ ƒ /cantieri/[id]                        1.91 kB         136 kB
├ ○ /cavi                                 24.3 kB         193 kB
├ ○ /certificazioni                       5.27 kB         139 kB
├ ○ /comande                              14.7 kB         183 kB
├ ○ /forgot-password                      5.44 kB         120 kB
├ ○ /login                                6.79 kB         141 kB
├ ○ /parco-cavi                           19.3 kB         184 kB
├ ○ /productivity                         5.08 kB         116 kB
├ ○ /reports                               114 kB         249 kB
└ ○ /reset-password                       6.66 kB         121 kB
+ First Load JS shared by all              102 kB
  ├ chunks/4bd1b696-9e4e061d3aa2f8a5.js   53.2 kB
  ├ chunks/684-944669dad5902656.js        46.4 kB
  └ other shared chunks (total)           2.04 kB


ƒ Middleware                              33.8 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

