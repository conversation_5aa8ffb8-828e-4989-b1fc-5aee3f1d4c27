(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{16786:(e,a,s)=>{Promise.resolve().then(s.bind(s,93862))},17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>n});var t=s(95155),i=s(12115),r=s(30064),l=s(59434);let n=r.bL,c=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)(r.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...i})});c.displayName=r.B8.displayName;let d=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)(r.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...i})});d.displayName=r.l9.displayName;let o=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)(r.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...i})});o.displayName=r.UC.displayName},27883:(e,a,s)=>{"use strict";s.d(a,{Qi:()=>o,eU:()=>m,jn:()=>c,tA:()=>d});var t=s(95155),i=s(12115),r=s(59434),l=s(51154);let n=i.forwardRef((e,a)=>{let{className:s,variant:i="primary",size:n="md",loading:c=!1,glow:d=!1,icon:o,children:m,disabled:x,...u}=e,h=x||c;return(0,t.jsxs)("button",{className:(0,r.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[i],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[n],d&&"quick"!==i&&"btn-glow",h&&"opacity-50 cursor-not-allowed hover:shadow-none",s),disabled:h,ref:a,...u,children:[(0,t.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,t.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[c?(0,t.jsx)(l.A,{className:"h-4 w-4 animate-spin btn-icon"}):o?(0,t.jsx)("span",{className:"btn-icon",children:o}):null,m]})]})});n.displayName="AnimatedButton";let c=e=>(0,t.jsx)(n,{variant:"primary",...e}),d=e=>(0,t.jsx)(n,{variant:"secondary",...e}),o=e=>(0,t.jsx)(n,{variant:"danger",...e}),m=e=>(0,t.jsx)(n,{variant:"quick",...e})},47262:(e,a,s)=>{"use strict";s.d(a,{S:()=>n});var t=s(95155);s(12115);var i=s(76981),r=s(5196),l=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(r.A,{className:"size-3.5"})})})}},61610:(e,a,s)=>{"use strict";s.d(a,{Eb:()=>h,GN:()=>p});let t=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,i=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,r=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(t,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},c=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},d=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},o=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:255;return l(e).length>a?{isValid:!1,error:"Testo troppo lungo (max ".concat(a," caratteri)")}:r.test(e)||i.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0}},m=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},x=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},u=new Map,h=(e,a,s)=>{let t=Date.now(),i=u.get(e);return!i||t>i.resetTime?(u.set(e,{count:1,resetTime:t+s}),!0):!(i.count>=a)&&(i.count++,!0)},p=e=>{let a={},s=n(e.username);if(s.isValid||(a.username=s.error),e.password){let s=c(e.password);s.isValid||(a.password=s.error)}let t=m(e.ragione_sociale);if(t.isValid||(a.ragione_sociale=t.error),e.email){let s=d(e.email);s.isValid||(a.email=s.error)}if(e.vat){let s=x(e.vat);s.isValid||(a.vat=s.error)}if(e.indirizzo){let s=o(e.indirizzo,200);s.isValid||(a.indirizzo=s.error)}if(e.nazione){let s=o(e.nazione,50);s.isValid||(a.nazione=s.error)}if(e.referente_aziendale){let s=o(e.referente_aziendale,100);s.isValid||(a.referente_aziendale=s.error)}return{isValid:0===Object.keys(a).length,errors:a}}},93862:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>q});var t=s(95155),i=s(12115),r=s(66695),l=s(27883),n=s(13717),c=s(14186),d=s(40646),o=s(62525);function m(e){let{user:a,onEdit:s,onToggleStatus:i,onDelete:r}=e;return(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),s()},type:"button",className:"p-1.5 rounded hover:bg-blue-50 transition-colors",title:"Modifica utente",children:(0,t.jsx)(n.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),i()},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded transition-colors ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-slate-50"),title:a.abilitato?"Disabilita utente":"Abilita utente",children:a.abilitato?(0,t.jsx)(c.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),r()},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded transition-colors ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-red-50"),title:"Elimina utente",children:(0,t.jsx)(o.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})})]})}var x=s(26126),u=s(63743),h=s(85127),p=s(17313),j=s(40283),g=s(35695),v=s(25731),N=s(30285),b=s(62523),f=s(85057),w=s(59409),z=s(47262),y=s(61610),A=s(78749),E=s(92657),S=s(54416),C=s(4229);function T(e){let{user:a,onSave:s,onCancel:n}=e,[c,d]=(0,i.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[o,m]=(0,i.useState)({}),[x,u]=(0,i.useState)(!1),[h,p]=(0,i.useState)(""),[j,g]=(0,i.useState)(!1);(0,i.useEffect)(()=>{a&&d({username:a.username||"",password:"",ruolo:a.ruolo||"user",data_scadenza:a.data_scadenza?a.data_scadenza.split("T")[0]:"",abilitato:void 0===a.abilitato||a.abilitato,ragione_sociale:a.ragione_sociale||"",indirizzo:a.indirizzo||"",nazione:a.nazione||"",email:a.email||"",vat:a.vat||"",referente_aziendale:a.referente_aziendale||""})},[a]);let T=(e,a)=>{d(s=>({...s,[e]:a})),o[e]&&m(a=>({...a,[e]:""}))},_=()=>{let e=(0,y.GN)({username:c.username,password:a?void 0:c.password,ragione_sociale:c.ragione_sociale,email:c.email,vat:c.vat,indirizzo:c.indirizzo,nazione:c.nazione,referente_aziendale:c.referente_aziendale});return m(e.errors),e.isValid},R=async e=>{e.preventDefault();let t="user-form-".concat((null==a?void 0:a.id_utente)||"new","-").concat(Date.now());if(!(0,y.Eb)(t,5,6e4))return void p("Troppi tentativi. Riprova tra un minuto.");if(_()){u(!0),p("");try{let e,t={...c};a||(t.ruolo="user"),a&&!t.password.trim()&&delete t.password,t.data_scadenza&&(t.data_scadenza=t.data_scadenza),e=a?await v.dG.updateUser(a.id_utente,t):await v.dG.createUser(t),s(e)}catch(e){var i,r;p((null==(r=e.response)||null==(i=r.data)?void 0:i.detail)||e.message||"Errore durante il salvataggio dell'utente")}finally{u(!1)}}};return(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:a?"Modifica Utente: ".concat(a.username):"Crea Nuovo Utente Standard"})}),(0,t.jsxs)(r.Wu,{children:[h&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,t.jsx)("p",{className:"text-red-600",children:h})}),(0,t.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"username",children:"Username *"}),(0,t.jsx)(b.p,{id:"username",value:c.username,onChange:e=>T("username",e.target.value),disabled:x,className:o.username?"border-red-500":""}),o.username&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:o.username})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"password",children:a?"Nuova Password (lascia vuoto per non modificare)":"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b.p,{id:"password",type:j?"text":"password",value:c.password,onChange:e=>T("password",e.target.value),disabled:x,className:o.password?"border-red-500 pr-10":"pr-10"}),(0,t.jsx)(N.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>g(!j),disabled:x,children:j?(0,t.jsx)(A.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(E.A,{className:"h-4 w-4 text-gray-400"})})]}),o.password&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:o.password})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"ragione_sociale",children:"Ragione Sociale *"}),(0,t.jsx)(b.p,{id:"ragione_sociale",value:c.ragione_sociale,onChange:e=>T("ragione_sociale",e.target.value),disabled:x,className:o.ragione_sociale?"border-red-500":""}),o.ragione_sociale&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:o.ragione_sociale})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(b.p,{id:"email",type:"email",value:c.email,onChange:e=>T("email",e.target.value),disabled:x,className:o.email?"border-red-500":""}),o.email&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:o.email})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"indirizzo",children:"Indirizzo"}),(0,t.jsx)(b.p,{id:"indirizzo",value:c.indirizzo,onChange:e=>T("indirizzo",e.target.value),disabled:x})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"nazione",children:"Nazione"}),(0,t.jsx)(b.p,{id:"nazione",value:c.nazione,onChange:e=>T("nazione",e.target.value),disabled:x})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"vat",children:"VAT"}),(0,t.jsx)(b.p,{id:"vat",value:c.vat,onChange:e=>T("vat",e.target.value),disabled:x})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,t.jsx)(b.p,{id:"referente_aziendale",value:c.referente_aziendale,onChange:e=>T("referente_aziendale",e.target.value),disabled:x})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,t.jsx)(b.p,{id:"data_scadenza",type:"date",value:c.data_scadenza,onChange:e=>T("data_scadenza",e.target.value),disabled:x})]}),a?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,t.jsxs)(w.l6,{value:c.ruolo,onValueChange:e=>T("ruolo",e),disabled:x,children:[(0,t.jsx)(w.bq,{children:(0,t.jsx)(w.yv,{})}),(0,t.jsxs)(w.gC,{children:[(0,t.jsx)(w.eb,{value:"user",children:"User"}),(0,t.jsx)(w.eb,{value:"cantieri_user",children:"Cantieri User"})]})]})]}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,t.jsx)("div",{className:"px-3 py-2 bg-slate-50 border border-slate-200 rounded-md text-sm text-slate-600",children:"User (Standard)"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(z.S,{id:"abilitato",checked:c.abilitato,onCheckedChange:e=>T("abilitato",e),disabled:x||a&&"owner"===a.ruolo}),(0,t.jsx)(f.J,{htmlFor:"abilitato",children:"Utente abilitato"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,t.jsx)(l.tA,{type:"button",onClick:n,disabled:x,icon:(0,t.jsx)(S.A,{className:"h-4 w-4"}),children:"Annulla"}),(0,t.jsx)(l.jn,{type:"submit",loading:x,icon:(0,t.jsx)(C.A,{className:"h-4 w-4"}),glow:!0,children:x?"Salvataggio...":"Salva"})]})]})]})]})}var _=s(54213),R=s(53904),k=s(51154);function D(){let[e,a]=(0,i.useState)(null),[s,n]=(0,i.useState)(!1),[c,d]=(0,i.useState)(""),o=async()=>{n(!0),d("");try{let e=await v.dG.getDatabaseData();a(e)}catch(a){var e,s;d((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati del database")}finally{n(!1)}};(0,i.useEffect)(()=>{o()},[]);let m=(e,a,s)=>{if(!a||0===a.length)return(0,t.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",s]});let i=Object.keys(a[0]);return(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,t.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900",children:s}),(0,t.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,t.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,t.jsxs)(h.XI,{children:[(0,t.jsx)(h.A0,{className:"sticky top-0 bg-slate-50",children:(0,t.jsx)(h.Hj,{children:i.map(e=>(0,t.jsx)(h.nd,{className:"font-medium",children:e},e))})}),(0,t.jsx)(h.BF,{children:a.map((e,a)=>(0,t.jsx)(h.Hj,{children:i.map(a=>(0,t.jsx)(h.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,t.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},x=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(_.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,t.jsx)(l.jn,{size:"sm",onClick:o,loading:s,icon:(0,t.jsx)(R.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,t.jsxs)(r.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(E.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),s?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)(k.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,t.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):c?(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,t.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,t.jsx)("p",{className:"text-red-600",children:c})]}):e?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),x.map(a=>e[a.key]&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),m(a.key,e[a.key],a.title)]},a.key)),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:x.map(a=>(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,t.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,t.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var V=s(40133),U=s(1243);function I(){let[e,a]=(0,i.useState)(""),[s,n]=(0,i.useState)(!1),[c,d]=(0,i.useState)(!1),[m,x]=(0,i.useState)(""),[u,h]=(0,i.useState)(""),p=async()=>{if("RESET DATABASE"!==e||!s)return void x("Conferma richiesta per procedere con il reset");d(!0),x(""),h("");try{await v.dG.resetDatabase(),h("Database resettato con successo! Tutti i dati sono stati eliminati."),a(""),n(!1)}catch(e){var t,i;x((null==(i=e.response)||null==(t=i.data)?void 0:t.detail)||e.message||"Errore durante il reset del database")}finally{d(!1)}},j="RESET DATABASE"===e&&s&&!c;return(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),"Reset Database"]})}),(0,t.jsxs)(r.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(U.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,t.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,t.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,t.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,t.jsx)("li",{children:"Tutti i cavi installati"}),(0,t.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,t.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,t.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,t.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),m&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:m})}),u&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-green-600",children:u})}),(0,t.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,t.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(f.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,t.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,t.jsx)(b.p,{id:"confirm-text",value:e,onChange:e=>a(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:c,className:"RESET DATABASE"===e?"border-green-500":""})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(z.S,{id:"confirm-checkbox",checked:s,onCheckedChange:n,disabled:c}),(0,t.jsx)(f.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Stato Conferma:"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("RESET DATABASE"===e?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===e?"✓ Corretto":"✗ Richiesto"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(s?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Checkbox confermata: ",s?"✓ S\xec":"✗ Richiesta"]})]})]})]}),(0,t.jsx)(l.Qi,{onClick:p,disabled:!j,className:"w-full",size:"lg",loading:c,icon:(0,t.jsx)(o.A,{className:"h-5 w-5"}),glow:!0,children:c?"Reset in corso...":"RESET DATABASE - ELIMINA TUTTI I DATI"}),!j&&(0,t.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per abilitare il reset"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,t.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,t.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,t.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,t.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,t.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var L=s(3493),P=s(43332),Z=s(48136),B=s(57434),G=s(84616);function F(){let[e,a]=(0,i.useState)("categorie");return(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(L.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,t.jsxs)(r.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(L.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,t.jsxs)(p.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(p.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Categorie"]}),(0,t.jsxs)(p.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),"Produttori"]}),(0,t.jsxs)(p.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,t.jsx)(B.A,{className:"h-4 w-4"}),"Standard"]}),(0,t.jsxs)(p.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,t.jsx)(L.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,t.jsxs)(p.av,{value:"categorie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,t.jsxs)(N.$,{children:[(0,t.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(P.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,t.jsxs)(p.av,{value:"produttori",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,t.jsxs)(N.$,{children:[(0,t.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(Z.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,t.jsxs)(p.av,{value:"standard",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,t.jsxs)(N.$,{children:[(0,t.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(B.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,t.jsxs)(p.av,{value:"tipologie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,t.jsxs)(N.$,{children:[(0,t.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(L.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,t.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var O=s(17580),J=s(12318),X=s(70306);function q(){let e=(0,g.useRouter)(),[a,s]=(0,i.useState)("visualizza-utenti"),[c,d]=(0,i.useState)(""),[o,N]=(0,i.useState)([]),[b,f]=(0,i.useState)([]),[w,z]=(0,i.useState)(!0),[y,A]=(0,i.useState)(""),[E,S]=(0,i.useState)(null),[C,R]=(0,i.useState)({open:!1,message:"",severity:"success"}),{user:U,impersonateUser:P}=(0,j.A)();(0,i.useEffect)(()=>{Z()},[a]);let Z=async()=>{try{if(z(!0),A(""),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){let e=await v.dG.getUsers();N(e)}else if("cantieri"===a){let e=await v._I.getCantieri();f(e)}}catch(a){var e,s;A((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati")}finally{z(!1)}},B=e=>{S(e),s("modifica-utente")},G=async e=>{try{await v.dG.toggleUserStatus(e),Z()}catch(e){var a,s;A((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante la modifica dello stato utente")}},q=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await v.dG.deleteUser(e),Z()}catch(e){var a,s;A((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante l'eliminazione dell'utente")}},$=e=>{S(null),s("visualizza-utenti"),Z()},Q=()=>{S(null),s("visualizza-utenti")},M=async a=>{try{await P(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){var s,t;A((null==(t=e.response)||null==(s=t.data)?void 0:s.detail)||e.message||"Errore durante l'impersonificazione")}},H=e=>{let a="NEUTRAL";switch(e){case"owner":a="PROGRESS";break;case"user":a="INFO";break;case"cantieri_user":a="SUCCESS";break;default:a="NEUTRAL"}let s=(0,u.qn)(a);return(0,t.jsx)(x.E,{className:s.badge,children:e})},W=(e,a)=>{let s="SUCCESS",i="Attivo";if(e){if(a){let e=new Date(a),t=new Date;e<t?(s="ERROR",i="Scaduto"):e.getTime()-t.getTime()<6048e5&&(s="WARNING",i="In Scadenza")}}else s="ERROR",i="Disabilitato";let r=(0,u.qn)(s);return(0,t.jsx)(x.E,{className:r.badge,children:i})};return(o.filter(e=>{var a,s,t;return(null==(a=e.username)?void 0:a.toLowerCase().includes(c.toLowerCase()))||(null==(s=e.ragione_sociale)?void 0:s.toLowerCase().includes(c.toLowerCase()))||(null==(t=e.email)?void 0:t.toLowerCase().includes(c.toLowerCase()))}),U&&"owner"===U.ruolo)?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,t.jsxs)(p.tU,{value:a,onValueChange:s,className:"w-full",children:[(0,t.jsxs)(p.j7,{className:"grid w-full ".concat(E?"grid-cols-6":"grid-cols-5"),children:[(0,t.jsxs)(p.Xi,{value:"visualizza-utenti",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)(O.A,{className:"h-4 w-4"}),"Visualizza Utenti"]}),(0,t.jsxs)(p.Xi,{value:"crea-utente",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)(J.A,{className:"h-4 w-4"}),"Crea Nuovo Utente"]}),E&&(0,t.jsxs)(p.Xi,{value:"modifica-utente",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),"Modifica Utente"]}),(0,t.jsxs)(p.Xi,{value:"database-tipologie-cavi",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)(L.A,{className:"h-4 w-4"}),"Database Tipologie Cavi"]}),(0,t.jsxs)(p.Xi,{value:"visualizza-database-raw",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)(_.A,{className:"h-4 w-4"}),"Visualizza Database Raw"]}),(0,t.jsxs)(p.Xi,{value:"reset-database",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),"Reset Database"]})]}),(0,t.jsxs)(p.av,{value:"visualizza-utenti",className:"space-y-4",children:[y&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:y})}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Lista Utenti"})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(h.XI,{className:"min-w-full",children:[(0,t.jsx)(h.A0,{children:(0,t.jsxs)(h.Hj,{children:[(0,t.jsx)(h.nd,{className:"w-[60px] text-center",children:"ID"}),(0,t.jsx)(h.nd,{className:"w-[120px]",children:"Username"}),(0,t.jsx)(h.nd,{className:"w-[100px] text-center",children:"Password"}),(0,t.jsx)(h.nd,{className:"w-[100px] text-center",children:"Ruolo"}),(0,t.jsx)(h.nd,{className:"w-[250px]",children:"Ragione Sociale"}),(0,t.jsx)(h.nd,{className:"w-[200px]",children:"Email"}),(0,t.jsx)(h.nd,{className:"w-[120px] text-center",children:"VAT"}),(0,t.jsx)(h.nd,{className:"w-[100px] text-center",children:"Nazione"}),(0,t.jsx)(h.nd,{className:"w-[150px]",children:"Referente"}),(0,t.jsx)(h.nd,{className:"w-[100px] text-center",children:"Scadenza"}),(0,t.jsx)(h.nd,{className:"w-[100px] text-center",children:"Stato"}),(0,t.jsx)(h.nd,{className:"w-[120px] text-center",children:"Azioni"})]})}),(0,t.jsx)(h.BF,{children:w?(0,t.jsx)(h.Hj,{children:(0,t.jsx)(h.nA,{colSpan:12,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===o.length?(0,t.jsx)(h.Hj,{children:(0,t.jsx)(h.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):o.map(e=>(0,t.jsxs)(h.Hj,{className:"hover:bg-slate-50",children:[(0,t.jsx)(h.nA,{className:"text-center text-slate-500 text-sm font-mono",children:e.id_utente}),(0,t.jsx)(h.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,t.jsx)(h.nA,{className:"text-center font-mono text-xs text-slate-500",children:e.password_plain||"***"}),(0,t.jsx)(h.nA,{className:"text-center",children:H(e.ruolo)}),(0,t.jsx)(h.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,t.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,t.jsx)(h.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,t.jsx)(h.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,t.jsx)(h.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,t.jsx)(h.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,t.jsx)(h.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,t.jsx)(h.nA,{className:"text-center",children:W(e.abilitato,e.data_scadenza)}),(0,t.jsx)(h.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(m,{user:e,onEdit:()=>B(e),onToggleStatus:()=>G(e.id_utente),onDelete:()=>q(e.id_utente)}),(0,t.jsx)(l.jn,{size:"sm",onClick:()=>M(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,t.jsx)(X.A,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,t.jsx)(p.av,{value:"crea-utente",className:"space-y-4",children:(0,t.jsx)(T,{user:null,onSave:$,onCancel:Q})}),E&&(0,t.jsx)(p.av,{value:"modifica-utente",className:"space-y-4",children:(0,t.jsx)(T,{user:E,onSave:$,onCancel:Q})}),(0,t.jsx)(p.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,t.jsx)(F,{})}),(0,t.jsx)(p.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,t.jsx)(D,{})}),(0,t.jsx)(p.av,{value:"reset-database",className:"space-y-4",children:(0,t.jsx)(I,{})})]})})}):(window.location.replace("/login"),null)}}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3455,5585,546,1807,4674,283,1642,8441,1684,7358],()=>a(16786)),_N_E=e.O()}]);