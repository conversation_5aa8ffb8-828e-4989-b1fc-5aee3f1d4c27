/**
 * Utility per la gestione degli stati delle bobine e dei cavi
 * Implementa le stesse regole della webapp originale
 */

// Stati del cavo
export const CABLE_STATES = {
  DA_INSTALLARE: 'Da installare',
  IN_CORSO: 'In corso',
  INSTALLATO: 'Installato',
  SPARE: 'SPARE'
} as const

export type CableState = typeof CABLE_STATES[keyof typeof CABLE_STATES]

// Stati della bobina
export const REEL_STATES = {
  DISPONIBILE: 'Disponibile',
  IN_USO: 'In uso',
  TERMINATA: 'Terminata',
  OVER: 'Over'
} as const

export type ReelState = typeof REEL_STATES[keyof typeof REEL_STATES]

/**
 * Verifica se un cavo è già installato
 * Implementa la stessa logica della webapp originale
 * @param cavo - Oggetto cavo
 * @returns True se il cavo è già installato, false altrimenti
 */
export const isCableInstalled = (cavo: any): boolean => {
  return cavo.stato_installazione === CABLE_STATES.INSTALLATO ||
         (cavo.metratura_reale && parseFloat(cavo.metratura_reale.toString()) > 0)
}

/**
 * Verifica se un cavo è in stato SPARE
 * @param cavo - Oggetto cavo
 * @returns True se il cavo è in stato SPARE, false altrimenti
 */
export const isCableSpare = (cavo: any): boolean => {
  return cavo.modificato_manualmente === 3 || cavo.stato_installazione === CABLE_STATES.SPARE
}

/**
 * Verifica la compatibilità tra cavo e bobina
 * Implementa la stessa logica della webapp originale
 * @param cavo - Oggetto cavo
 * @param bobina - Oggetto bobina
 * @returns True se compatibili, false altrimenti
 */
export const isCompatible = (cavo: any, bobina: any): boolean => {
  return cavo.tipologia === bobina.tipologia &&
         String(cavo.sezione) === String(bobina.sezione)
}

/**
 * Determina lo stato di una bobina in base ai metri residui e totali
 * @param metriResidui - Metri residui
 * @param metriTotali - Metri totali
 * @returns Stato della bobina
 */
export const determineReelState = (metriResidui: number, metriTotali: number): ReelState => {
  if (metriResidui < 0) {
    return REEL_STATES.OVER
  }

  if (metriResidui === 0) {
    return REEL_STATES.TERMINATA
  }

  if (metriResidui < metriTotali) {
    return REEL_STATES.IN_USO
  }

  return REEL_STATES.DISPONIBILE
}

/**
 * Verifica se una bobina può essere modificata in base al suo stato
 * @param statoBobina - Stato della bobina
 * @returns True se la bobina può essere modificata, false altrimenti
 */
export const canModifyReel = (statoBobina: string): boolean => {
  // Una bobina può essere modificata se:
  // 1. È in stato DISPONIBILE
  // 2. È in stato IN_USO
  // 3. Non è in stato TERMINATA o OVER
  return statoBobina === REEL_STATES.DISPONIBILE || 
         statoBobina === REEL_STATES.IN_USO
}

/**
 * Ottiene il colore associato a uno stato della bobina per i badge
 * @param stato - Stato della bobina
 * @returns Classi CSS per il colore del badge
 */
export const getReelStateColor = (stato: string): string => {
  switch (stato) {
    case REEL_STATES.DISPONIBILE:
      return 'bg-green-100 text-green-800'
    case REEL_STATES.IN_USO:
      return 'bg-yellow-100 text-yellow-800'
    case REEL_STATES.TERMINATA:
      return 'bg-red-100 text-red-800'
    case REEL_STATES.OVER:
      return 'bg-red-500 text-white'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

/**
 * Ottiene il colore di sfondo per le righe della tabella in base allo stato
 * @param stato - Stato della bobina
 * @returns Classi CSS per il colore di sfondo
 */
export const getReelRowColor = (stato: string): string => {
  switch (stato) {
    case REEL_STATES.DISPONIBILE:
      return 'hover:bg-green-50'
    case REEL_STATES.IN_USO:
      return 'hover:bg-yellow-50'
    case REEL_STATES.TERMINATA:
      return 'hover:bg-red-50'
    case REEL_STATES.OVER:
      return 'hover:bg-red-100'
    default:
      return 'hover:bg-gray-50'
  }
}

/**
 * Verifica se una bobina è utilizzabile per nuove installazioni
 * @param statoBobina - Stato della bobina
 * @param metriResidui - Metri residui
 * @returns True se la bobina è utilizzabile
 */
export const isReelUsable = (statoBobina: string, metriResidui: number): boolean => {
  return (statoBobina === REEL_STATES.DISPONIBILE || statoBobina === REEL_STATES.IN_USO) &&
         metriResidui > 0
}

/**
 * Verifica se una bobina può accettare nuovi cavi senza andare OVER
 * Una bobina OVER non può accettare nessun nuovo cavo
 * @param statoBobina - Stato della bobina
 * @returns True se la bobina può accettare nuovi cavi
 */
export const canReelAcceptNewCables = (statoBobina: string): boolean => {
  // Una bobina OVER o TERMINATA non può accettare nuovi cavi
  return statoBobina !== REEL_STATES.OVER && statoBobina !== REEL_STATES.TERMINATA
}

/**
 * Verifica se l'aggiunta di metri causerebbe lo stato OVER
 * @param metriResidui - Metri residui attuali
 * @param metriDaAggiungere - Metri da sottrarre (posare)
 * @returns True se l'operazione causerebbe stato OVER
 */
export const wouldCauseOverState = (metriResidui: number, metriDaAggiungere: number): boolean => {
  return (metriResidui - metriDaAggiungere) < 0
}

/**
 * Verifica se una bobina può accettare una specifica quantità di metri
 * Implementa la logica OVER corretta: una bobina OVER non può accettare nessun nuovo cavo
 * @param statoBobina - Stato attuale della bobina
 * @param metriResidui - Metri residui attuali
 * @param metriRichiesti - Metri richiesti per il nuovo cavo
 * @returns Object con risultato e messaggio di errore se applicabile
 */
export const canReelAcceptMeters = (
  statoBobina: string,
  metriResidui: number,
  metriRichiesti: number
): { canAccept: boolean; reason?: string } => {
  // Controllo 1: Bobina già OVER - interdetta da qualsiasi operazione
  if (statoBobina === REEL_STATES.OVER) {
    return {
      canAccept: false,
      reason: 'La bobina è in stato OVER e non può accettare nuovi cavi'
    }
  }

  // Controllo 2: Bobina terminata
  if (statoBobina === REEL_STATES.TERMINATA) {
    return {
      canAccept: false,
      reason: 'La bobina è terminata e non può accettare nuovi cavi'
    }
  }

  // Controllo 3: Metri residui insufficienti (senza force_over)
  if (metriResidui <= 0) {
    return {
      canAccept: false,
      reason: 'La bobina non ha metri residui disponibili'
    }
  }

  // Controllo 4: L'operazione causerebbe OVER
  if (wouldCauseOverState(metriResidui, metriRichiesti)) {
    return {
      canAccept: false,
      reason: `Metri insufficienti: disponibili ${metriResidui}m, richiesti ${metriRichiesti}m. L'operazione causerebbe stato OVER.`
    }
  }

  return { canAccept: true }
}

/**
 * Valida se una lista di cavi può essere aggiunta a una bobina
 * Implementa la logica OVER: blocca se la bobina è già OVER o se l'operazione causerebbe OVER
 * @param statoBobina - Stato attuale della bobina
 * @param metriResidui - Metri residui attuali
 * @param caviConMetri - Array di oggetti con metri richiesti per ogni cavo
 * @returns Object con risultato e dettagli degli errori
 */
export const validateMultipleCablesForReel = (
  statoBobina: string,
  metriResidui: number,
  caviConMetri: Array<{ id: string; metri: number }>
): {
  isValid: boolean;
  errors: Array<{ cavoId: string; error: string }>;
  totalMetriRichiesti: number;
  metriResiduiDopo: number;
} => {
  const errors: Array<{ cavoId: string; error: string }> = []
  const totalMetriRichiesti = caviConMetri.reduce((sum, cavo) => sum + cavo.metri, 0)
  const metriResiduiDopo = metriResidui - totalMetriRichiesti

  // Controllo globale: bobina già OVER
  if (statoBobina === REEL_STATES.OVER) {
    caviConMetri.forEach(cavo => {
      errors.push({
        cavoId: cavo.id,
        error: 'La bobina è in stato OVER e non può accettare nuovi cavi'
      })
    })
    return {
      isValid: false,
      errors,
      totalMetriRichiesti,
      metriResiduiDopo
    }
  }

  // Controllo globale: bobina terminata
  if (statoBobina === REEL_STATES.TERMINATA) {
    caviConMetri.forEach(cavo => {
      errors.push({
        cavoId: cavo.id,
        error: 'La bobina è terminata e non può accettare nuovi cavi'
      })
    })
    return {
      isValid: false,
      errors,
      totalMetriRichiesti,
      metriResiduiDopo
    }
  }

  // Controllo: l'operazione totale causerebbe OVER
  if (metriResiduiDopo < 0) {
    errors.push({
      cavoId: 'TOTALE',
      error: `Operazione bloccata: metri totali richiesti (${totalMetriRichiesti}m) superano i metri disponibili (${metriResidui}m). Questo causerebbe stato OVER.`
    })
  }

  // Validazione individuale di ogni cavo
  let metriResiduiSimulati = metriResidui
  for (const cavo of caviConMetri) {
    if (cavo.metri <= 0) {
      errors.push({
        cavoId: cavo.id,
        error: 'I metri devono essere maggiori di zero'
      })
      continue
    }

    // Simula l'aggiunta progressiva per verificare quando si raggiunge OVER
    if (metriResiduiSimulati - cavo.metri < 0 && errors.length === 0) {
      errors.push({
        cavoId: cavo.id,
        error: `Questo cavo causerebbe stato OVER (disponibili: ${metriResiduiSimulati}m, richiesti: ${cavo.metri}m)`
      })
    }

    metriResiduiSimulati -= cavo.metri
  }

  return {
    isValid: errors.length === 0,
    errors,
    totalMetriRichiesti,
    metriResiduiDopo
  }
}

/**
 * Calcola la percentuale di utilizzo di una bobina
 * @param metriResidui - Metri residui
 * @param metriTotali - Metri totali
 * @returns Percentuale di utilizzo (0-100)
 */
export const calculateReelUsagePercentage = (metriResidui: number, metriTotali: number): number => {
  if (metriTotali <= 0) return 0
  const utilizzo = ((metriTotali - metriResidui) / metriTotali) * 100
  return Math.max(0, Math.min(100, utilizzo))
}

/**
 * Formatta i metri per la visualizzazione
 * @param metri - Metri da formattare
 * @returns Stringa formattata con unità
 */
export const formatMeters = (metri: number): string => {
  return `${metri.toFixed(1)}m`
}

/**
 * Ottiene una descrizione testuale dello stato della bobina
 * @param stato - Stato della bobina
 * @returns Descrizione dello stato
 */
export const getReelStateDescription = (stato: string): string => {
  switch (stato) {
    case REEL_STATES.DISPONIBILE:
      return 'Bobina disponibile per nuove installazioni'
    case REEL_STATES.IN_USO:
      return 'Bobina parzialmente utilizzata'
    case REEL_STATES.TERMINATA:
      return 'Bobina completamente esaurita'
    case REEL_STATES.OVER:
      return 'Bobina sovra-utilizzata (metri negativi)'
    default:
      return 'Stato non definito'
  }
}
