import { NextRequest, NextResponse } from 'next/server'

interface Responsabile {
  id_responsabile: number
  nome_responsabile: string
  telefono?: string
  email?: string
  experience_level: string
  id_cantiere: number
  data_creazione: string
  attivo: boolean
}

export async function GET(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Per ora restituiamo dati mock per testare l'interfaccia
    // TODO: Implementare chiamata al backend Python
    const mockResponsabili: Responsabile[] = [
      {
        id_responsabile: 1,
        nome_responsabile: "<PERSON>",
        telefono: "+39 ************",
        email: "<EMAIL>",
        experience_level: "Senior",
        id_cantiere: cantiereId,
        data_creazione: "2024-01-15T10:00:00Z",
        attivo: true
      },
      {
        id_responsabile: 2,
        nome_responsabile: "<PERSON> Verdi",
        telefono: "+39 ************",
        email: "<EMAIL>",
        experience_level: "Senior",
        id_cantiere: cantiereId,
        data_creazione: "2024-01-20T14:30:00Z",
        attivo: true
      },
      {
        id_responsabile: 3,
        nome_responsabile: "Anna Bianchi",
        telefono: "+39 ************",
        email: "<EMAIL>",
        experience_level: "Junior",
        id_cantiere: cantiereId,
        data_creazione: "2024-02-01T09:15:00Z",
        attivo: true
      }
    ]

    return NextResponse.json({
      success: true,
      data: mockResponsabili,
      total: mockResponsabili.length
    })

  } catch (error) {
    console.error('Errore nel recupero responsabili:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    const body = await request.json()
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Validazione base
    if (!body.nome_responsabile) {
      return NextResponse.json(
        { error: 'Nome responsabile obbligatorio' },
        { status: 400 }
      )
    }

    // Per ora restituiamo un mock del responsabile creato
    // TODO: Implementare chiamata al backend Python
    const nuovoResponsabile: Responsabile = {
      id_responsabile: Math.floor(Math.random() * 1000) + 100,
      nome_responsabile: body.nome_responsabile,
      telefono: body.telefono || null,
      email: body.email || null,
      experience_level: body.experience_level || 'Senior',
      id_cantiere: cantiereId,
      data_creazione: new Date().toISOString(),
      attivo: true
    }

    return NextResponse.json({
      success: true,
      data: nuovoResponsabile
    }, { status: 201 })

  } catch (error) {
    console.error('Errore nella creazione responsabile:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
