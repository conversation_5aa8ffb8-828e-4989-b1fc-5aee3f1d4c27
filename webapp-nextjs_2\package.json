{"name": "webapp-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "node tests/run-all-tests.js", "test:api": "node tests/crash-test-api.js", "test:frontend": "node tests/crash-test-frontend.js", "analyze": "node tests/analyze-unused-files.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.7", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.516.0", "next": "^15.3.3", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "workbox-webpack-plugin": "^7.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "puppeteer": "^23.0.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}