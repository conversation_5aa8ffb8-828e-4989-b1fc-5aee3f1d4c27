'use client'

import React from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { AlertTriangle, X, Calendar, Clock } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'

export default function ExpirationWarning() {
  const { 
    expirationWarning, 
    daysUntilExpiration, 
    expirationDate, 
    dismissExpirationWarning 
  } = useAuth()

  if (!expirationWarning) {
    return null
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const getAlertVariant = () => {
    if (daysUntilExpiration === 0) return 'destructive'
    if (daysUntilExpiration === 1) return 'destructive'
    return 'default'
  }

  const getIcon = () => {
    if (daysUntilExpiration === 0) return <AlertTriangle className="h-4 w-4" />
    if (daysUntilExpiration === 1) return <Clock className="h-4 w-4" />
    return <Calendar className="h-4 w-4" />
  }

  return (
    <Alert variant={getAlertVariant()} className="mb-4 border-l-4">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-2">
          {getIcon()}
          <div className="flex-1">
            <AlertDescription className="font-medium">
              {expirationWarning}
            </AlertDescription>
            {expirationDate && (
              <AlertDescription className="text-sm mt-1 opacity-90">
                Data di scadenza: {formatDate(expirationDate)}
              </AlertDescription>
            )}
            <AlertDescription className="text-sm mt-2 opacity-80">
              Contatta l'amministratore per rinnovare il tuo account.
            </AlertDescription>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={dismissExpirationWarning}
          className="h-6 w-6 p-0 hover:bg-transparent"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  )
}
