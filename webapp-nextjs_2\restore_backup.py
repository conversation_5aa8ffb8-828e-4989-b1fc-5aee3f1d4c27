#!/usr/bin/env python3
"""
Script per ripristinare un backup della webapp-nextjs_2
"""

import os
import shutil
import zipfile
import sys
import datetime

def list_available_backups():
    """Lista tutti i backup disponibili"""
    parent_dir = os.path.dirname(os.getcwd())
    backups = []
    
    for file in os.listdir(parent_dir):
        if file.startswith('webapp-nextjs_2_backup_') and file.endswith('.zip'):
            backup_path = os.path.join(parent_dir, file)
            backup_size = os.path.getsize(backup_path)
            backup_size_mb = backup_size / (1024 * 1024)
            backup_time = os.path.getmtime(backup_path)
            backup_date = datetime.datetime.fromtimestamp(backup_time)
            
            backups.append({
                'file': file,
                'path': backup_path,
                'size_mb': backup_size_mb,
                'date': backup_date
            })
    
    # Ordina per data (più recente prima)
    backups.sort(key=lambda x: x['date'], reverse=True)
    
    return backups

def restore_backup(backup_path):
    """Ripristina un backup specifico"""
    
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    
    print(f"🔄 Ripristino backup...")
    print(f"📦 File backup: {backup_path}")
    print(f"📁 Directory corrente: {current_dir}")
    
    try:
        # Verifica che il file di backup esista
        if not os.path.exists(backup_path):
            print(f"❌ File di backup non trovato: {backup_path}")
            return False
        
        # Crea backup della versione corrente prima del ripristino
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        current_backup_name = f"webapp-nextjs_2_current_backup_{timestamp}"
        current_backup_dir = os.path.join(parent_dir, current_backup_name)
        
        print(f"💾 Creazione backup della versione corrente...")
        print(f"📁 Backup corrente: {current_backup_dir}")
        
        # Copia la directory corrente come backup di sicurezza
        shutil.copytree(current_dir, current_backup_dir, 
                       ignore=shutil.ignore_patterns('node_modules', '.next', 'dist', 'build', '__pycache__'))
        
        print(f"✅ Backup corrente creato: {current_backup_dir}")
        
        # Rimuovi il contenuto della directory corrente (eccetto alcuni file)
        print(f"🗑️ Rimozione contenuto directory corrente...")
        
        for item in os.listdir(current_dir):
            item_path = os.path.join(current_dir, item)
            if item not in ['node_modules', '.git', '.env.local', '.env.production']:
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                else:
                    os.remove(item_path)
        
        # Estrai il backup
        print(f"📦 Estrazione backup...")
        
        with zipfile.ZipFile(backup_path, 'r') as zip_ref:
            zip_ref.extractall(current_dir)
        
        print(f"✅ Backup estratto con successo!")
        
        # Verifica che i file principali siano presenti
        required_files = ['package.json', 'src', 'next.config.js']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(os.path.join(current_dir, file)):
                missing_files.append(file)
        
        if missing_files:
            print(f"⚠️ File mancanti dopo il ripristino: {missing_files}")
            print(f"🔄 Ripristino del backup corrente...")
            
            # Ripristina il backup corrente
            for item in os.listdir(current_dir):
                item_path = os.path.join(current_dir, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                else:
                    os.remove(item_path)
            
            # Copia indietro il backup corrente
            for item in os.listdir(current_backup_dir):
                src = os.path.join(current_backup_dir, item)
                dst = os.path.join(current_dir, item)
                if os.path.isdir(src):
                    shutil.copytree(src, dst)
                else:
                    shutil.copy2(src, dst)
            
            print(f"❌ Ripristino fallito - versione corrente ripristinata")
            return False
        
        print(f"✅ Ripristino completato con successo!")
        print(f"📝 Backup della versione precedente salvato in: {current_backup_dir}")
        print(f"\n🔧 Prossimi passi:")
        print(f"   1. cd {current_dir}")
        print(f"   2. npm install")
        print(f"   3. npm run build")
        print(f"   4. npm run dev")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il ripristino: {str(e)}")
        return False

def main():
    print("🔧 Sistema di Ripristino Backup webapp-nextjs_2")
    print("=" * 60)
    
    # Lista backup disponibili
    backups = list_available_backups()
    
    if not backups:
        print("📋 Nessun backup trovato.")
        return
    
    print(f"📋 Backup disponibili:")
    for i, backup in enumerate(backups, 1):
        print(f"  {i}. {backup['file']}")
        print(f"     📅 Data: {backup['date'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"     📏 Dimensione: {backup['size_mb']:.2f} MB")
        print()
    
    # Se è stato specificato un argomento, usa quello
    if len(sys.argv) > 1:
        try:
            choice = int(sys.argv[1])
            if 1 <= choice <= len(backups):
                selected_backup = backups[choice - 1]
            else:
                print(f"❌ Scelta non valida: {choice}")
                return
        except ValueError:
            # Forse è un percorso file
            backup_path = sys.argv[1]
            if os.path.exists(backup_path):
                print(f"📦 Ripristino backup specificato: {backup_path}")
                restore_backup(backup_path)
                return
            else:
                print(f"❌ File non trovato: {backup_path}")
                return
    else:
        # Chiedi all'utente di scegliere
        try:
            choice = input(f"\n🔢 Scegli il backup da ripristinare (1-{len(backups)}) o 'q' per uscire: ")
            
            if choice.lower() == 'q':
                print("👋 Uscita...")
                return
            
            choice = int(choice)
            if 1 <= choice <= len(backups):
                selected_backup = backups[choice - 1]
            else:
                print(f"❌ Scelta non valida: {choice}")
                return
        except ValueError:
            print("❌ Input non valido")
            return
        except KeyboardInterrupt:
            print("\n👋 Uscita...")
            return
    
    # Conferma ripristino
    print(f"\n⚠️ ATTENZIONE: Stai per ripristinare il backup:")
    print(f"   📦 File: {selected_backup['file']}")
    print(f"   📅 Data: {selected_backup['date'].strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   📏 Dimensione: {selected_backup['size_mb']:.2f} MB")
    print(f"\n🔄 Questo sostituirà la versione corrente di webapp-nextjs_2")
    print(f"💾 La versione corrente verrà salvata come backup di sicurezza")
    
    if len(sys.argv) <= 1:  # Solo se non è automatico
        confirm = input(f"\n❓ Continuare? (s/N): ")
        if confirm.lower() not in ['s', 'si', 'y', 'yes']:
            print("👋 Operazione annullata")
            return
    
    # Esegui ripristino
    success = restore_backup(selected_backup['path'])
    
    if success:
        print(f"\n🎉 Ripristino completato con successo!")
    else:
        print(f"\n❌ Ripristino fallito!")

if __name__ == "__main__":
    main()
