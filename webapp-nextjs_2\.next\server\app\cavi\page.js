(()=>{var e={};e.id=986,e.ids=[986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10698:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>t});let t=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,i)=>{"use strict";i.d(a,{T:()=>n});var t=i(60687),s=i(43210),r=i(4780);let n=s.forwardRef(({className:e,...a},i)=>(0,t.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...a}));n.displayName="Textarea"},35476:(e,a,i)=>{Promise.resolve().then(i.bind(i,10698))},45583:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},51400:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>eZ});var t=i(60687),s=i(43210),r=i(16189),n=i(44493),l=i(29523),o=i(91821),c=i(63213),d=i(62185),m=i(96834),x=i(56896);i(15391);var u=i(6211),p=i(89667),h=i(15079),v=i(70569),b=i(98599),g=i(11273),j=i(31355),f=i(1359),N=i(32547),y=i(96963),_=i(55509),C=i(25028),w=i(46059),z=i(14163),A=i(8730),k=i(65551),S=i(63376),E=i(42247),O="Popover",[I,$]=(0,g.A)(O,[_.Bk]),T=(0,_.Bk)(),[F,R]=I(O),M=e=>{let{__scopePopover:a,children:i,open:r,defaultOpen:n,onOpenChange:l,modal:o=!1}=e,c=T(a),d=s.useRef(null),[m,x]=s.useState(!1),[u,p]=(0,k.i)({prop:r,defaultProp:n??!1,onChange:l,caller:O});return(0,t.jsx)(_.bL,{...c,children:(0,t.jsx)(F,{scope:a,contentId:(0,y.B)(),triggerRef:d,open:u,onOpenChange:p,onOpenToggle:s.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:m,onCustomAnchorAdd:s.useCallback(()=>x(!0),[]),onCustomAnchorRemove:s.useCallback(()=>x(!1),[]),modal:o,children:i})})};M.displayName=O;var P="PopoverAnchor";s.forwardRef((e,a)=>{let{__scopePopover:i,...r}=e,n=R(P,i),l=T(i),{onCustomAnchorAdd:o,onCustomAnchorRemove:c}=n;return s.useEffect(()=>(o(),()=>c()),[o,c]),(0,t.jsx)(_.Mz,{...l,...r,ref:a})}).displayName=P;var D="PopoverTrigger",L=s.forwardRef((e,a)=>{let{__scopePopover:i,...s}=e,r=R(D,i),n=T(i),l=(0,b.s)(a,r.triggerRef),o=(0,t.jsx)(z.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":K(r.open),...s,ref:l,onClick:(0,v.m)(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?o:(0,t.jsx)(_.Mz,{asChild:!0,...n,children:o})});L.displayName=D;var B="PopoverPortal",[q,V]=I(B,{forceMount:void 0}),U=e=>{let{__scopePopover:a,forceMount:i,children:s,container:r}=e,n=R(B,a);return(0,t.jsx)(q,{scope:a,forceMount:i,children:(0,t.jsx)(w.C,{present:i||n.open,children:(0,t.jsx)(C.Z,{asChild:!0,container:r,children:s})})})};U.displayName=B;var G="PopoverContent",J=s.forwardRef((e,a)=>{let i=V(G,e.__scopePopover),{forceMount:s=i.forceMount,...r}=e,n=R(G,e.__scopePopover);return(0,t.jsx)(w.C,{present:s||n.open,children:n.modal?(0,t.jsx)(W,{...r,ref:a}):(0,t.jsx)(H,{...r,ref:a})})});J.displayName=G;var Z=(0,A.TL)("PopoverContent.RemoveScroll"),W=s.forwardRef((e,a)=>{let i=R(G,e.__scopePopover),r=s.useRef(null),n=(0,b.s)(a,r),l=s.useRef(!1);return s.useEffect(()=>{let e=r.current;if(e)return(0,S.Eq)(e)},[]),(0,t.jsx)(E.A,{as:Z,allowPinchZoom:!0,children:(0,t.jsx)(Y,{...e,ref:n,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,v.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||i.triggerRef.current?.focus()}),onPointerDownOutside:(0,v.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,i=0===a.button&&!0===a.ctrlKey;l.current=2===a.button||i},{checkForDefaultPrevented:!1}),onFocusOutside:(0,v.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),H=s.forwardRef((e,a)=>{let i=R(G,e.__scopePopover),r=s.useRef(!1),n=s.useRef(!1);return(0,t.jsx)(Y,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||i.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,"pointerdown"===a.detail.originalEvent.type&&(n.current=!0));let t=a.target;i.triggerRef.current?.contains(t)&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&n.current&&a.preventDefault()}})}),Y=s.forwardRef((e,a)=>{let{__scopePopover:i,trapFocus:s,onOpenAutoFocus:r,onCloseAutoFocus:n,disableOutsidePointerEvents:l,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:m,...x}=e,u=R(G,i),p=T(i);return(0,f.Oh)(),(0,t.jsx)(N.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:r,onUnmountAutoFocus:n,children:(0,t.jsx)(j.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:m,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>u.onOpenChange(!1),children:(0,t.jsx)(_.UC,{"data-state":K(u.open),role:"dialog",id:u.contentId,...p,...x,ref:a,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),X="PopoverClose";function K(e){return e?"open":"closed"}s.forwardRef((e,a)=>{let{__scopePopover:i,...s}=e,r=R(X,i);return(0,t.jsx)(z.sG.button,{type:"button",...s,ref:a,onClick:(0,v.m)(e.onClick,()=>r.onOpenChange(!1))})}).displayName=X,s.forwardRef((e,a)=>{let{__scopePopover:i,...s}=e,r=T(i);return(0,t.jsx)(_.i3,{...r,...s,ref:a})}).displayName="PopoverArrow";var Q=i(4780);let ee=s.forwardRef(({className:e,align:a="center",sideOffset:i=4,...s},r)=>(0,t.jsx)(U,{children:(0,t.jsx)(J,{ref:r,align:a,sideOffset:i,className:(0,Q.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));ee.displayName=J.displayName;var ea=i(62688);let ei=(0,ea.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),et=(0,ea.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),es=(0,ea.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var er=i(11860);let en=(0,ea.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),el=(0,ea.A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]),eo=(0,ea.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),ec=(0,ea.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),ed=(0,ea.A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]]);function em({data:e=[],columns:a=[],loading:i=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:o,renderRow:c,className:d,pagination:x=!0,defaultRowsPerPage:p=25}){let[v,b]=(0,s.useState)({key:null,direction:null}),[g,j]=(0,s.useState)({}),[f,N]=(0,s.useState)({}),[y,_]=(0,s.useState)(0),[C,w]=(0,s.useState)(p),z=a=>[...new Set(e.map(e=>e[a]).filter(Boolean))].sort(),A=(0,s.useMemo)(()=>{let a=[...e];return Object.entries(g).forEach(([e,i])=>{!i.value||Array.isArray(i.value)&&0===i.value.length||"string"==typeof i.value&&""===i.value.trim()||(a=a.filter(a=>{let t=a[e];if("select"===i.type)return(Array.isArray(i.value)?i.value:[i.value]).includes(t);if("text"===i.type){let e=i.value.toLowerCase(),a=String(t||"").toLowerCase();return"equals"===i.operator?a===e:a.includes(e)}if("number"===i.type){let e=parseFloat(t),a=parseFloat(i.value);if(isNaN(e)||isNaN(a))return!1;switch(i.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),v.key&&v.direction&&a.sort((e,a)=>{let i=e[v.key],t=a[v.key];if(null==i&&null==t)return 0;if(null==i)return"asc"===v.direction?-1:1;if(null==t)return"asc"===v.direction?1:-1;let s=parseFloat(i),r=parseFloat(t),n=!isNaN(s)&&!isNaN(r),l=0;return l=n?s-r:String(i).localeCompare(String(t)),"asc"===v.direction?l:-l}),a},[e,g,v]),k=(0,s.useMemo)(()=>{if(!x)return A;let e=y*C,a=e+C;return A.slice(e,a)},[A,y,C,x]),S=Math.ceil(A.length/C),E=y*C+1,O=Math.min((y+1)*C,A.length),I=e=>{let i=a.find(a=>a.field===e);i?.disableSort||b(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},$=(e,a)=>{j(i=>({...i,[e]:{...i[e],...a}}))},T=e=>{j(a=>{let i={...a};return delete i[e],i})},F=e=>v.key!==e?(0,t.jsx)(ei,{className:"h-3 w-3"}):"asc"===v.direction?(0,t.jsx)(et,{className:"h-3 w-3"}):"desc"===v.direction?(0,t.jsx)(es,{className:"h-3 w-3"}):(0,t.jsx)(ei,{className:"h-3 w-3"}),R=Object.keys(g).length>0;return i?(0,t.jsx)(n.Zp,{className:d,children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,t.jsxs)("div",{className:d,children:[R&&(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(g).map(([e,i])=>{let s=a.find(a=>a.field===e);if(!s)return null;let r=Array.isArray(i.value)?i.value.join(", "):String(i.value);return(0,t.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[s.headerName,": ",r,(0,t.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>T(e),children:(0,t.jsx)(er.A,{className:"h-3 w-3"})})]},e)}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{j({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsxs)(u.XI,{children:[(0,t.jsx)(u.A0,{children:(0,t.jsx)(u.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:a.map(a=>(0,t.jsx)(u.nd,{className:(0,Q.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:{width:a.width,...a.headerStyle},children:a.renderHeader?a.renderHeader():(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"truncate",children:a.headerName}),(0,t.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!a.disableSort&&(0,t.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>I(a.field),children:F(a.field)}),!a.disableFilter&&(0,t.jsxs)(M,{open:f[a.field],onOpenChange:e=>N(i=>({...i,[a.field]:e})),children:[(0,t.jsx)(L,{asChild:!0,children:(0,t.jsx)(l.$,{variant:"ghost",size:"sm",className:(0,Q.cn)("h-4 w-4 p-0 hover:bg-mariner-100",g[a.field]&&"text-mariner-600 opacity-100"),children:(0,t.jsx)(en,{className:"h-2.5 w-2.5"})})}),(0,t.jsx)(ee,{className:"w-64",align:"start",children:(0,t.jsx)(ex,{column:a,data:e,currentFilter:g[a.field],onFilterChange:e=>$(a.field,e),onClearFilter:()=>T(a.field),getUniqueValues:()=>z(a.field)})})]})]})]}),g[a.field]&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},a.field))})}),(0,t.jsx)(u.BF,{children:k.length>0?k.map((e,i)=>c?c(e,y*C+i):(0,t.jsx)(u.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:a.map(a=>(0,t.jsx)(u.nA,{className:(0,Q.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},i)):(0,t.jsx)(u.Hj,{children:(0,t.jsx)(u.nA,{colSpan:a.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),x&&A.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,t.jsxs)(h.l6,{value:C.toString(),onValueChange:e=>{w(Number(e)),_(0)},children:[(0,t.jsx)(h.bq,{className:"w-20",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"10",children:"10"}),(0,t.jsx)(h.eb,{value:"25",children:"25"}),(0,t.jsx)(h.eb,{value:"50",children:"50"}),(0,t.jsx)(h.eb,{value:"100",children:"100"}),(0,t.jsx)(h.eb,{value:A.length.toString(),children:"Tutto"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:A.length>0?`${E}-${O} di ${A.length}`:"0 di 0"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>_(0),disabled:0===y,className:"h-8 w-8 p-0",children:(0,t.jsx)(el,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>_(e=>Math.max(0,e-1)),disabled:0===y,className:"h-8 w-8 p-0",children:(0,t.jsx)(eo,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>_(e=>Math.min(S-1,e+1)),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(ec,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>_(S-1),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(ed,{className:"h-4 w-4"})})]})]})]})]})}function ex({column:e,currentFilter:a,onFilterChange:i,onClearFilter:r,getUniqueValues:n}){let[o,c]=(0,s.useState)(a?.value||""),[d,m]=(0,s.useState)(a?.operator||"contains"),u=n(),v="number"!==e.dataType&&u.length<=20,b="number"===e.dataType,g=()=>{v?i({type:"select",value:Array.isArray(o)?o:[o]}):b?i({type:"number",value:o,operator:d}):i({type:"text",value:o,operator:d})};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",e.headerName]}),v?(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.S,{id:`filter-${e}`,checked:Array.isArray(o)?o.includes(e):o===e,onCheckedChange:a=>{Array.isArray(o)?c(a?[...o,e]:o.filter(a=>a!==e)):c(a?[e]:[])}}),(0,t.jsx)("label",{htmlFor:`filter-${e}`,className:"text-sm",children:e})]},e))}):(0,t.jsxs)("div",{className:"space-y-2",children:[b&&(0,t.jsxs)(h.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"}),(0,t.jsx)(h.eb,{value:"gt",children:"Maggiore di"}),(0,t.jsx)(h.eb,{value:"lt",children:"Minore di"}),(0,t.jsx)(h.eb,{value:"gte",children:"Maggiore o uguale"}),(0,t.jsx)(h.eb,{value:"lte",children:"Minore o uguale"})]})]}),!b&&(0,t.jsxs)(h.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]}),(0,t.jsx)(p.p,{placeholder:`Cerca ${e.headerName.toLowerCase()}...`,value:o,onChange:e=>c(e.target.value),onKeyDown:e=>"Enter"===e.key&&g()})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.$,{size:"sm",onClick:g,children:"Applica"}),(0,t.jsx)(l.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var eu=i(99270);let ep=(0,ea.A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),eh=(0,ea.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);function ev({cavi:e=[],onFilteredDataChange:a,loading:i=!1,selectionEnabled:r=!1,onSelectionToggle:o}){let[c,d]=(0,s.useState)(""),[m,x]=(0,s.useState)("contains"),u=e=>e?e.toString().toLowerCase().trim():"",v=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},b=(0,s.useCallback)((e,a,i)=>{let t=u(a);if(!t)return!0;let s=u(e.id_cavo),{prefix:r,number:n,suffix:l}=v(e.id_cavo||""),o=u(e.tipologia),c=u(e.formazione||e.sezione),d=u(e.utility),m=u(e.sistema),x=u(e.da||e.ubicazione_partenza),p=u(e.a||e.ubicazione_arrivo),h=u(e.utenza_partenza),b=u(e.utenza_arrivo),g=[s,r,n,l,o,c,d,m,x,p,h,b,u(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":u(e.id_bobina)],j=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],f=t.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(f){let e=f[1],a=parseFloat(f[2]);return j.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(t);return!!(!isNaN(N)&&j.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?g.some(e=>e===t):g.some(e=>e.includes(t)))},[]);(0,s.useCallback)(()=>{if(!c.trim())return void a?.(e);let i=c.split(",").map(e=>e.trim()).filter(e=>e.length>0),t=[];t="equals"===m?1===i.length?e.filter(e=>b(e,i[0],!0)):e.filter(e=>i.every(a=>b(e,a,!0))):e.filter(e=>i.some(a=>b(e,a,!1))),a?.(t)},[c,m,e,a,b]);let g=e=>{d(e)},j=()=>{d(""),x("contains")};return(0,t.jsx)(n.Zp,{className:"mb-1",children:(0,t.jsxs)(n.Wu,{className:"p-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(p.p,{placeholder:"Ricerca intelligente cavi...",value:c,onChange:e=>g(e.target.value),disabled:i,className:"pl-10 pr-10 h-8"}),c&&(0,t.jsx)(l.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:j,children:(0,t.jsx)(er.A,{className:"h-2.5 w-2.5"})})]}),(0,t.jsx)("div",{className:"w-32",children:(0,t.jsxs)(h.l6,{value:m,onValueChange:e=>x(e),children:[(0,t.jsx)(h.bq,{className:"h-8",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]})}),c&&(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:j,disabled:i,className:"transition-all duration-200 hover:scale-105",children:"Pulisci"}),o&&(0,t.jsxs)(l.$,{variant:r?"default":"outline",size:"sm",onClick:o,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105",children:[r?(0,t.jsx)(ep,{className:"h-4 w-4"}):(0,t.jsx)(eh,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]})]}),c&&(0,t.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCA1"}),(0,t.jsx)("span",{children:"• Virgole per multipli"}),(0,t.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function eb({text:e,maxLength:a=20,className:i=""}){let[r,n]=(0,s.useState)(!1),[l,o]=(0,s.useState)({x:0,y:0});if(!e)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});let c=e.length>a,d=c?`${e.substring(0,a)}...`:e;return c?(0,t.jsxs)("div",{className:"relative inline-block",children:[(0,t.jsx)("span",{className:`cursor-help ${i}`,style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{o({x:e.clientX,y:e.clientY}),n(!0)},onMouseMove:e=>{o({x:e.clientX,y:e.clientY})},onMouseLeave:()=>n(!1),title:e,children:d}),r&&(0,t.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:l.y-40,left:l.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[e,(0,t.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,t.jsx)("span",{className:i,children:e})}function eg({cavi:e=[],loading:a=!1,selectionEnabled:i=!1,selectedCavi:r=[],onSelectionChange:n,onStatusAction:o,onContextMenuAction:c}){let[d,p]=(0,s.useState)(e),[h,v]=(0,s.useState)(e),[b,g]=(0,s.useState)(i),j=e=>{n&&n(e?h.map(e=>e.id_cavo):[])},f=(e,a)=>{n&&n(a?[...r,e]:r.filter(a=>a!==e))},N=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})});if(e.ok){let a=await e.blob(),i=window.URL.createObjectURL(a),t=document.createElement("a");t.href=i,t.download=`cavi_export_${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(i),document.body.removeChild(t)}else{let a=await e.json();alert(`Errore durante l'esportazione: ${a.error}`)}}catch(e){alert("Errore durante l'esportazione")}},y=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1,newStatus:e})}),i=await a.json();i.success?alert(i.message):alert(`Errore: ${i.error}`)}catch(e){alert("Errore durante il cambio stato")}},_=()=>{alert(`Assegnazione comanda per ${r.length} cavi`)},C=async()=>{if(confirm(`Sei sicuro di voler eliminare ${r.length} cavi?`))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert(`Errore: ${a.error}`)}catch(e){alert("Errore durante l'eliminazione")}},w=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,t.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(eb,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(eb,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,t.jsx)(eb,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(eb,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(eb,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>{let a=e.id_bobina;if(a,!a||"N/A"===a)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});if("BOBINA_VUOTA"===a)return(0,t.jsx)(m.E,{variant:"outline",className:"text-xs px-2 py-0.5 text-orange-600 border-orange-300 bg-orange-50",children:"Vuota"});let i=a.match(/_B(.+)$/);return i||(i=a.match(/_b(.+)$/))||(i=a.match(/c\d+_[bB](\d+)$/))||(i=a.match(/(\d+)$/))?(0,t.jsx)("span",{className:"font-medium",children:i[1]}):(0,t.jsx)("span",{className:"font-medium text-xs",children:a})}},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableFilter:!0,disableSort:!0,renderCell:e=>z(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableFilter:!0,disableSort:!0,renderCell:e=>A(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableFilter:!0,disableSort:!0,renderCell:e=>k(e)}];return b&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,t.jsx)(x.S,{checked:r.length===h.length&&h.length>0,onCheckedChange:j}),renderCell:e=>(0,t.jsx)(x.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>f(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[b,r,h,j,f]),z=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.comanda_posa,s=e.comanda_partenza,r=e.comanda_arrivo,n=e.comanda_certificazione,l=i||s||r||n;if(l&&"In corso"===e.stato_installazione)return(0,t.jsx)(m.E,{className:"bg-blue-600 text-white cursor-pointer hover:bg-blue-700 hover:scale-105 transition-all duration-200 hover:shadow-md px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),o?.(e,"view_command",l)},children:l});let c=e.stato_installazione||"Da installare";return"Installato"===c||a>0?(0,t.jsx)(m.E,{className:"bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300",onClick:a=>{a.stopPropagation(),o?.(e,"modify_reel")},children:"Installato"}):"In corso"===c?(0,t.jsx)(m.E,{className:"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300",onClick:a=>{a.stopPropagation(),o?.(e,"insert_meters")},children:"In corso"}):(0,t.jsx)(m.E,{variant:"outline",className:"text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium",onClick:a=>{a.stopPropagation(),o?.(e,"insert_meters")},children:"Da installare"})},A=e=>{let a,i,s,r=e.metri_posati>0||e.metratura_reale>0,n=e.collegamento||e.collegamenti||0;if(!r)return(0,t.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"});switch(n){case 0:a="⚪⚪ Collega",i="connect_cable",s="bg-gray-100 text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-gray-300";break;case 1:a="\uD83D\uDFE2⚪ Completa",i="connect_arrival",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 2:a="⚪\uD83D\uDFE2 Completa",i="connect_departure",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 3:a="\uD83D\uDFE2\uD83D\uDFE2 Scollega",i="disconnect_cable",s="bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300";break;default:a="Gestisci",i="manage_connections",s="bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 hover:text-blue-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-blue-300"}return(0,t.jsx)(m.E,{className:s,onClick:a=>{a.stopPropagation(),o?.(e,i)},children:a})},k=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?(0,t.jsx)(m.E,{className:"bg-green-600 text-white cursor-pointer hover:bg-green-700 transition-all duration-200 hover:scale-105 px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),o?.(e,"generate_pdf")},children:"✓ Genera PDF"}):(0,t.jsx)(m.E,{variant:"outline",className:"text-purple-700 cursor-pointer hover:bg-purple-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border-purple-200",onClick:a=>{a.stopPropagation(),o?.(e,"create_certificate")},children:"\uD83D\uDD50 Certifica"}):(0,t.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"})};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(ev,{cavi:e,onFilteredDataChange:e=>{p(e)},loading:a,selectionEnabled:b,onSelectionToggle:()=>{g(!b)}}),(0,t.jsx)(em,{data:d,columns:w,loading:a,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{v(e)},renderRow:(e,a)=>{let i=r.includes(e.id_cavo);return(0,t.jsx)(u.Hj,{className:`
          ${i?"bg-blue-50 border-blue-200":"bg-white"}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${i?"ring-1 ring-blue-300":""}
        `,onClick:()=>b&&f(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),c?.(e,"context_menu")},children:w.map(a=>(0,t.jsx)(u.nA,{className:`
              py-2 px-2 text-sm text-left
              ${i?"text-blue-900":"text-gray-900"}
              transition-colors duration-200
            `,style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,t.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),b&&r.length>0&&(0,t.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(m.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[r.length," cavi selezionati"]}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>j(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>N(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCA"}),(0,t.jsx)("span",{children:"Esporta"})]}),(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>y(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDD04"}),(0,t.jsx)("span",{children:"Cambia Stato"})]}),(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>_(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCB"}),(0,t.jsx)("span",{children:"Assegna Comanda"})]}),(0,t.jsxs)(l.$,{variant:"destructive",size:"sm",onClick:()=>C(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,t.jsx)("span",{children:"Elimina"})]})]})]})})]})}var ej=i(53411),ef=i(23361),eN=i(5336),ey=i(48730),e_=i(43649),eC=i(45583),ew=i(19080);function ez({cavi:e,filteredCavi:a,className:i,revisioneCorrente:r}){let l=(0,s.useMemo)(()=>{let i=e.length,t=a.length,s=a.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,r=a.filter(e=>"In corso"===e.stato_installazione).length,n=a.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,l=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=a.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=a.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=a.reduce((e,a)=>e+(a.metri_teorici||0),0),m=a.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===t?0:Math.round(100*(((s-n)*2+(n-c)*3.5+4*c)/(4*t)*100))/100;return{totalCavi:i,filteredCount:t,installati:s,inCorso:r,daInstallare:t-s-r,collegati:n,parzialmenteCollegati:l,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[e,a]);return(0,t.jsx)(n.Zp,{className:i,children:(0,t.jsxs)(n.Wu,{className:"p-1.5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(ej.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),r&&(0,t.jsxs)(m.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",r]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(ef.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:l.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",l.totalCavi," cavi"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(eN.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:l.installati}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(ey.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:l.inCorso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(e_.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:l.daInstallare}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(eC.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:l.collegati}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(ew.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:l.certificati}),(0,t.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[l.metriInstallati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",l.metriTotali.toLocaleString(),"m"]})]})]})]}),l.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,t.jsxs)("span",{className:`font-bold ${l.percentualeInstallazione>=80?"text-emerald-700":l.percentualeInstallazione>=50?"text-yellow-700":l.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"}`,children:[l.percentualeInstallazione.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${l.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":l.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":l.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"}`,style:{width:`${Math.min(l.percentualeInstallazione,100)}%`}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,t.jsxs)("span",{children:[l.installati,"I + ",l.collegati,"C + ",l.certificati,"Cert"]})]})]})]})})}var eA=i(63503),ek=i(80013);let eS=(0,ea.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var eE=i(41862),eO=i(93613);function eI({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:n,onError:m}){let{cantiere:x}=(0,c.A)(),u=r||x,[h,v]=(0,s.useState)([]),[b,g]=(0,s.useState)(""),[j,f]=(0,s.useState)(""),[N,y]=(0,s.useState)(""),[_,C]=(0,s.useState)(!1),[w,z]=(0,s.useState)(!1),[A,k]=(0,s.useState)(""),[S,E]=(0,s.useState)("compatibili"),O=i?h.filter(e=>{let a=e.tipologia===i.tipologia&&e.sezione===i.sezione,t=!N||e.id_bobina.toLowerCase().includes(N.toLowerCase());return a&&t&&e.metri_residui>0}):[],I=i?h.filter(e=>{let a=e.tipologia!==i.tipologia||e.sezione!==i.sezione,t=!N||e.id_bobina.toLowerCase().includes(N.toLowerCase());return a&&t&&e.metri_residui>0}):[],$=async()=>{if(!i)return;if(!b)return void k("Selezionare un'operazione");if("assegna_nuova"===b&&!j)return void k("Selezionare una bobina dalla lista");let e="";if("annulla_installazione"===b)e="";else if("assegna_nuova"===b){if((e=j)===i.id_bobina)return void k("La bobina selezionata \xe8 gi\xe0 associata al cavo")}else if("rimuovi_bobina"!==b)return void k("Opzione non valida");else if((e="BOBINA_VUOTA")===i.id_bobina)return void k("Il cavo ha gi\xe0 la bobina vuota assegnata");if("assegna_nuova"===b){let e=h.find(e=>e.id_bobina===j),a=i.metri_posati||0;if(e&&a>e.metri_residui)return void k(`La bobina selezionata ha solo ${e.metri_residui}m disponibili, ma il cavo ha ${a}m posati`)}try{if(C(!0),k(""),!u)throw Error("Cantiere non selezionato");console.log({cantiere:u.id_cantiere,cavo:i.id_cavo,operazione:b,nuovaBobina:e}),"annulla_installazione"===b?await d.At.cancelInstallation(u.id_cantiere,i.id_cavo):await d.At.updateBobina(u.id_cantiere,i.id_cavo,e,!0);let t="";switch(b){case"assegna_nuova":t=`Nuova bobina ${e} assegnata al cavo ${i.id_cavo}`;break;case"rimuovi_bobina":t=`Bobina rimossa dal cavo ${i.id_cavo} (metri restituiti alla bobina precedente)`;break;case"annulla_installazione":t=`Installazione annullata per il cavo ${i.id_cavo} (metri restituiti, stato resettato a "da installare")`;break;default:t=`Operazione completata per il cavo ${i.id_cavo}`}n(t),a()}catch(e){m(e.response?.data?.detail||e.message||"Errore durante la modifica della bobina")}finally{C(!1)}},T=()=>{_||(g(""),f(""),y(""),k(""),E("compatibili"),a())};return i?(0,t.jsx)(eA.lG,{open:e,onOpenChange:T,children:(0,t.jsxs)(eA.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,t.jsx)(eA.c7,{children:(0,t.jsxs)(eA.L3,{children:["Modifica Bobina Cavo ",i.id_cavo]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ew.A,{className:"h-5 w-5"}),(0,t.jsx)("h3",{className:"font-medium",children:"Cavo Selezionato"})]}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"ID:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:i.id_cavo})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Tipologia:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:i.tipologia})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Formazione:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:i.sezione})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Metri:"}),(0,t.jsxs)("span",{className:"ml-1 font-medium",children:[i.metri_teorici," m"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Bobina:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:i.id_bobina})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Stato:"}),(0,t.jsx)("span",{className:"ml-1 px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium",children:i.stato_installazione})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===b,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Assegna nuova bobina"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===b,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Rimuovi bobina attuale"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===b,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Annulla installazione"})]})]})]}),"assegna_nuova"===b&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(p.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:N,onChange:e=>y(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,t.jsx)("button",{onClick:()=>E("compatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"compatibili"===S?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eN.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Compatibili (",O.length,")"]})]})}),(0,t.jsx)("button",{onClick:()=>E("incompatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"incompatibili"===S?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Incompatibili (",I.length,")"]})]})})]}),(0,t.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:w?(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,t.jsx)("div",{className:"p-2",children:"compatibili"===S?0===O.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"space-y-1",children:O.map(e=>(0,t.jsx)("div",{onClick:()=>f(e.id_bobina),className:`p-3 rounded cursor-pointer transition-colors ${j===e.id_bobina?"bg-blue-100 border border-blue-300":"hover:bg-gray-50 border border-transparent"}`,children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.id_bobina}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:[e.metri_residui,"m"]})})]})},e.id_bobina))}):0===I.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"space-y-1",children:I.map(e=>(0,t.jsx)("div",{onClick:()=>f(e.id_bobina),className:`p-3 rounded cursor-pointer transition-colors ${j===e.id_bobina?"bg-orange-100 border border-orange-300":"hover:bg-gray-50 border border-transparent"}`,children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.id_bobina}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("div",{className:"text-xs text-orange-600 font-medium",children:"30m"})})]})},e.id_bobina))})})})]})]}),A&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:A})]}),(0,t.jsxs)(eA.Es,{className:"flex justify-end space-x-2",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:T,disabled:_,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:$,disabled:_||!b||"bobina_compatibile"===b&&!j,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[_&&(0,t.jsx)(eE.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})}):null}function e$({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:n,onError:x}){let{cantiere:u}=(0,c.A)(),h=r||u,[v,b]=(0,s.useState)({metri_posati:"",id_bobina:""}),[g,j]=(0,s.useState)({}),[f,N]=(0,s.useState)({}),[y,_]=(0,s.useState)(!1),[C,w]=(0,s.useState)([]),[z,A]=(0,s.useState)(!1),[k,S]=(0,s.useState)(""),[E,O]=(0,s.useState)(!1),I=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=C.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},$=i?C.filter(e=>{let a=e.tipologia===i.tipologia&&e.sezione===i.sezione,t=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&t&&e.metri_residui>0}):[],T=i?C.filter(e=>{let a=e.tipologia!==i.tipologia||e.sezione!==i.sezione,t=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&t&&e.metri_residui>0}):[],F=e=>{b(a=>({...a,id_bobina:e.id_bobina})),j(e=>{let a={...e};return delete a.id_bobina,a})},R=async()=>{if(console.log({cavo:i?.id_cavo,metri_posati:v.metri_posati,id_bobina:v.id_bobina}),!i)return;if(!v.metri_posati||0>parseFloat(v.metri_posati))return void x("Inserire metri posati validi (≥ 0)");if(!v.id_bobina)return void x("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(v.metri_posati);if("BOBINA_VUOTA"!==v.id_bobina){let e=C.find(e=>e.id_bobina===v.id_bobina);e&&e.metri_residui}try{if(_(!0),!h)throw Error("Cantiere non selezionato");console.log({cantiere:h.id_cantiere,cavo:i.id_cavo,metri:e,bobina:v.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===v.id_bobina}),await d.At.updateMetriPosati(h.id_cantiere,i.id_cavo,e,v.id_bobina,!0),n(`Metri posati aggiornati con successo per il cavo ${i.id_cavo}: ${e}m`),a()}catch(e){x(e.response?.data?.detail||e.message||"Errore durante il salvataggio dei metri posati")}finally{_(!1)}},M=()=>{y||(b({metri_posati:"",id_bobina:""}),j({}),N({}),S(""),a())};return i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eA.lG,{open:e,onOpenChange:M,children:(0,t.jsxs)(eA.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,t.jsxs)(eA.c7,{className:"flex-shrink-0",children:[(0,t.jsxs)(eA.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eS,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",i.id_cavo]}),(0,t.jsx)(eA.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",i.tipologia||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Da:"})," ",i.ubicazione_partenza||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",i.sezione||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"A:"})," ",i.ubicazione_arrivo||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri teorici:"})," ",i.metri_teorici||"N/A"," m"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Gi\xe0 posati:"})," ",i.metratura_reale||0," m"]})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.p,{id:"metri",type:"number",value:v.metri_posati,onChange:e=>b(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,t.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),g.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:g.metri_posati}),f.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-amber-600",children:f.metri_posati})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,t.jsx)("div",{className:"sm:col-span-5",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(p.p,{placeholder:"ID, tipologia, formazione...",value:k,onChange:e=>S(e.target.value),className:"pl-10",disabled:y}),k&&(0,t.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>S(""),children:(0,t.jsx)(er.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)("div",{className:"sm:col-span-7",children:(0,t.jsxs)(l.$,{type:"button",variant:"BOBINA_VUOTA"===v.id_bobina?"default":"outline",className:`w-full h-10 font-bold flex items-center justify-center gap-2 ${"BOBINA_VUOTA"===v.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"}`,onClick:()=>{b(e=>({...e,id_bobina:"BOBINA_VUOTA"})),j(e=>{let a={...e};return delete a.id_bobina,a}),console.log({saving:!1,metri_posati:v.metri_posati,id_bobina:"BOBINA_VUOTA",errorsCount:Object.keys(g).length})},disabled:y,children:["BOBINA_VUOTA"===v.id_bobina&&(0,t.jsx)(eN.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]})}),z?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(eE.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento bobine..."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(eN.A,{className:"h-4 w-4"}),"Bobine Compatibili (",$.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===$.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:$.map(e=>(0,t.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${v.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"}`,onClick:()=>F(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,t.jsx)(eN.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:I(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(e_.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",T.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===T.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:T.map(e=>(0,t.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${v.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"}`,onClick:()=>F(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,t.jsx)(eN.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:I(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===C.length&&!z&&(0,t.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(e_.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsx)(o.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),g.id_bobina&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:g.id_bobina})]})]})]}),(0,t.jsxs)(eA.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,t.jsx)("div",{children:"installato"===i.stato_installazione&&i.id_bobina&&(0,t.jsx)(l.$,{variant:"outline",onClick:()=>{O(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:M,disabled:y,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:R,disabled:y||!v.metri_posati||0>parseFloat(v.metri_posati)||!v.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,t.jsx)(eE.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,t.jsx)(eI,{open:E,onClose:()=>O(!1),cavo:i,onSuccess:e=>{n(e),O(!1),a()},onError:x})]}):null}function eT({open:e,onClose:a,cavo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,s.useState)(""),[p,v]=(0,s.useState)([]),[b,g]=(0,s.useState)(!1),[j,f]=(0,s.useState)(!1),[N,y]=(0,s.useState)(""),_=async()=>{if(i&&m)try{g(!0),y(""),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"partenza",x),r(`Collegamento lato partenza completato per il cavo ${i.id_cavo}`),a()}catch(e){n(e.response?.data?.detail||e.message||"Errore durante il collegamento")}finally{g(!1)}},C=async()=>{if(i&&m)try{g(!0),y(""),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"arrivo",x),r(`Collegamento lato arrivo completato per il cavo ${i.id_cavo}`),a()}catch(e){n(e.response?.data?.detail||e.message||"Errore durante il collegamento")}finally{g(!1)}},w=async e=>{if(i&&m)try{g(!0),y(""),await d.At.scollegaCavo(m.id_cantiere,i.id_cavo,e);let t=e?` lato ${e}`:"";r(`Scollegamento${t} completato per il cavo ${i.id_cavo}`),a()}catch(e){n(e.response?.data?.detail||e.message||"Errore durante lo scollegamento")}finally{g(!1)}};if(!i)return null;let z=(()=>{if(!i)return{stato:"non_collegato",descrizione:"Non collegato"};switch(i.collegamento||i.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}})(),A=(i.metri_posati||i.metratura_reale||0)>0;return(0,t.jsx)(eA.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eA.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(eA.c7,{children:[(0,t.jsxs)(eA.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eC.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",i.id_cavo]}),(0,t.jsxs)(eA.rr,{children:["Gestisci i collegamenti del cavo ",i.id_cavo]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Stato Attuale"}),(0,t.jsx)("div",{className:"mt-1 text-lg font-semibold",children:z.descrizione})]}),!A&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Il cavo deve essere installato prima di poter essere collegato."})]}),N&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:N})]}),A&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"responsabile",children:"Responsabile Collegamento"}),(0,t.jsxs)(h.l6,{value:x,onValueChange:u,disabled:j,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:p.map(e=>(0,t.jsxs)(h.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&` - ${e.numero_telefono}`]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:["partenza"!==z.stato&&"completo"!==z.stato&&(0,t.jsxs)(l.$,{onClick:_,disabled:b||!x,className:"w-full",children:[b?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eC.A,{className:"h-4 w-4 mr-2"}),"Collega Partenza"]}),"arrivo"!==z.stato&&"completo"!==z.stato&&(0,t.jsxs)(l.$,{onClick:C,disabled:b||!x,className:"w-full",children:[b?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eC.A,{className:"h-4 w-4 mr-2"}),"Collega Arrivo"]})]}),"non_collegato"!==z.stato&&(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)(l.$,{onClick:()=>w(),disabled:b,variant:"destructive",className:"w-full",children:[b?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eO.A,{className:"h-4 w-4 mr-2"}),"Scollega Completamente"]})})]})]})]}),(0,t.jsx)(eA.Es,{children:(0,t.jsx)(l.$,{variant:"outline",onClick:a,disabled:b,children:"Chiudi"})})]})})}var eF=i(34729),eR=i(86561),eM=i(31158);function eP({open:e,onClose:a,cavo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,s.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[v,b]=(0,s.useState)([]),[g,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(!1),[y,_]=(0,s.useState)(""),C=async()=>{if(i&&m){if(!x.responsabile_certificazione)return void _("Seleziona un responsabile per la certificazione");try{j(!0),_("");let e={id_cavo:i.id_cavo,responsabile_certificazione:x.responsabile_certificazione,data_certificazione:x.data_certificazione,esito_certificazione:x.esito_certificazione,note_certificazione:x.note_certificazione||null};await d.km.createCertificazione(m.id_cantiere,e),r(`Certificazione completata per il cavo ${i.id_cavo}`),a()}catch(e){n(e.response?.data?.detail||e.message||"Errore durante la certificazione")}finally{j(!1)}}},w=async()=>{if(i&&m)try{j(!0),_("");let e=await d.km.generatePDF(m.id_cantiere,i.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download",`certificato_${i.id_cavo}.pdf`),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a),r(`PDF certificato generato per il cavo ${i.id_cavo}`)}catch(e){n(e.response?.data?.detail||e.message||"Errore durante la generazione del PDF")}finally{j(!1)}};if(!i)return null;let z=(i.metri_posati||i.metratura_reale||0)>0,A=!!i&&3===(i.collegamento||i.collegamenti||0),k=!!i&&(!0===i.certificato||"SI"===i.certificato||"CERTIFICATO"===i.certificato);return(0,t.jsx)(eA.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eA.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eA.c7,{children:[(0,t.jsxs)(eA.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eR.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",i.id_cavo]}),(0,t.jsxs)(eA.rr,{children:["Certifica il cavo ",i.id_cavo," o genera il PDF del certificato"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Stato Cavo"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:`w-3 h-3 rounded-full ${z?"bg-green-500":"bg-red-500"}`}),(0,t.jsx)("span",{className:"text-sm",children:z?"Installato":"Non installato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:`w-3 h-3 rounded-full ${A?"bg-green-500":"bg-red-500"}`}),(0,t.jsx)("span",{className:"text-sm",children:A?"Collegato":"Non collegato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:`w-3 h-3 rounded-full ${k?"bg-green-500":"bg-red-500"}`}),(0,t.jsx)("span",{className:"text-sm",children:k?"Certificato":"Non certificato"})]})]})]}),!z&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Il cavo deve essere installato prima di poter essere certificato."})]}),!A&&z&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Il cavo deve essere completamente collegato prima di poter essere certificato."})]}),y&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:y})]}),k?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(eR.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."})]}),(0,t.jsxs)(l.$,{onClick:w,disabled:g,className:"w-full",children:[g?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eM.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):z&&A&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"responsabile",children:"Responsabile Certificazione *"}),(0,t.jsxs)(h.l6,{value:x.responsabile_certificazione,onValueChange:e=>u(a=>({...a,responsabile_certificazione:e})),disabled:f,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:v.map(e=>(0,t.jsxs)(h.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&` - ${e.numero_telefono}`]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"data",children:"Data Certificazione"}),(0,t.jsx)(p.p,{id:"data",type:"date",value:x.data_certificazione,onChange:e=>u(a=>({...a,data_certificazione:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"esito",children:"Esito Certificazione"}),(0,t.jsxs)(h.l6,{value:x.esito_certificazione,onValueChange:e=>u(a=>({...a,esito_certificazione:e})),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"CONFORME",children:"CONFORME"}),(0,t.jsx)(h.eb,{value:"NON_CONFORME",children:"NON CONFORME"}),(0,t.jsx)(h.eb,{value:"CONFORME_CON_RISERVA",children:"CONFORME CON RISERVA"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(eF.T,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:x.note_certificazione,onChange:e=>u(a=>({...a,note_certificazione:e.target.value})),rows:3})]}),(0,t.jsxs)(l.$,{onClick:C,disabled:g||!x.responsabile_certificazione,className:"w-full",children:[g?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eR.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo"]})]})]}),(0,t.jsx)(eA.Es,{children:(0,t.jsx)(l.$,{variant:"outline",onClick:a,disabled:g,children:"Chiudi"})})]})})}var eD=i(6727),eL=i(41312);function eB({open:e,onClose:a,caviSelezionati:i,tipoComanda:r,onSuccess:n,onError:m}){let{cantiere:x}=(0,c.A)(),[u,p]=(0,s.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[v,b]=(0,s.useState)([]),[g,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(!1),[y,_]=(0,s.useState)(""),C=async()=>{if(x){if(!u.responsabile)return void _("Seleziona un responsabile per la comanda");if(0===i.length)return void _("Seleziona almeno un cavo per la comanda");try{j(!0),_("");let e={tipo_comanda:u.tipo_comanda,responsabile:u.responsabile,note:u.note||null},t=await d.CV.createComandaWithCavi(x.id_cantiere,e,i);n(`Comanda ${t.data.codice_comanda} creata con successo per ${i.length} cavi`),a()}catch(e){m(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{j(!1)}}};return(0,t.jsx)(eA.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eA.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eA.c7,{children:[(0,t.jsxs)(eA.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eD.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsxs)(eA.rr,{children:["Crea una nuova comanda per ",i.length," cavi selezionati"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)(ek.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",i.length,")"]}),(0,t.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[i.slice(0,10).map(e=>(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),i.length>10&&(0,t.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",i.length-10," altri..."]})]})})]}),y&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(h.l6,{value:u.tipo_comanda,onValueChange:e=>p(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,t.jsx)(h.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,t.jsx)(h.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,t.jsx)(h.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,t.jsxs)(h.l6,{value:u.responsabile,onValueChange:e=>p(a=>({...a,responsabile:e})),disabled:f,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:v.map(e=>(0,t.jsx)(h.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eL.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(eF.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:u.note,onChange:e=>p(a=>({...a,note:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(u.tipo_comanda)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Responsabile:"})," ",u.responsabile||"Non selezionato"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cavi:"})," ",i.length," selezionati"]}),u.note&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Note:"})," ",u.note]})]})]})]}),(0,t.jsxs)(eA.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:a,disabled:g,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:C,disabled:g||!u.responsabile||0===i.length,children:[g?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eD.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var eq=i(16023);let eV=(0,ea.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);function eU({open:e,onClose:a,tipo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,s.useState)(null),[h,v]=(0,s.useState)(""),[b,g]=(0,s.useState)(!1),[j,f]=(0,s.useState)(""),[N,y]=(0,s.useState)(0),_=(0,s.useRef)(null),C=async()=>{if(x&&m){if("cavi"===i&&!h.trim())return void f("Inserisci il codice revisione per l'importazione cavi");try{let e;if(g(!0),f(""),y(0),e="cavi"===i?await d.mg.importCavi(m.id_cantiere,x,h.trim()):await d.mg.importBobine(m.id_cantiere,x),y(100),e.data.success){let t=e.data.details,s=e.data.message;"cavi"===i&&t?.cavi_importati?s+=` (${t.cavi_importati} cavi importati)`:"bobine"===i&&t?.bobine_importate&&(s+=` (${t.bobine_importate} bobine importate)`),r(s),a()}else n(e.data.message||"Errore durante l'importazione")}catch(e){n(e.response?.data?.detail||e.message||"Errore durante l'importazione del file")}finally{g(!1),y(0)}}},w=()=>{b||(u(null),v(""),f(""),y(0),_.current&&(_.current.value=""),a())},z=()=>"cavi"===i?"Cavi":"Bobine";return(0,t.jsx)(eA.lG,{open:e,onOpenChange:w,children:(0,t.jsxs)(eA.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eA.c7,{children:[(0,t.jsxs)(eA.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eq.A,{className:"h-5 w-5"}),"Importa ",z()," da Excel"]}),(0,t.jsxs)(eA.rr,{children:["Carica un file Excel per importare ",z().toLowerCase()," nel cantiere"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===i?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,t.jsx)("span",{children:e})]},a))})]}),j&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:j})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"file",children:"File Excel *"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.p,{ref:_,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{let a=e.target.files?.[0];if(a){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(a.type)&&!a.name.toLowerCase().endsWith(".xlsx")&&!a.name.toLowerCase().endsWith(".xls"))return void f("Seleziona un file Excel valido (.xlsx o .xls)");u(a),f("")}},disabled:b,className:"flex-1"}),x&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(eN.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),x&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)(eV,{className:"h-4 w-4 inline mr-1"}),x.name," (",(x.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===i&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ek.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,t.jsx)(p.p,{id:"revisione",value:h,onChange:e=>v(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:b}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),b&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${N}%`}})})]}),x&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",z()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File:"})," ",x.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Dimensione:"})," ",(x.size/1024/1024).toFixed(2)," MB"]}),"cavi"===i&&h&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Revisione:"})," ",h]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",m?.nome_cantiere]})]})]})]}),(0,t.jsxs)(eA.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:w,disabled:b,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:C,disabled:b||!x||"cavi"===i&&!h.trim(),children:[b?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eq.A,{className:"h-4 w-4 mr-2"}),"Importa ",z()]})]})]})})}var eG=i(61611);function eJ({open:e,onClose:a,onSuccess:i,onError:r}){let{cantiere:n}=(0,c.A)(),[m,u]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[p,h]=(0,s.useState)(!1),[v,b]=(0,s.useState)(""),g=(e,a)=>{u(i=>({...i,[e]:a}))},j=async()=>{if(n)try{h(!0);let e=await d.mg.exportCavi(n.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download",`cavi_${n.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a),i("Export cavi completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei cavi")}finally{h(!1)}},f=async()=>{if(n)try{h(!0);let e=await d.mg.exportBobine(n.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download",`bobine_${n.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a),i("Export bobine completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export delle bobine")}finally{h(!1)}},N=async()=>{if(n)try{h(!0),b("");let e=[];m.cavi&&e.push(j()),m.bobine&&e.push(f()),m.comande,m.certificazioni,m.responsabili,await Promise.all(e);let t=Object.values(m).filter(Boolean).length;i(`Export completato: ${t} file scaricati`),a()}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei dati")}finally{h(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,t.jsx)(eG.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,t.jsx)(eV,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,t.jsx)(eV,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,t.jsx)(eV,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,t.jsx)(eV,{className:"h-4 w-4"}),available:!1}];return(0,t.jsx)(eA.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eA.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eA.c7,{children:[(0,t.jsxs)(eA.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eM.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,t.jsxs)(eA.rr,{children:["Seleziona i dati da esportare dal cantiere ",n?.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[v&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:v})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,t.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border ${e.available?"bg-white":"bg-gray-50"}`,children:[(0,t.jsx)(x.S,{id:e.key,checked:m[e.key],onCheckedChange:a=>g(e.key,a),disabled:!e.available||p}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)(ek.J,{htmlFor:e.key,className:`font-medium ${!e.available?"text-gray-500":""}`,children:[e.label,!e.available&&(0,t.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,t.jsx)("p",{className:`text-sm mt-1 ${!e.available?"text-gray-400":"text-gray-600"}`,children:e.description})]})]},e.key))]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,t.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,t.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,t.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(m).filter(Boolean).length>0&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(ek.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",n?.nome_cantiere]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(m).filter(Boolean).length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,t.jsxs)(eA.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:a,disabled:p,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:N,disabled:p||0===Object.values(m).filter(Boolean).length,children:[p?(0,t.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eM.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(m).filter(Boolean).length>0?`(${Object.values(m).filter(Boolean).length})`:""]})]})]})})}function eZ(){let{user:e,cantiere:a,isAuthenticated:i,isLoading:m}=(0,c.A)();(0,r.useRouter)(),console.log({cantiereFromAuth:a,user:e,userRole:e?.ruolo});let x=({title:e,description:a,variant:i})=>{},[u,p]=(0,s.useState)([]),[h,v]=(0,s.useState)([]),[b,g]=(0,s.useState)(!0),[j,f]=(0,s.useState)(""),[N,y]=(0,s.useState)([]),[_,C]=(0,s.useState)(!0),[w,z]=(0,s.useState)([]),[A,k]=(0,s.useState)(""),[S,E]=(0,s.useState)({open:!1,cavo:null}),[O,I]=(0,s.useState)({open:!1,cavo:null}),[$,T]=(0,s.useState)({open:!1,cavo:null}),[F,R]=(0,s.useState)({open:!1,cavo:null}),[M,P]=(0,s.useState)({open:!1}),[D,L]=(0,s.useState)({open:!1}),[B,q]=(0,s.useState)(!1),[V,U]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[G,J]=(0,s.useState)(0),Z=a||(G>0?{id_cantiere:G,commessa:`Cantiere ${G}`}:null),W=async()=>{try{g(!0),f("");try{let e=await d.At.getCavi(G),a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);p(a),v(i),H(a)}catch(e){try{let e=await fetch(`http://localhost:8001/api/cavi/debug/${G}`),a=await e.json();if(a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),i=a.cavi.filter(e=>e.spare);p(e),v(i),H(e),f("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw e}}}catch(e){f(`Errore nel caricamento dei cavi: ${e.response?.data?.detail||e.message}`)}finally{g(!1)}},H=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,t=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,r=e.reduce((e,a)=>e+(a.metri_teorici||0),0),n=e.reduce((e,a)=>e+(a.metri_posati||0),0);U({totali:a,installati:i,collegati:t,certificati:s,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(t/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:r,metriInstallati:n,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},Y=(e,a,i)=>{switch(a){case"insert_meters":E({open:!0,cavo:e});break;case"modify_reel":I({open:!0,cavo:e});break;case"view_command":x({title:"Visualizza Comanda",description:`Apertura comanda ${i} per cavo ${e.id_cavo}`});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":T({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":R({open:!0,cavo:e})}},X=(e,a)=>{switch(a){case"view_details":x({title:"Visualizza Dettagli",description:`Apertura dettagli per cavo ${e.id_cavo}`});break;case"edit":x({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":x({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":x({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":N.includes(e.id_cavo)?(y(N.filter(a=>a!==e.id_cavo)),x({title:"Cavo Deselezionato",description:`Cavo ${e.id_cavo} deselezionato`})):(y([...N,e.id_cavo]),x({title:"Cavo Selezionato",description:`Cavo ${e.id_cavo} selezionato`}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),x({title:"ID Copiato",description:`ID cavo ${e.id_cavo} copiato negli appunti`});break;case"copy_details":let i=`ID: ${e.id_cavo}, Tipologia: ${e.tipologia}, Formazione: ${e.formazione||e.sezione}, Metri: ${e.metri_teorici}`;navigator.clipboard.writeText(i),x({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":x({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":x({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":P({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":P({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":P({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":P({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":x({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":x({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:x({title:"Azione non implementata",description:`Azione ${a} non ancora implementata`})}},K=e=>{x({title:"Operazione completata",description:e}),W()},Q=e=>{x({title:"Errore",description:e,variant:"destructive"})};return m||b?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)(eE.A,{className:"h-8 w-8 animate-spin"})}):G?j?(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:j})]}),(0,t.jsx)(l.$,{onClick:W,className:"mt-4",children:"Riprova"})]}):(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsx)(ez,{cavi:u,filteredCavi:w,revisioneCorrente:A,className:"mb-2"}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(eg,{cavi:u,loading:b,selectionEnabled:_,selectedCavi:N,onSelectionChange:y,onStatusAction:Y,onContextMenuAction:X})}),h.length>0&&(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(ew.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Cavi Spare (",h.length,")"]})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(eg,{cavi:h,loading:b,selectionEnabled:!1,onStatusAction:Y,onContextMenuAction:X})})]})}),!1,(0,t.jsx)(e$,{open:S.open,onClose:()=>E({open:!1,cavo:null}),cavo:S.cavo,cantiere:Z,onSuccess:K,onError:Q}),(0,t.jsx)(eI,{open:O.open,onClose:()=>I({open:!1,cavo:null}),cavo:O.cavo,cantiere:Z,onSuccess:K,onError:Q}),(0,t.jsx)(eT,{open:$.open,onClose:()=>T({open:!1,cavo:null}),cavo:$.cavo,onSuccess:K,onError:Q}),(0,t.jsx)(eP,{open:F.open,onClose:()=>R({open:!1,cavo:null}),cavo:F.cavo,onSuccess:K,onError:Q}),(0,t.jsx)(eB,{open:M.open,onClose:()=>P({open:!1}),caviSelezionati:N,tipoComanda:M.tipoComanda,onSuccess:K,onError:Q}),(0,t.jsx)(eU,{open:D.open,onClose:()=>L({open:!1}),tipo:D.tipo||"cavi",onSuccess:K,onError:Q}),(0,t.jsx)(eJ,{open:B,onClose:()=>q(!1),onSuccess:K,onError:Q})]}):(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(eO.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]}),(0,t.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,t.jsx)("h3",{className:"font-bold",children:"Debug Info:"}),(0,t.jsxs)("p",{children:["User: ",e?e.username:"Non autenticato"]}),(0,t.jsxs)("p",{children:["Cantiere context: ",a?a.commessa:"Nessuno"]}),(0,t.jsxs)("p",{children:["Token presente: ","N/A"]})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,i)=>{"use strict";i.d(a,{S:()=>l});var t=i(60687);i(43210);var s=i(40211),r=i(13964),n=i(4780);function l({className:e,...a}){return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(r.A,{className:"size-3.5"})})})}},61611:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,i)=>{"use strict";i.d(a,{Cf:()=>m,Es:()=>u,L3:()=>p,c7:()=>x,lG:()=>l,rr:()=>h,zM:()=>o});var t=i(60687);i(43210);var s=i(26134),r=i(11860),n=i(4780);function l({...e}){return(0,t.jsx)(s.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:i=!0,...l}){return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[a,i&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(r.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function u({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function p({className:e,...a}){return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...a})}function h({className:e,...a}){return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...a})}},65831:(e,a,i)=>{Promise.resolve().then(i.bind(i,51400))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86561:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},86691:(e,a,i)=>{"use strict";i.r(a),i.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=i(65239),s=i(48088),r=i(88170),n=i.n(r),l=i(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);i.d(a,o);let c={children:["",{children:["cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,10698)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cavi/page",pathname:"/cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../webpack-runtime.js");a.C(e);var i=e=>a(a.s=e),t=a.X(0,[447,124,658,952,400,995,891,807,109],()=>i(86691));module.exports=t})();