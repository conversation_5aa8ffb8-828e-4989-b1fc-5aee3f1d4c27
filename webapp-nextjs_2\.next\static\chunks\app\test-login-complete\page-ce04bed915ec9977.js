(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3149],{23227:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:i=!1,...c}=e,l=i?r.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),s),...c})}},30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var a=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:t,size:i,asChild:c=!1,...l}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:s})),...l})}},39077:(e,s,t)=>{Promise.resolve().then(t.bind(t,86955))},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51154:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},59434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(52596),r=t(39688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=t(95155);t(12115);var r=t(59434);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}},71007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},86955:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),r=t(12115),i=t(66695),n=t(30285),d=t(26126),c=t(40283),l=t(40646),o=t(54861),u=t(75525),x=t(51154),h=t(71007),m=t(23227);function g(){let[e,s]=(0,r.useState)({}),[t,g]=(0,r.useState)(!1),{login:v,loginCantiere:p,logout:b,user:j,cantiere:f,isAuthenticated:N}=(0,c.A)(),y=async(e,t)=>{g(!0);try{let a=await t();s(s=>({...s,[e]:{success:!0,result:a}}))}catch(t){s(s=>({...s,[e]:{success:!1,error:t.message||t}}))}finally{g(!1)}},w=async()=>(await b(),await v("admin","admin")),k=async()=>(await b(),await v("testuser2","test123")),A=async()=>(await b(),await p("MU258UC","test123")),C=async()=>(await b(),await v("invalid","invalid")),_=(e,s)=>s?(0,a.jsxs)("div",{className:"p-3 rounded-lg border ".concat(s.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[s.success?(0,a.jsx)(l.A,{className:"w-4 h-4 text-green-600"}):(0,a.jsx)(o.A,{className:"w-4 h-4 text-red-600"}),(0,a.jsx)("span",{className:"font-medium ".concat(s.success?"text-green-800":"text-red-800"),children:e})]}),(0,a.jsx)("pre",{className:"text-xs ".concat(s.success?"text-green-700":"text-red-700"),children:JSON.stringify(s.success?s.result:s.error,null,2)})]}):null;return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 p-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"w-8 h-8 text-white"})})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"Test Sistema Login Completo"}),(0,a.jsx)("p",{className:"text-slate-600",children:"webapp-nextjs_2 - Verifica tutti i tipi di autenticazione"})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Stato Autenticazione Corrente"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(d.E,{variant:N?"default":"secondary",children:N?"Autenticato":"Non Autenticato"})}),j&&(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("p",{className:"font-medium text-blue-900",children:["Utente: ",j.username]}),(0,a.jsxs)("p",{className:"text-blue-700",children:["Ruolo: ",j.ruolo]}),(0,a.jsxs)("p",{className:"text-blue-700",children:["ID: ",j.id_utente]})]}),f&&(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("p",{className:"font-medium text-green-900",children:["Cantiere: ",f.commessa]}),(0,a.jsxs)("p",{className:"text-green-700",children:["Codice: ",f.codice_univoco]}),(0,a.jsxs)("p",{className:"text-green-700",children:["ID: ",f.id_cantiere]})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Test di Login"}),(0,a.jsx)(i.BT,{children:"Testa tutti i tipi di login supportati dal sistema"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)(n.$,{onClick:()=>y("Admin Login",w),disabled:t,className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),"Test Admin Login",t&&(0,a.jsx)(x.A,{className:"w-4 h-4 animate-spin"})]}),(0,a.jsxs)(n.$,{onClick:()=>y("User Login",k),disabled:t,variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),"Test User Login",t&&(0,a.jsx)(x.A,{className:"w-4 h-4 animate-spin"})]}),(0,a.jsxs)(n.$,{onClick:()=>y("Cantiere Login",A),disabled:t,variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Test Cantiere Login",t&&(0,a.jsx)(x.A,{className:"w-4 h-4 animate-spin"})]}),(0,a.jsxs)(n.$,{onClick:()=>y("Invalid Login",C),disabled:t,variant:"destructive",className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),"Test Invalid Login",t&&(0,a.jsx)(x.A,{className:"w-4 h-4 animate-spin"})]})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(n.$,{onClick:b,disabled:t,variant:"secondary",className:"w-full",children:"Logout"})})]})]}),Object.keys(e).length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Risultati Test"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:Object.entries(e).map(e=>{let[s,t]=e;return(0,a.jsx)("div",{children:_(s,t)},s)})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Credenziali di Test"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Admin/Owner"}),(0,a.jsx)("p",{className:"text-blue-700",children:"Username: admin"}),(0,a.jsx)("p",{className:"text-blue-700",children:"Password: admin"}),(0,a.jsx)("p",{className:"text-blue-700",children:"Ruolo: owner"})]}),(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"User Standard"}),(0,a.jsx)("p",{className:"text-green-700",children:"Username: testuser2"}),(0,a.jsx)("p",{className:"text-green-700",children:"Password: test123"}),(0,a.jsx)("p",{className:"text-green-700",children:"Ruolo: user"})]}),(0,a.jsxs)("div",{className:"p-3 bg-orange-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-orange-900 mb-2",children:"Cantiere"}),(0,a.jsx)("p",{className:"text-orange-700",children:"Codice: MU258UC"}),(0,a.jsx)("p",{className:"text-orange-700",children:"Password: test123"}),(0,a.jsx)("p",{className:"text-orange-700",children:"Ruolo: cantieri_user"}),(0,a.jsx)("p",{className:"text-orange-600 text-xs mt-1",children:"⚠️ Login cantiere ha errori backend"})]})]})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3464,3455,283,8441,1684,7358],()=>s(39077)),_N_E=e.O()}]);