'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import { Building2, Plus, Search, Loader2, Eye, EyeOff, Copy, Settings, Lock } from 'lucide-react'

export default function CantieriPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [cantieriStats, setCantieriStats] = useState<Record<number, { percentuale_avanzamento: number }>>({})
  const [statsLoading, setStatsLoading] = useState(false)
  const [passwordVisibility, setPasswordVisibility] = useState<Record<number, boolean>>({})
  const [revealedPasswords, setRevealedPasswords] = useState<Record<number, string>>({})

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      loadCantieri()
    }
  }, [isAuthenticated])

  const loadCantieri = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantieri()
      setCantieri(data)

      // Carica le statistiche per ogni cantiere
      await loadCantieriStatistics(data)
    } catch (error) {
      setError('Errore nel caricamento dei cantieri')
    } finally {
      setLoading(false)
    }
  }

  const loadCantieriStatistics = async (cantieriData: Cantiere[]) => {
    try {
      setStatsLoading(true)
      const statsPromises = cantieriData.map(async (cantiere) => {
        try {
          const stats = await cantieriApi.getCantiereStatistics(cantiere.id_cantiere)
          return { id: cantiere.id_cantiere, stats }
        } catch (error) {
          console.error(`Errore nel caricamento statistiche cantiere ${cantiere.id_cantiere}:`, error)
          return { id: cantiere.id_cantiere, stats: { percentuale_avanzamento: 0 } }
        }
      })

      const results = await Promise.all(statsPromises)
      const statsMap = results.reduce((acc, { id, stats }) => {
        acc[id] = stats
        return acc
      }, {} as Record<number, { percentuale_avanzamento: number }>)

      setCantieriStats(statsMap)
    } catch (error) {
      console.error('Errore nel caricamento delle statistiche:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  const togglePasswordVisibility = async (cantiere: Cantiere) => {
    const cantiereId = cantiere.id_cantiere
    const isCurrentlyVisible = passwordVisibility[cantiereId]

    if (isCurrentlyVisible) {
      // Nascondi la password
      setPasswordVisibility(prev => ({ ...prev, [cantiereId]: false }))
      setRevealedPasswords(prev => ({ ...prev, [cantiereId]: '' }))
    } else {
      // Mostra la password - carica se non già caricata
      if (!revealedPasswords[cantiereId]) {
        try {
          const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
          const token = localStorage.getItem('token') || localStorage.getItem('access_token')
          const response = await fetch(`${backendUrl}/api/cantieri/${cantiereId}/view-password`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.detail || 'Errore nel recupero password')
          }

          const data = await response.json()
          setRevealedPasswords(prev => ({ ...prev, [cantiereId]: data.password_cantiere }))
          setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Errore nel recupero password')
        }
      } else {
        // Password già caricata, mostra semplicemente
        setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))
      }
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const filteredCantieri = cantieri.filter(cantiere =>
    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="max-w-[90%] mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca cantieri..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full"
            />
          </div>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Nuovo Cantiere
        </Button>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Commessa</TableHead>
              <TableHead>Descrizione</TableHead>
              <TableHead>Cliente</TableHead>
              <TableHead>Avanzamento</TableHead>
              <TableHead>Data Creazione</TableHead>
              <TableHead>Codice</TableHead>
              <TableHead>Password</TableHead>
              <TableHead className="text-right">Azioni</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCantieri.map((cantiere) => {
              const stats = cantieriStats[cantiere.id_cantiere]
              const progressPercentage = stats?.percentuale_avanzamento || 0
              const isPasswordVisible = passwordVisibility[cantiere.id_cantiere]
              const revealedPassword = revealedPasswords[cantiere.id_cantiere]

              return (
                <TableRow key={cantiere.id_cantiere}>
                  <TableCell className="font-medium">{cantiere.commessa}</TableCell>
                  <TableCell>{cantiere.descrizione}</TableCell>
                  <TableCell>{cantiere.nome_cliente}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progressPercentage}%` }}
                        />
                      </div>
                      <span className="text-sm text-muted-foreground min-w-[3rem]">
                        {progressPercentage.toFixed(1)}%
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {cantiere.codice_univoco}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(cantiere.codice_univoco)}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {cantiere.password_cantiere ? (
                        <>
                          <code className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
                            {isPasswordVisible ? revealedPassword : '••••••••'}
                          </code>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => togglePasswordVisibility(cantiere)}
                            className="h-6 w-6 p-0"
                          >
                            {isPasswordVisible ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                          </Button>
                          {isPasswordVisible && revealedPassword && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => copyToClipboard(revealedPassword)}
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          )}
                        </>
                      ) : (
                        <code className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded">
                          Non impostata
                        </code>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center gap-2 justify-end">
                      <Button size="sm" variant="outline">
                        <Settings className="mr-1 h-3 w-3" />
                        Gestisci
                      </Button>
                      <Button size="sm" variant="outline">
                        <Lock className="mr-1 h-3 w-3" />
                        Password
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </Card>
    </div>
  )
}
