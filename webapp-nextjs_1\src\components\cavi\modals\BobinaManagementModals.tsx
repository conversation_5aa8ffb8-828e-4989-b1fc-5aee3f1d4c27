'use client'

import React, { useState, useEffect, useMemo } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Search,
  CheckCircle,
  AlertTriangle,
  Ruler,
  Package,
  X,
  Loader2,
  HelpCircle,
  Settings
} from 'lucide-react'
import { Cavo } from '@/types'
import { parcoCaviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useCantiere } from '@/hooks/useCantiere'

// Enhanced Modal Components (imported from CableActionModals)
interface EnhancedModalHeaderProps {
  icon: React.ReactNode
  title: string
  cableId: string
  description?: string
}

const EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({
  icon,
  title,
  cableId,
  description
}) => (
  <DialogHeader>
    <DialogTitle className="flex items-center gap-2">
      {icon}
      <span>{title}</span>
    </DialogTitle>
    {description && (
      <DialogDescription className="text-sm text-muted-foreground">
        {description}
      </DialogDescription>
    )}
  </DialogHeader>
)

interface EnhancedDialogContentProps {
  children: React.ReactNode
  className?: string
  onKeyDown?: (e: React.KeyboardEvent) => void
  ariaLabelledBy?: string
  ariaDescribedBy?: string
}

const EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({
  children,
  className = "sm:max-w-md",
  onKeyDown,
  ariaLabelledBy,
  ariaDescribedBy
}) => (
  <DialogContent 
    className={className}
    onKeyDown={onKeyDown}
    aria-labelledby={ariaLabelledBy}
    aria-describedby={ariaDescribedBy}
    onPointerDownOutside={(e) => e.preventDefault()}
    onEscapeKeyDown={(e) => {
      if (onKeyDown) {
        onKeyDown(e as any)
      }
    }}
  >
    {children}
  </DialogContent>
)

// Types
interface Bobina {
  id_bobina: string
  numero_bobina: string
  tipologia: string
  n_conduttori: string
  sezione: string
  metri_totali: number
  metri_residui: number
  stato_bobina: string
  fornitore?: string
  compatible?: boolean
}

interface ModificaBobinaModalProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  cantiere?: { id_cantiere: string | number; commessa: string } | null
  onSave: (cavoId: string, bobinaId: string, option: string) => Promise<void>
}

interface InserisciMetriModalProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSave: (cavoId: string, metriPosati: number) => Promise<void>
}

// Unified Modal Props
interface UnifiedCableBobbinModalProps {
  mode: 'aggiungi_metri' | 'modifica_bobina'
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  cantiere?: { id_cantiere: string | number; commessa: string } | null
  onSave: (data: any) => Promise<void>
}

// Edit options for modifica_bobina mode
type EditOption = 'cambia_bobina' | 'bobina_vuota' | 'annulla_posa'

// Cable Info Card Component
interface CableInfoCardProps {
  cavo: Cavo
}

const CableInfoCard: React.FC<CableInfoCardProps> = ({ cavo }) => (
  <div className="bg-blue-50 border border-blue-200 rounded-lg p-5 mb-5">
    {/* Titolo della sezione migliorato - rimosso codice cavo duplicato */}
    <div className="flex items-center gap-3 mb-4">
      <Package className="h-5 w-5 text-blue-600" />
      <h3 className="font-semibold text-blue-800 text-base">Informazioni Cavo</h3>
    </div>

    {/* Griglia 2x2 con gerarchia visiva migliorata - Opzione A */}
    <div className="grid grid-cols-2 gap-x-8 gap-y-4">
      <div className="info-item">
        <span className="block text-xs text-gray-500 font-medium uppercase mb-1">Tipologia</span>
        <span className="text-gray-900 font-bold text-sm">{cavo.tipologia || 'N/A'}</span>
      </div>

      <div className="info-item">
        <span className="block text-xs text-gray-500 font-medium uppercase mb-1">Formazione</span>
        <span className="text-gray-900 font-bold text-sm">{cavo.sezione || 'N/A'}</span>
      </div>

      <div className="info-item">
        <span className="block text-xs text-gray-500 font-medium uppercase mb-1">Da</span>
        <span className="text-gray-900 font-bold text-sm">{cavo.ubicazione_partenza || 'N/A'}</span>
      </div>

      <div className="info-item">
        <span className="block text-xs text-gray-500 font-medium uppercase mb-1">A</span>
        <span className="text-gray-900 font-bold text-sm">{cavo.ubicazione_arrivo || 'N/A'}</span>
      </div>
    </div>
  </div>
)

// ==================== UNIFIED CABLE BOBBIN MODAL ====================

export const UnifiedCableBobbinModal: React.FC<UnifiedCableBobbinModalProps> = ({
  mode,
  open,
  onClose,
  cavo,
  cantiere,
  onSave
}) => {
  // Stati comuni
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [bobine, setBobine] = useState<Bobina[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedBobina, setSelectedBobina] = useState<string>('')

  // Stati specifici per modalità
  const [metersInput, setMetersInput] = useState<string>('')
  const [selectedEditOption, setSelectedEditOption] = useState<EditOption | null>(null)
  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')

  const { user } = useAuth()
  const { cantiereId: cantiereIdFromHook, cantiere: cantiereFromHook, isValidCantiere: isValidCantiereFromHook } = useCantiere()

  // Usa il cantiere passato come prop o quello dal sistema universale come fallback (come InserisciMetriDialog)
  const cantiereToUse = cantiere || cantiereFromHook
  const cantiereId = cantiereToUse?.id_cantiere || cantiereIdFromHook
  const isValidCantiere = cantiereToUse ? true : isValidCantiereFromHook

  // Logica per determinare titolo e descrizione dinamici
  const modalConfig = useMemo(() => {
    if (mode === 'aggiungi_metri') {
      // Titolo corretto: "Inserisci Metri Posati: C001"
      const title = `Inserisci Metri Posati: ${cavo?.id_cavo || 'N/A'}`

      return {
        title: title,
        description: 'Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA',
        icon: <Ruler className="h-5 w-5 text-green-500" />,
        primaryButtonText: 'Salva'
      }
    } else {
      const bobinaNumber = cavo?.id_bobina ?
        cavo.id_bobina.split('_B')[1] || cavo.id_bobina.split('_b')[1] || cavo.id_bobina : ''

      // Titolo corretto: "Modifica Bobina Cavo: C001 / Bobina: 2"
      let title = `Modifica Bobina Cavo: ${cavo?.id_cavo || 'N/A'}`

      if (bobinaNumber) {
        title += ` / Bobina: ${bobinaNumber}`
      }

      return {
        title: title,
        description: 'Seleziona una nuova bobina per il cavo o modifica i parametri',
        icon: <Settings className="h-5 w-5 text-blue-500" />,
        primaryButtonText: 'Salva Modifiche'
      }
    }
  }, [mode, cavo?.id_cavo, cavo?.id_bobina]) // Aggiunte dipendenze specifiche

  // Determina se la sezione selezione bobina è abilitata
  const isBobbinSelectionEnabled = useMemo(() => {
    if (mode === 'aggiungi_metri') return true
    if (mode === 'modifica_bobina') return true // SEMPRE visibile in modifica bobina
    return false
  }, [mode])

  // Reset stati quando si apre/chiude la modale
  useEffect(() => {
    if (open) {
      setError('')
      setSearchTerm('')
      setSelectedBobina('')
      setSelectedEditOption(null)

      if (mode === 'aggiungi_metri') {
        setMetersInput('')
      } else if (mode === 'modifica_bobina' && cavo) {
        setMetersInput(String(cavo.metratura_reale || cavo.metri_posati || 0))
      }
    }
  }, [open, mode, cavo])

  // Carica bobine dall'API usando il sistema universale
  const loadBobine = async () => {
    if (!cavo || !cantiereId || !isValidCantiere) {
      console.log('🔍 UnifiedModal: Caricamento bobine saltato - mancano dati:', {
        cavo: !!cavo,
        cantiereId,
        isValidCantiere,
        cantiereToUse: !!cantiereToUse
      })
      return
    }

    console.log('🔄 UnifiedModal: Caricamento bobine per cantiere:', cantiereId)
    console.log('🔐 UnifiedModal: Debug autenticazione:', {
      user: !!user,
      token: !!localStorage.getItem('token'),
      tokenLength: localStorage.getItem('token')?.length || 0
    })
    setIsLoading(true)

    try {
      const response = await parcoCaviApi.getBobine(cantiereId)

      // Gestisce diversi formati di risposta (come InserisciMetriDialog)
      let bobineData = []
      if (Array.isArray(response)) {
        bobineData = response
      } else if (response && Array.isArray(response.data)) {
        bobineData = response.data
      } else if (response && response.bobine && Array.isArray(response.bobine)) {
        bobineData = response.bobine
      } else {
        throw new Error('Formato risposta API non valido')
      }

      console.log('📦 UnifiedModal: Bobine ricevute:', bobineData.length)
      console.log('📋 UnifiedModal: Dettaglio bobine:', bobineData.map(b => ({
        id: b.id_bobina,
        tipologia: b.tipologia,
        sezione: b.sezione,
        metri_residui: b.metri_residui,
        stato: b.stato_bobina
      })))

      // Filtra bobine utilizzabili (stessa logica delle modali esistenti)
      const bobineUtilizzabili = bobineData.filter((bobina: any) =>
        bobina.stato_bobina !== 'Terminata' &&
        bobina.stato_bobina !== 'Over' &&
        bobina.metri_residui > 0
      )

      console.log('✅ UnifiedModal: Bobine utilizzabili:', bobineUtilizzabili.length)

      // Determina compatibilità
      const bobineWithCompatibility = bobineUtilizzabili.map((bobina: any) => ({
        ...bobina,
        compatible: bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione
      }))

      const compatibili = bobineWithCompatibility.filter(b => b.compatible).length
      const incompatibili = bobineWithCompatibility.filter(b => !b.compatible).length

      console.log('🎯 UnifiedModal: Compatibilità bobine:', { compatibili, incompatibili })

      setBobine(bobineWithCompatibility)
      setError('') // Pulisci eventuali errori precedenti
    } catch (error) {
      console.error('❌ UnifiedModal: Errore caricamento bobine:', error)
      setError('Errore durante il caricamento delle bobine. Riprova.')
      setBobine([])
    } finally {
      setIsLoading(false)
    }
  }

  // Carica bobine quando si apre la modale o cambia il cantiere
  useEffect(() => {
    if (open && cavo && cantiereId && isValidCantiere) {
      loadBobine()
    }
  }, [open, cavo, cantiereId, isValidCantiere])

  // Filtra bobine in base alla ricerca
  const filteredBobine = useMemo(() => {
    if (!searchTerm) return bobine

    const searchLower = searchTerm.toLowerCase()
    return bobine.filter(bobina => {
      const idBobina = bobina.id_bobina?.toLowerCase() || ''
      const tipologia = bobina.tipologia?.toLowerCase() || ''
      const numeroBobina = bobina.numero_bobina?.toLowerCase() || ''

      return idBobina.includes(searchLower) ||
             tipologia.includes(searchLower) ||
             numeroBobina.includes(searchLower)
    })
  }, [bobine, searchTerm])

  // Separa bobine compatibili e incompatibili
  const compatibleBobine = filteredBobine.filter(b => b.compatible)
  const incompatibleBobine = filteredBobine.filter(b => !b.compatible)

  // Validazione input metri con messaggi migliorati
  const validateMeters = (value: string): string => {
    if (!value.trim()) return 'Il campo metri è obbligatorio'
    const meters = parseFloat(value)
    if (isNaN(meters)) return 'Inserire un valore numerico valido'
    if (meters < 0) return 'I metri non possono essere negativi'
    if (meters === 0) return 'I metri devono essere maggiori di zero'
    if (meters > 10000) return 'Valore troppo elevato (massimo 10.000m)'
    return ''
  }

  // Gestione errori con toast notifications
  const showError = (message: string) => {
    setError(message)
    // Auto-clear error after 5 seconds
    setTimeout(() => setError(''), 5000)
  }

  // Gestione salvataggio
  const handleSave = async () => {
    if (!cavo) return

    setError('')
    setIsLoading(true)

    try {
      let payload: any = {}

      if (mode === 'aggiungi_metri') {
        // Validazione per aggiungi metri
        const metersError = validateMeters(metersInput)
        if (metersError) {
          showError(metersError)
          setIsLoading(false)
          return
        }

        payload = {
          mode: 'aggiungi_metri',
          cableId: cavo.id_cavo,
          metersToInstall: parseFloat(metersInput),
          bobbinId: selectedBobina || 'BOBINA_VUOTA'
        }
      } else if (mode === 'modifica_bobina') {
        // Validazione per modifica bobina
        if (!selectedEditOption) {
          showError('Selezionare un\'opzione di modifica')
          setIsLoading(false)
          return
        }

        if (selectedEditOption === 'cambia_bobina' && !selectedBobina) {
          showError('Selezionare una bobina per continuare')
          setIsLoading(false)
          return
        }

        payload = {
          mode: 'modifica_bobina',
          cableId: cavo.id_cavo,
          editOption: selectedEditOption,
          newBobbinId: selectedBobina || null
        }
      }

      await onSave(payload)
      onClose()
    } catch (error: any) {
      console.error('Errore salvataggio unified modal:', error)
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Errore durante il salvataggio. Riprovare.'
      showError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Gestione chiusura
  const handleClose = () => {
    if (!isLoading) {
      onClose()
    }
  }

  // Gestione tasti
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose()
    }
  }

  // Determina se il form è valido
  const isFormValid = useMemo(() => {
    if (mode === 'aggiungi_metri') {
      return metersInput.trim() && !validateMeters(metersInput)
    } else if (mode === 'modifica_bobina') {
      if (!selectedEditOption) return false

      // Validazione metri per tutte le opzioni tranne annulla_posa
      if (selectedEditOption !== 'annulla_posa') {
        if (!metersInput.trim() || validateMeters(metersInput)) return false
      }

      // Validazione specifica per cambio bobina
      if (selectedEditOption === 'cambia_bobina' && !selectedBobina) return false

      return true
    }
    return false
  }, [mode, metersInput, selectedEditOption, selectedBobina])

  // Controllo di sicurezza: non renderizzare se non c'è un cavo valido
  if (!cavo || !cavo.id_cavo) {
    console.warn('⚠️ UnifiedCableBobbinModal: Tentativo di apertura senza cavo valido:', cavo)
    return null
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <EnhancedDialogContent
        className="sm:max-w-3xl max-h-[85vh] overflow-hidden"
        onKeyDown={handleKeyDown}
        ariaLabelledBy="unified-modal-title"
      >
        <EnhancedModalHeader
          icon={modalConfig.icon}
          title={modalConfig.title}
          cableId={cavo.id_cavo}
          description={modalConfig.description}
        />

        <div className="space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]">
          {/* Sezione Informazioni Cavo */}
          <CableInfoCard cavo={cavo} />

          {/* Messaggio di errore */}
          {error && (
            <Alert className="bg-red-50 border-red-200" role="alert" aria-live="polite">
              <AlertTriangle className="h-4 w-4 text-red-600" aria-hidden="true" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Sezione Metri da Installare (solo per aggiungi_metri) */}
          {mode === 'aggiungi_metri' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">

              {/* Campo metri e pulsante BOBINA VUOTA allineati */}
              <div className="flex gap-6 items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="meters-input" className="text-sm font-medium text-gray-700">
                      Metri Installati:
                    </Label>
                    <div className="relative">
                      <Input
                        id="meters-input"
                        type="number"
                        value={metersInput}
                        onChange={(e) => setMetersInput(e.target.value)}
                        placeholder="0"
                        min="0"
                        max="10000"
                        step="0.1"
                        className="pr-6 w-24"
                        disabled={isLoading}
                        aria-describedby={metersInput && validateMeters(metersInput) ? "meters-error" : undefined}
                        aria-invalid={metersInput && validateMeters(metersInput) ? "true" : "false"}
                      />
                      <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                        m
                      </span>
                    </div>
                  </div>
                  {metersInput && validateMeters(metersInput) && (
                    <p id="meters-error" className="text-red-600 text-sm mt-1" role="alert">
                      {validateMeters(metersInput)}
                    </p>
                  )}
                </div>

                {/* Pulsante BOBINA VUOTA allineato al design modifica_bobina */}
                <div className="flex gap-4 flex-1 justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedBobina('BOBINA_VUOTA')}
                    className={`text-xs font-medium border-2 px-3 py-1.5 ${
                      selectedBobina === 'BOBINA_VUOTA'
                        ? 'bg-blue-100 border-blue-500 text-blue-800 shadow-lg'
                        : 'border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400'
                    }`}
                    disabled={isLoading}
                  >
                    🔄 BOBINA VUOTA
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Sezione Modifica Bobina */}
          {mode === 'modifica_bobina' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">

              {/* Campo metri e pulsanti affiancati */}
              <div className="flex gap-6 items-center">
                <div className="flex-shrink-0">
                  <Label className="text-sm font-medium text-gray-700">
                    Metri Posati: <span className="font-bold text-lg">{cavo.metratura_reale || cavo.metri_posati || 0}m</span>
                  </Label>
                </div>

                {/* Pulsanti BOBINA VUOTA e ANNULLA INSTALLAZIONE - dimensioni ottimizzate */}
                <div className="flex gap-4 flex-1 justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedEditOption('bobina_vuota')
                      setSelectedBobina(null) // Deseleziona bobina quando clicco BOBINA VUOTA
                    }}
                    className={`text-xs font-medium border-2 px-3 py-1.5 ${
                      selectedEditOption === 'bobina_vuota'
                        ? 'bg-blue-100 border-blue-500 text-blue-800 shadow-lg'
                        : 'border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400'
                    }`}
                    disabled={isLoading}
                  >
                    🔄 BOBINA VUOTA
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedEditOption('annulla_posa')
                      setSelectedBobina(null) // Deseleziona bobina quando clicco ANNULLA INSTALLAZIONE
                    }}
                    className={`text-xs font-medium border-2 px-3 py-1.5 ${
                      selectedEditOption === 'annulla_posa'
                        ? 'bg-blue-100 border-blue-500 text-blue-800 shadow-lg'
                        : 'border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400'
                    }`}
                    disabled={isLoading}
                  >
                    ❌ ANNULLA INSTALLAZIONE
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Sezione Selezione Bobina (condizionale) */}
          {isBobbinSelectionEnabled && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <Package className="h-5 w-5" />
                Selezione Bobina
              </h3>

              {/* Campo di ricerca */}
              <div className="mb-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search-bobina"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Cerca bobina per ID, tipologia o numero..."
                    className="pl-10"
                    disabled={isLoading}
                  />
                </div>


              </div>

              {/* Tab per bobine compatibili/incompatibili */}
              <div className="flex border-b border-gray-200 mb-4">
                <button
                  onClick={() => setActiveTab('compatibili')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'compatibili'
                      ? 'border-green-500 text-green-600 bg-green-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Compatibili ({compatibleBobine.length})
                </button>
                <button
                  onClick={() => setActiveTab('incompatibili')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'incompatibili'
                      ? 'border-orange-500 text-orange-600 bg-orange-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Incompatibili ({incompatibleBobine.length})
                </button>
              </div>

              {/* Lista bobine con tab */}
              <div className="border rounded-lg h-64 overflow-y-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm text-gray-600">Caricamento bobine...</span>
                    </div>
                  </div>
                ) : (
                  <div className="p-2">
                    {activeTab === 'compatibili' ? (
                      compatibleBobine.length === 0 ? (
                        <div className="text-center py-8">
                          <div className="text-gray-500 text-sm mb-2">
                            Nessuna bobina compatibile trovata
                          </div>
                          <div className="text-xs text-gray-400">
                            Cercando bobine con tipologia <strong>{cavo?.tipologia}</strong> e formazione <strong>{cavo?.sezione}</strong>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {compatibleBobine.map((bobina) => {
                            // Estrai solo il numero finale dall'ID (es: da "C1_B2" -> "2")
                            const displayId = bobina.id_bobina.split('_B')[1] || bobina.id_bobina.split('_b')[1] || bobina.id_bobina

                            return (
                              <div
                                key={bobina.id_bobina}
                                onClick={() => {
                                  setSelectedBobina(bobina.id_bobina)
                                  // In modifica bobina, imposta automaticamente "cambia_bobina"
                                  if (mode === 'modifica_bobina') {
                                    setSelectedEditOption('cambia_bobina')
                                  }
                                }}
                                className={`p-3 rounded cursor-pointer transition-colors border-2 ${
                                  selectedBobina === bobina.id_bobina
                                    ? 'bg-blue-100 border-blue-500 shadow-md'
                                    : 'hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                                }`}
                              >
                                {/* Layout orizzontale migliorato */}
                                <div className="flex flex-wrap gap-6 text-sm">
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">ID:</span>
                                    <span className="text-gray-900 font-bold text-base">{displayId}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">TIPOLOGIA:</span>
                                    <span className="text-gray-900 font-semibold">{bobina.tipologia}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">FORMAZIONE:</span>
                                    <span className="text-gray-900 font-semibold">{bobina.sezione}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">RESIDUI:</span>
                                    <span className="text-gray-900 font-bold">{bobina.metri_residui}m</span>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      )
                    ) : (
                      incompatibleBobine.length === 0 ? (
                        <div className="text-center py-8">
                          <div className="text-gray-500 text-sm">
                            Nessuna bobina incompatibile trovata
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {incompatibleBobine.map((bobina) => {
                            // Estrai solo il numero finale dall'ID (es: da "C1_B2" -> "2")
                            const displayId = bobina.id_bobina.split('_B')[1] || bobina.id_bobina.split('_b')[1] || bobina.id_bobina

                            return (
                              <div
                                key={bobina.id_bobina}
                                onClick={() => {
                                  setSelectedBobina(bobina.id_bobina)
                                  // In modifica bobina, imposta automaticamente "cambia_bobina"
                                  if (mode === 'modifica_bobina') {
                                    setSelectedEditOption('cambia_bobina')
                                  }
                                }}
                                className={`p-3 rounded cursor-pointer transition-colors border-2 ${
                                  selectedBobina === bobina.id_bobina
                                    ? 'bg-blue-100 border-blue-500 shadow-md'
                                    : 'hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                                }`}
                              >
                                {/* Layout orizzontale migliorato */}
                                <div className="flex flex-wrap gap-6 text-sm">
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">ID:</span>
                                    <span className="text-gray-900 font-bold text-base">{displayId}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">TIPOLOGIA:</span>
                                    <span className="text-gray-900 font-semibold">{bobina.tipologia}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">FORMAZIONE:</span>
                                    <span className="text-gray-900 font-semibold">{bobina.sezione}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-500 font-medium">RESIDUI:</span>
                                    <span className="text-gray-900 font-bold">{bobina.metri_residui}m</span>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      )
                    )}

                    {/* Messaggio quando non ci sono bobine */}
                    {!isLoading && bobine.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <Package className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                        <p>Nessuna bobina disponibile</p>
                        <p className="text-sm">Verifica che ci siano bobine nel parco cavi</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
            className="flex-1 hover:bg-gray-50"
          >
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || !isFormValid}
            className={`flex-1 ${
              !isFormValid
                ? 'opacity-50 cursor-not-allowed'
                : mode === 'aggiungi_metri'
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                {modalConfig.primaryButtonText}
              </>
            )}
          </Button>
        </DialogFooter>
      </EnhancedDialogContent>
    </Dialog>
  )
}


// Export types
export type {
  ModificaBobinaModalProps,
  InserisciMetriModalProps,
  UnifiedCableBobbinModalProps,
  EditOption,
  Bobina
}