import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    const response = await fetch(`${backendUrl}/api/password/confirm-password-reset`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Passa gli headers necessari per il rate limiting
        'X-Forwarded-For': request.headers.get('x-forwarded-for') || 
                          request.headers.get('x-real-ip') || 
                          request.ip || 
                          'unknown',
        'User-Agent': request.headers.get('user-agent') || 'unknown'
      },
      body: JSON.stringify(body)
    })

    const data = await response.json()

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
