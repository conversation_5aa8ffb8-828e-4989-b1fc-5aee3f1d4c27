'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Cantiere } from '@/types'

interface UseCantiereResult {
  cantiereId: number | null
  cantiere: Cantiere | null
  isValidCantiere: boolean
  isLoading: boolean
  error: string | null
  validateCantiere: (id: number | string) => boolean
  clearError: () => void
}

/**
 * Hook personalizzato per la gestione robusta del cantiere selezionato
 * Gestisce validazione, errori e sincronizzazione con AuthContext
 */
export function useCantiere(): UseCantiereResult {
  const { cantiere, isLoading: authLoading } = useAuth()
  const [cantiereId, setCantiereId] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Funzione per validare un ID cantiere
  const validateCantiere = (id: number | string): boolean => {
    if (id === null || id === undefined) return false
    
    const numId = typeof id === 'string' ? parseInt(id, 10) : id
    
    if (isNaN(numId) || numId <= 0) {
      console.warn('🏗️ useCantiere: ID cantiere non valido:', id)
      return false
    }
    
    return true
  }

  // Effetto per sincronizzare con AuthContext e localStorage
  useEffect(() => {
    if (authLoading) {
      console.log('🏗️ useCantiere: Autenticazione in corso...')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      let selectedId: number | null = null

      // Priorità 1: Cantiere dal context di autenticazione (login cantiere diretto)
      if (cantiere?.id_cantiere && validateCantiere(cantiere.id_cantiere)) {
        selectedId = cantiere.id_cantiere
        console.log('🏗️ useCantiere: Usando cantiere dal context (login cantiere):', selectedId)
      } else {
        // Priorità 2: Cantiere dal localStorage (cantiere_data per login cantiere)
        const cantiereData = localStorage.getItem('cantiere_data')
        if (cantiereData) {
          try {
            const parsedData = JSON.parse(cantiereData)
            if (parsedData.id_cantiere && validateCantiere(parsedData.id_cantiere)) {
              selectedId = parsedData.id_cantiere
              console.log('🏗️ useCantiere: Usando cantiere da cantiere_data:', selectedId)
            }
          } catch (parseError) {
            console.warn('🏗️ useCantiere: Errore parsing cantiere_data:', parseError)
          }
        }

        // Priorità 3: Cantiere dal localStorage (selectedCantiereId per selezione manuale)
        if (!selectedId) {
          const storedId = localStorage.getItem('selectedCantiereId')
          if (storedId && validateCantiere(storedId)) {
            selectedId = parseInt(storedId, 10)
            console.log('🏗️ useCantiere: Usando cantiere da selectedCantiereId:', selectedId)
          }
        }
      }

      if (selectedId) {
        setCantiereId(selectedId)
        console.log('🏗️ useCantiere: Cantiere valido impostato:', selectedId)
      } else {
        console.warn('🏗️ useCantiere: Nessun cantiere valido trovato')
        setCantiereId(null)
        setError('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')
      }
    } catch (err) {
      console.error('🏗️ useCantiere: Errore nella gestione cantiere:', err)
      setError('Errore nella gestione del cantiere selezionato.')
      setCantiereId(null)
    } finally {
      setIsLoading(false)
    }
  }, [cantiere, authLoading])

  const clearError = () => setError(null)

  return {
    cantiereId,
    cantiere,
    isValidCantiere: cantiereId !== null && cantiereId > 0,
    isLoading,
    error,
    validateCantiere,
    clearError
  }
}

/**
 * Hook semplificato che restituisce solo l'ID del cantiere valido o null
 */
export function useCantiereId(): number | null {
  const { cantiereId } = useCantiere()
  return cantiereId
}

/**
 * Hook che forza la presenza di un cantiere valido
 * Lancia un errore se non c'è un cantiere selezionato
 */
export function useRequiredCantiere(): { cantiereId: number; cantiere: Cantiere | null } {
  const { cantiereId, cantiere, isLoading, error } = useCantiere()

  if (isLoading) {
    throw new Error('Caricamento cantiere in corso...')
  }

  if (error) {
    throw new Error(error)
  }

  if (!cantiereId || cantiereId <= 0) {
    throw new Error('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')
  }

  return { cantiereId, cantiere }
}
