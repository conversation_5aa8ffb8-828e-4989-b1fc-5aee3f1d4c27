'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Activity, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  BarChart3,
  Calendar,
  Zap
} from 'lucide-react'

export default function ProductivityPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('week')

  // Dati mock per la demo
  const productivityData = {
    totalCables: 1250,
    installedCables: 890,
    connectedCables: 650,
    certifiedCables: 420,
    activeTeams: 8,
    avgInstallationRate: 12.5, // cavi/ora
    avgConnectionRate: 8.3,    // cavi/ora
    avgCertificationRate: 6.1, // cavi/ora
    completionPercentage: 71.2,
    estimatedCompletion: '15 giorni'
  }

  const teamStats = [
    { name: 'Team Alpha', members: 4, installed: 156, connected: 98, certified: 67, efficiency: 92 },
    { name: 'Team Beta', members: 3, installed: 134, connected: 89, certified: 54, efficiency: 88 },
    { name: 'Team Gamma', members: 5, installed: 189, connected: 145, certified: 89, efficiency: 95 },
    { name: 'Team Delta', members: 4, installed: 167, connected: 123, certified: 78, efficiency: 90 }
  ]

  const recentActivities = [
    { time: '10:30', action: 'Installazione completata', details: 'Cavo FG16OM4-24 - Settore A', team: 'Alpha' },
    { time: '10:15', action: 'Collegamento certificato', details: 'Cavo MM-OM3-12 - Settore B', team: 'Beta' },
    { time: '09:45', action: 'Nuova installazione', details: 'Cavo SM-G652D-48 - Settore C', team: 'Gamma' },
    { time: '09:30', action: 'Test completato', details: 'Cavo FG16OM4-12 - Settore A', team: 'Delta' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">
        
        {/* Period selector */}
        <div className="flex justify-end mb-6">
          <div className="flex gap-2">
            {['day', 'week', 'month'].map((period) => (
              <Button
                key={period}
                variant={selectedPeriod === period ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(period)}
                className="capitalize"
              >
                {period === 'day' ? 'Oggi' : period === 'week' ? 'Settimana' : 'Mese'}
              </Button>
            ))}
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Avanzamento Totale</CardTitle>
              <Target className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{productivityData.completionPercentage}%</div>
              <Progress value={productivityData.completionPercentage} className="mt-2" />
              <p className="text-xs text-slate-500 mt-2">
                {productivityData.installedCables} di {productivityData.totalCables} cavi
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Velocità Installazione</CardTitle>
              <Zap className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{productivityData.avgInstallationRate}</div>
              <p className="text-xs text-slate-500">cavi/ora per persona</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-xs text-green-600">+12% vs settimana scorsa</span>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Team Attivi</CardTitle>
              <Users className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{productivityData.activeTeams}</div>
              <p className="text-xs text-slate-500">squadre operative</p>
              <div className="flex items-center mt-2">
                <Badge variant="secondary" className="text-xs">16 persone</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Completamento Stimato</CardTitle>
              <Calendar className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{productivityData.estimatedCompletion}</div>
              <p className="text-xs text-slate-500">al ritmo attuale</p>
              <div className="flex items-center mt-2">
                <Clock className="h-3 w-3 text-purple-500 mr-1" />
                <span className="text-xs text-purple-600">Aggiornato ora</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Team Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                Performance Team
              </CardTitle>
              <CardDescription>Statistiche dettagliate per squadra</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamStats.map((team, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-slate-900">{team.name}</h4>
                        <Badge variant={team.efficiency >= 90 ? 'default' : 'secondary'}>
                          {team.efficiency}% efficienza
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm text-slate-600">
                        <div>Installati: <span className="font-medium text-slate-900">{team.installed}</span></div>
                        <div>Collegati: <span className="font-medium text-slate-900">{team.connected}</span></div>
                        <div>Certificati: <span className="font-medium text-slate-900">{team.certified}</span></div>
                      </div>
                      <Progress value={team.efficiency} className="mt-2 h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-green-600" />
                Attività Recenti
              </CardTitle>
              <CardDescription>Ultime operazioni completate</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-slate-50 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-slate-900">{activity.action}</p>
                        <span className="text-xs text-slate-500">{activity.time}</span>
                      </div>
                      <p className="text-sm text-slate-600 truncate">{activity.details}</p>
                      <Badge variant="outline" className="mt-1 text-xs">
                        {activity.team}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    </div>
  )
}
