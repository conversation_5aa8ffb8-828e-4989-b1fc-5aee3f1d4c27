'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  DisconnectCableModal, 
  GeneratePdfModal, 
  CertificationErrorModal,
  CertificationModal,
  SuccessToast 
} from '../modals/CableActionModals'
import { ModificaBobinaModal, InserisciMetriModal } from '../modals/BobinaManagementModals'
import { Cavo } from '@/types'

// Mock cable data for demonstration
const mockCavo: Cavo = {
  id_cavo: 'C001',
  tipologia: 'Fibra Ottica',
  sezione: '12 fibre',
  stato: 'INSTALLATO',
  ubicazione_partenza: 'Quadro A',
  ubicazione_arrivo: 'Quadro B',
  metri_installati: 150,
  collegamenti: 3, // Both sides connected
  responsabile_partenza: 'Tecnico 1',
  responsabile_arrivo: 'Tecnico 2'
}

interface ModalState {
  disconnect: boolean
  generatePdf: boolean
  certificationError: boolean
  certification: boolean
  modificaBobina: boolean
  inserisciMetri: boolean
}

export default function EnhancedModalsExample() {
  const [modals, setModals] = useState<ModalState>({
    disconnect: false,
    generatePdf: false,
    certificationError: false,
    certification: false,
    modificaBobina: false,
    inserisciMetri: false
  })

  const [toast, setToast] = useState({ visible: false, message: '' })

  const openModal = (modalName: keyof ModalState) => {
    setModals(prev => ({ ...prev, [modalName]: true }))
  }

  const closeModal = (modalName: keyof ModalState) => {
    setModals(prev => ({ ...prev, [modalName]: false }))
  }

  const showToast = (message: string) => {
    setToast({ visible: true, message })
  }

  // Mock handlers
  const handleDisconnect = async (cavoId: string) => {
    console.log('Disconnecting cable:', cavoId)
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
    showToast(`Cavo ${cavoId} scollegato con successo`)
  }

  const handleGeneratePdf = async (cavoId: string, options: any) => {
    console.log('Generating PDF for cable:', cavoId, 'with options:', options)
    await new Promise(resolve => setTimeout(resolve, 3000)) // Simulate PDF generation
    showToast(`Certificato PDF generato per il cavo ${cavoId}`)
  }

  const handleCertify = async (cavoId: string, certificationData: any) => {
    console.log('Certifying cable:', cavoId, 'with data:', certificationData)
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate certification
    showToast(`Cavo ${cavoId} certificato con successo`)
  }

  const handleModificaBobina = async (cavoId: string, bobinaId: string, option: string) => {
    console.log('Modifying bobina for cable:', cavoId, 'bobina:', bobinaId, 'option:', option)

    let message = ''
    let simulationTime = 1500

    switch (option) {
      case 'cambia-bobina':
        message = `Bobina ${bobinaId} assegnata al cavo ${cavoId}`
        break
      case 'bobina-vuota':
        message = `Bobina rimossa dal cavo ${cavoId}`
        break
      case 'annulla-posa':
        message = `Installazione annullata per il cavo ${cavoId} - metri restituiti alla bobina`
        simulationTime = 2000 // Operazione più complessa
        break
      default:
        message = `Operazione completata per il cavo ${cavoId}`
    }

    await new Promise(resolve => setTimeout(resolve, simulationTime)) // Simulate modification
    showToast(message)
  }

  const handleInserisciMetri = async (cavoId: string, metriPosati: number) => {
    console.log('Inserting metri for cable:', cavoId, 'metri:', metriPosati)
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate insertion
    showToast(`Registrati ${metriPosati}m per il cavo ${cavoId}`)
  }

  return (
    <div className="p-8 space-y-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Enhanced Cable Management Modals
        </h1>
        <p className="text-gray-600 mb-8">
          Demonstration of the improved modal system for CABLYS cable management
        </p>

        {/* Cable Info Card */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Cable Information - {mockCavo.id_cavo}
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Type:</span>
              <p className="text-gray-600">{mockCavo.tipologia}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Section:</span>
              <p className="text-gray-600">{mockCavo.sezione}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Status:</span>
              <p className="text-gray-600">{mockCavo.stato}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Length:</span>
              <p className="text-gray-600">{mockCavo.metri_installati}m</p>
            </div>
          </div>
        </div>

        {/* Modal Trigger Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <Button
            onClick={() => openModal('disconnect')}
            variant="destructive"
            className="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <span className="text-lg">⚡</span>
            <span>Disconnect Cable</span>
          </Button>

          <Button
            onClick={() => openModal('generatePdf')}
            variant="default"
            className="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <span className="text-lg">📄</span>
            <span>Generate PDF</span>
          </Button>

          <Button
            onClick={() => openModal('certification')}
            variant="default"
            className="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <span className="text-lg">🔒</span>
            <span>Certify Cable</span>
          </Button>

          <Button
            onClick={() => openModal('certificationError')}
            variant="outline"
            className="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <span className="text-lg">❌</span>
            <span>Certification Error</span>
          </Button>

          <Button
            onClick={() => openModal('modificaBobina')}
            variant="default"
            className="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <span className="text-lg">📦</span>
            <span>Modify Bobina</span>
          </Button>

          <Button
            onClick={() => openModal('inserisciMetri')}
            variant="default"
            className="h-20 flex flex-col items-center justify-center space-y-2"
          >
            <span className="text-lg">📏</span>
            <span>Insert Meters</span>
          </Button>
        </div>

        {/* Features List */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">
            Enhanced Features Implemented
          </h3>
          <ul className="space-y-2 text-sm text-blue-800">
            <li>✅ Prominent cable ID display in modal headers</li>
            <li>✅ Enhanced overlay behavior (no outside click closing)</li>
            <li>✅ ESC key support for all modals</li>
            <li>✅ Loading states and button feedback</li>
            <li>✅ Form validation with real-time error messages</li>
            <li>✅ Accessibility compliance (ARIA labels, focus management)</li>
            <li>✅ Confirmation dialogs for destructive actions</li>
            <li>✅ Visual status indicators with tooltips</li>
            <li>✅ Searchable dropdowns and improved form controls</li>
            <li>✅ Success toast notifications</li>
          </ul>
        </div>
      </div>

      {/* All Modals */}
      <DisconnectCableModal
        open={modals.disconnect}
        onClose={() => closeModal('disconnect')}
        cavo={mockCavo}
        onConfirm={handleDisconnect}
      />

      <GeneratePdfModal
        open={modals.generatePdf}
        onClose={() => closeModal('generatePdf')}
        cavo={mockCavo}
        onGenerate={handleGeneratePdf}
      />

      <CertificationModal
        open={modals.certification}
        onClose={() => closeModal('certification')}
        cavo={mockCavo}
        onCertify={handleCertify}
      />

      <CertificationErrorModal
        open={modals.certificationError}
        onClose={() => closeModal('certificationError')}
        cavo={mockCavo}
        errorMessage="Il cavo non soddisfa i requisiti per la certificazione"
        missingRequirements={[
          'Il cavo deve essere completamente collegato',
          'Tutti i test di collaudo devono essere completati',
          'La documentazione tecnica deve essere presente'
        ]}
      />

      <ModificaBobinaModal
        open={modals.modificaBobina}
        onClose={() => closeModal('modificaBobina')}
        cavo={mockCavo}
        onSave={handleModificaBobina}
      />

      <InserisciMetriModal
        open={modals.inserisciMetri}
        onClose={() => closeModal('inserisciMetri')}
        cavo={mockCavo}
        onSave={handleInserisciMetri}
      />

      <SuccessToast
        visible={toast.visible}
        message={toast.message}
        onClose={() => setToast({ visible: false, message: '' })}
      />
    </div>
  )
}
